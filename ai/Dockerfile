# TractionX AI Agent Foundation Dockerfile

FROM python:3.12-slim

WORKDIR /app

# Build arguments for database connections
ARG MONGODB_URL
ARG MONGODB_DB_NAME
ARG QDRANT_URL
ARG REDIS_URL

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_NO_INTERACTION=1 \
    MONGODB_URL=${MONGODB_URL} \
    MONGODB_DB_NAME=${MONGODB_DB_NAME} \
    QDRANT_URL=${QDRANT_URL} \
    REDIS_URL=${REDIS_URL}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry - use pip for a more reliable installation
RUN pip install --no-cache-dir "poetry==$POETRY_VERSION" && \
    poetry --version && \
    poetry config virtualenvs.create false

# Copy pyproject.toml and poetry.lock first for better layer caching
COPY pyproject.toml poetry.lock* ./

# Install dependencies
RUN pip install --upgrade pip && \
    pip install uvicorn && \
    poetry install --no-interaction --no-ansi --no-root

# Copy application code
COPY . .

# Create non-root user and fix permissions
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app

USER appuser

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Set Python path and ensure packages are in PATH
ENV PYTHONPATH=/app
# Run application
CMD ["uvicorn", "ai.main:app", "--host", "0.0.0.0", "--port", "8002", "--reload"]
