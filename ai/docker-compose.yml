services:
  ai-agents:
    build: 
      context: .
      args:
        - MONGODB_URL=${MONGODB_URL:-mongodb+srv://prasanna:<EMAIL>}
        - MONGODB_DB_NAME=${MONGODB_DB_NAME:-tx_ai}
        - QDRANT_URL=${QDRANT_URL:-https://b423b15b-97b6-4fea-a932-592403dd8b92.us-west-1-0.aws.cloud.qdrant.io}
        - REDIS_URL=${REDIS_URL:-redis://default:<EMAIL>:14410}
    ports:
      - "8002:8002"
    # This configuration is optimized for development with hot reloading
    env_file:
      - .env
    volumes:
      - .:/app:delegated  # Mount entire project directory for development
    networks:
      - ai-network

networks:
  ai-network:
    driver: bridge
