const { fontFamily } = require("tailwindcss/defaultTheme")

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./ui/**/*.{ts,tsx}",
    "./content/**/*.{md,mdx}",
  ],
  darkMode: false,
  theme: {
    // PRD-compliant breakpoint system
    screens: {
      'xs': '480px',   // Mobile small
      'sm': '640px',   // Mobile large
      'md': '1024px',  // Tablet
      'lg': '1440px',  // Desktop
      'xl': '1920px',  // Large desktop
    },
    container: {
      center: true,
      padding: {
        DEFAULT: "1rem",
        xs: "1rem",
        sm: "1.5rem",
        md: "2rem",
        lg: "2.5rem",
        xl: "3rem",
      },
      screens: {
        xs: "480px",
        sm: "640px",
        md: "1024px",
        lg: "1440px",
        xl: "1920px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "24px", // Premium card radius matching deal/[id] design
        md: "16px", // Medium radius for smaller cards
        sm: "8px",  // Small radius for buttons/badges
        xl: "24px", // Extra large for main cards
        "2xl": "32px", // For hero sections
      },
      gridTemplateColumns: {
        '13': 'repeat(13, minmax(0, 1fr))'
      },
      fontFamily: {
        sans: ["Space Grotesk", "var(--font-sans)", ...fontFamily.sans],
        heading: ["Space Grotesk", "var(--font-heading)", ...fontFamily.sans],
      },
      fontSize: {
        // Mobile-first typography system - minimum 15px for mobile readability
        'xs': ['13px', { lineHeight: '1.4' }],   // Small text, labels
        'sm': ['15px', { lineHeight: '1.5' }],   // Body text mobile minimum
        'base': ['16px', { lineHeight: '1.6' }], // Primary body text
        'lg': ['18px', { lineHeight: '1.5' }],   // Large body text
        'xl': ['20px', { lineHeight: '1.4' }],   // Subheadings
        '2xl': ['24px', { lineHeight: '1.3' }],  // Headings
        '3xl': ['28px', { lineHeight: '1.2' }],  // Large headings
        '4xl': ['32px', { lineHeight: '1.1' }],  // Hero text
        '5xl': ['36px', { lineHeight: '1.1' }],  // Display text
      },
      spacing: {
        // Mobile-first spacing system
        '18': '4.5rem',   // 72px - section spacing
        '20': '5rem',     // 80px - large section spacing
        '24': '6rem',     // 96px - extra large spacing
        '28': '7rem',     // 112px - hero spacing
        '32': '8rem',     // 128px - maximum spacing
        // Touch-friendly spacing
        'touch': '44px',  // Minimum touch target (Apple HIG)
        'touch-lg': '48px', // Large touch target
      },
      // Mobile-first sizing utilities
      minHeight: {
        'touch': '44px',
        'touch-lg': '48px',
        'screen-mobile': '100dvh', // Dynamic viewport height for mobile
      },
      minWidth: {
        'touch': '44px',
        'touch-lg': '48px',
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
}
