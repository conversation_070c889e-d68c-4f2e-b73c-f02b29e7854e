"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Plus, Co<PERSON>, Check, ExternalLink } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import OnboardingAPI, { CreateInviteCodeResponse } from "@/lib/api/onboarding-api"

export default function TestOnboardingPage() {
  const [email, setEmail] = useState("")
  const [orgName, setOrgName] = useState("")
  const [isCreating, setIsCreating] = useState(false)
  const [inviteCode, setInviteCode] = useState<CreateInviteCodeResponse | null>(null)
  const [copiedCode, setCopiedCode] = useState(false)

  const handleCreateInviteCode = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      toast.error("Please enter an email address")
      return
    }

    try {
      setIsCreating(true)
      
      const response = await OnboardingAPI.createInviteCode({
        email: email.trim(),
        org_name: orgName.trim() || undefined,
      })

      setInviteCode(response)
      toast.success("Invite code created successfully!")
    } catch (error: any) {
      console.error("Create invite code error:", error)
      toast.error(error.message || "Failed to create invite code")
    } finally {
      setIsCreating(false)
    }
  }

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCode(true)
      toast.success("Code copied to clipboard!")
      
      setTimeout(() => setCopiedCode(false), 2000)
    } catch (error) {
      toast.error("Failed to copy code")
    }
  }

  const handleCopyUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      toast.success("URL copied to clipboard!")
    } catch (error) {
      toast.error("Failed to copy URL")
    }
  }

  const handleTestOnboarding = () => {
    if (inviteCode) {
      window.open(`/onboard/${inviteCode.code}`, '_blank')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <div className="container max-w-4xl mx-auto py-12 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">
            🚀 TractionX Onboarding Test
          </h1>
          <p className="text-xl text-muted-foreground">
            Test the invite code generation and onboarding flow
          </p>
        </div>

        {/* Create Invite Code Form */}
        <Card className="border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-2xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Create Test Invite Code
            </CardTitle>
            <CardDescription>
              Generate a unique invite code to test the onboarding flow.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateInviteCode} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-12 rounded-xl border-2 focus:border-primary transition-colors"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="org-name">Organization Name (Optional)</Label>
                  <Input
                    id="org-name"
                    type="text"
                    placeholder="Alpha Ventures"
                    value={orgName}
                    onChange={(e) => setOrgName(e.target.value)}
                    className="h-12 rounded-xl border-2 focus:border-primary transition-colors"
                  />
                </div>
              </div>
              <Button 
                type="submit" 
                disabled={isCreating} 
                className="w-full h-12 rounded-xl font-semibold"
              >
                {isCreating ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"
                  />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                {isCreating ? "Creating..." : "Create Invite Code"}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Generated Invite Code */}
        {inviteCode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-2xl">
              <CardHeader>
                <CardTitle className="text-green-600 dark:text-green-400">
                  ✅ Invite Code Generated!
                </CardTitle>
                <CardDescription>
                  Your invite code is ready. Use it to test the onboarding flow.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Invite Code Display */}
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-xl">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Invite Code</Label>
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="font-mono text-lg px-4 py-2">
                        {inviteCode.code}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyCode(inviteCode.code)}
                        className="h-8 w-8 p-0"
                      >
                        {copiedCode ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-muted-foreground">Email</Label>
                    <p className="font-medium">{inviteCode.email}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Expires</Label>
                    <p className="font-medium">
                      {new Date(inviteCode.expires_at * 1000).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <Separator />

                {/* Actions */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={handleTestOnboarding}
                    className="flex-1 h-12 rounded-xl font-semibold"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Test Onboarding Flow
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleCopyUrl(inviteCode.onboard_url)}
                    className="h-12 px-6 rounded-xl"
                  >
                    Copy URL
                  </Button>
                </div>

                {/* URL Display */}
                <div className="p-3 bg-muted/30 rounded-lg">
                  <Label className="text-xs text-muted-foreground">Onboarding URL</Label>
                  <p className="text-sm font-mono break-all">{inviteCode.onboard_url}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Instructions */}
        <Card className="border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-2xl">
          <CardHeader>
            <CardTitle>🧪 Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Step 1: Create Invite Code</h4>
              <p className="text-sm text-muted-foreground">
                Fill in the form above with a test email and optional organization name.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Step 2: Test Onboarding</h4>
              <p className="text-sm text-muted-foreground">
                Click "Test Onboarding Flow" to open the onboarding wizard in a new tab.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Step 3: Complete Setup</h4>
              <p className="text-sm text-muted-foreground">
                Follow the 3-step wizard: Organization setup → Profile creation → Welcome screen.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
