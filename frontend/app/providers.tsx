"use client"

import React from 'react'
import { AuthProvider } from '@/lib/auth-context'
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context'
import { ThemeProvider } from '@/components/theme-provider'
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

interface ProvidersProps {
  children: React.ReactNode
}

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      refetchOnWindowFocus: false,
    },
  },
})

export function Providers({ children }: ProvidersProps) {
  return (
    <QueryClientProvider client={queryClient}>
    <ThemeProvider
      attribute="class"
        defaultTheme="system"
        enableSystem
    >
      <AuthProvider>
        <PublicAuthProvider>
          {children}
        </PublicAuthProvider>
      </AuthProvider>
        {/* <Toaster /> */}
    </ThemeProvider>
    </QueryClientProvider>
  )
}
