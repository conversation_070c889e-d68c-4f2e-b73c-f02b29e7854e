"use client"

import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Icons } from '@/components/icons';
import { useAuth } from '@/lib/auth-context';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';
import { SharedFormsAPI } from '@/lib/api/shared-forms';
import { PublicSubmissionAPI } from '@/lib/api/public-submission';

export default function MagicLinkVerifyPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { loginWithTokens } = useAuth();
  const { loginWithTokens: publicLoginWithTokens } = usePublicAuth();

  const token = params?.token as string;
  const redirectUrl = searchParams?.get('redirect');

  const [status, setStatus] = useState<'verifying' | 'success' | 'error'>('verifying');
  const [error, setError] = useState<string>('');
  const [finalRedirectUrl, setFinalRedirectUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setError('Invalid verification link');
      return;
    }

    const verifyToken = async () => {
      try {
        // Try public user verification first
        try {
          const publicResponse = await PublicSubmissionAPI.verifyMagicLink(token);

          // This is a public user
          publicLoginWithTokens(
            publicResponse.access_token,
            publicResponse.refresh_token,
            publicResponse.user,
            publicResponse.submission ? {
              ...publicResponse.submission,
              status: publicResponse.submission.status as 'draft' | 'submitted',
              answers: publicResponse.submission.answers || {},
            } : undefined
          );

          setStatus('success');

          // Use redirect URL from query param or API response
          const redirectTarget = redirectUrl || publicResponse.redirect_url;
          setFinalRedirectUrl(redirectTarget || null);

          // Redirect after a short delay
          setTimeout(() => {
            if (redirectTarget) {
              window.location.href = redirectTarget;
            } else {
              // Default redirect for public users should be to the shared form
              router.push('/dashboard');
            }
          }, 2000);

          return;
        } catch (publicError) {
          // If public verification fails, try regular user verification
          console.log('Public verification failed, trying regular user verification');
        }

        // Try regular user verification
        const response = await SharedFormsAPI.verifyMagicLink(token);

        // Log the regular user in
        loginWithTokens(response.access_token, response.refresh_token, response.user, response.org_id);

        setStatus('success');

        // Redirect after a short delay
        setTimeout(() => {
          if (redirectUrl) {
            window.location.href = redirectUrl;
          } else {
            router.push('/dashboard');
          }
        }, 2000);

      } catch (err) {
        setStatus('error');
        setError(err instanceof Error ? err.message : 'Verification failed');
      }
    };

    verifyToken();
  }, [token, loginWithTokens, publicLoginWithTokens, router, redirectUrl]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-md w-full"
      >
        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
          <CardContent className="p-8 text-center">
            {status === 'verifying' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Icons.spinner className="h-12 w-12 animate-spin mx-auto mb-6 text-blue-600" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Verifying...
                </h1>
                <p className="text-gray-600">
                  Please wait while we verify your magic link.
                </p>
              </motion.div>
            )}

            {status === 'success' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                  <Icons.check className="h-8 w-8 text-green-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Welcome Back!
                </h1>
                <p className="text-gray-600 mb-4">
                  You've been successfully logged in.
                </p>
                {finalRedirectUrl && (
                  <p className="text-sm text-gray-500">
                    Redirecting you back to your form...
                  </p>
                )}
              </motion.div>
            )}

            {status === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                  <Icons.alertCircle className="h-8 w-8 text-red-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Verification Failed
                </h1>
                <p className="text-gray-600 mb-4">
                  {error}
                </p>
                <p className="text-sm text-gray-500">
                  Please request a new magic link or contact support.
                </p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
