"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"
import { Star, AlertCircle } from "lucide-react"

import { SettingsLayout } from "@/components/core/settings/settings-layout"
import { ProfileForm } from "@/components/core/settings/profile-form"
import { ChangePasswordModal } from "@/components/core/settings/change-password-modal"
import { SettingsAPI, UserProfile } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"

export default function ProfileSettingsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showPasswordModal, setShowPasswordModal] = useState(false)

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setIsLoading(true)
      const profileData = await SettingsAPI.getProfile()
      setProfile(profileData)
    } catch (error: any) {
      console.error("Failed to load profile:", error)
      toast.error("Failed to load profile")
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdate = async (data: { name?: string; profile_picture?: string }) => {
    try {
      await SettingsAPI.updateProfile(data)
      toast.success("Profile updated successfully!")
      
      // Reload profile to get updated data
      await loadProfile()
    } catch (error: any) {
      console.error("Failed to update profile:", error)
      toast.error(error.message || "Failed to update profile")
    }
  }

  const handlePasswordChange = async (data: {
    current_password: string
    new_password: string
    confirm_password: string
  }) => {
    try {
      await SettingsAPI.changePassword(data)
      toast.success("Password changed successfully!")
      setShowPasswordModal(false)
    } catch (error: any) {
      console.error("Failed to change password:", error)
      toast.error(error.message || "Failed to change password")
      throw error // Re-throw to keep modal open
    }
  }

  if (isLoading) {
    return (
      <SettingsLayout activeTab="profile">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={mobileRetreat.loading.container}
        >
          <div className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}>
            <div className="relative mb-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-12 h-12 mx-auto border-3 border-blue-200 border-t-blue-600 rounded-full"
              />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <Star className="w-6 h-6 text-blue-600" />
              </motion.div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Profile</h3>
            <p className="text-sm text-gray-600">Preparing your settings...</p>
          </div>
        </motion.div>
      </SettingsLayout>
    )
  }

  if (!profile) {
    return (
      <SettingsLayout activeTab="profile">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={mobileRetreat.error.container}
        >
          <div className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}>
            <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Profile Load Error</h3>
            <p className="text-gray-600 mb-6">Unable to load your profile settings.</p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors touch-target"
            >
              Try Again
            </motion.button>
          </div>
        </motion.div>
      </SettingsLayout>
    )
  }

  return (
    <SettingsLayout activeTab="profile">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="space-y-8"
      >
        <div className="max-w-4xl mx-auto">
          <ProfileForm
            profile={profile}
            onUpdate={handleProfileUpdate}
            onChangePassword={() => setShowPasswordModal(true)}
          />
        </div>
      </motion.div>

      <ChangePasswordModal
        open={showPasswordModal}
        onOpenChange={setShowPasswordModal}
        onSubmit={handlePasswordChange}
      />
    </SettingsLayout>
  )
}
