"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Plus, Copy, Mail, Calendar, Check } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PeerInviteForm } from "@/components/core/onboarding/peer-invite-form"
import OnboardingAPI, { CreateInviteCodeResponse } from "@/lib/api/onboarding-api"

export default function InviteCodesPage() {
  const [email, setEmail] = useState("")
  const [orgName, setOrgName] = useState("")
  const [isCreating, setIsCreating] = useState(false)
  const [inviteCodes, setInviteCodes] = useState<CreateInviteCodeResponse[]>([])
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const handleCreateInviteCode = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      toast.error("Please enter an email address")
      return
    }

    try {
      setIsCreating(true)
      
      const response = await OnboardingAPI.createInviteCode({
        email: email.trim(),
        org_name: orgName.trim() || undefined,
      })

      setInviteCodes(prev => [response, ...prev])
      setEmail("")
      setOrgName("")
      
      toast.success("Invite code created successfully!")
    } catch (error: any) {
      console.error("Create invite code error:", error)
      toast.error(error.message || "Failed to create invite code")
    } finally {
      setIsCreating(false)
    }
  }

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCode(code)
      toast.success("Code copied to clipboard!")
      
      setTimeout(() => setCopiedCode(null), 2000)
    } catch (error) {
      toast.error("Failed to copy code")
    }
  }

  const handleCopyUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      toast.success("URL copied to clipboard!")
    } catch (error) {
      toast.error("Failed to copy URL")
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="container max-w-4xl mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Team & Invitations</h1>
        <p className="text-muted-foreground">
          Manage team invitations and organization onboarding.
        </p>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="team-invites" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="team-invites">Team Invites</TabsTrigger>
          <TabsTrigger value="org-codes">Organization Codes</TabsTrigger>
        </TabsList>

        <TabsContent value="team-invites" className="space-y-6">
          <PeerInviteForm />
        </TabsContent>

        <TabsContent value="org-codes" className="space-y-6">

      {/* Create Invite Code Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New Invite Code
          </CardTitle>
          <CardDescription>
            Generate a unique invite code for organization onboarding.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleCreateInviteCode} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="org-name">Organization Name (Optional)</Label>
                <Input
                  id="org-name"
                  type="text"
                  placeholder="Alpha Ventures"
                  value={orgName}
                  onChange={(e) => setOrgName(e.target.value)}
                />
              </div>
            </div>
            <Button type="submit" disabled={isCreating} className="w-full md:w-auto">
              {isCreating ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"
                />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              {isCreating ? "Creating..." : "Create Invite Code"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Invite Codes List */}
      {inviteCodes.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Recent Invite Codes</h2>
          <div className="grid gap-4">
            {inviteCodes.map((invite, index) => (
              <motion.div
                key={invite.code}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-3 flex-1">
                        <div className="flex items-center gap-3">
                          <Badge variant="secondary" className="font-mono text-lg px-3 py-1">
                            {invite.code}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyCode(invite.code)}
                            className="h-8 w-8 p-0"
                          >
                            {copiedCode === invite.code ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Mail className="h-4 w-4" />
                          {invite.email}
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          Expires: {formatDate(invite.expires_at)}
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyUrl(invite.onboard_url)}
                        >
                          Copy URL
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {inviteCodes.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="space-y-4">
              <div className="h-16 w-16 bg-muted rounded-full mx-auto flex items-center justify-center">
                <Mail className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">No invite codes yet</h3>
                <p className="text-muted-foreground">
                  Create your first invite code to get started with onboarding new organizations.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
