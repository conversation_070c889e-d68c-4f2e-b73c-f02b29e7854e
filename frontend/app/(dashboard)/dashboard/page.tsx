"use client"

import { useState } from "react"
import { DashboardHeader } from "@/components/header"
import { DashboardShell } from "@/components/shell"
import {
  SummaryTiles,
  ChartsSection,
  ActivityFeed,
  ShareThesisBlock,
  OnboardingFlow,
  QuickActions
} from "@/components/core/dashboard"
import { useDashboard } from "@/lib/hooks/use-dashboard"
import { useAuth } from "@/lib/auth-context"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

// Note: All authentication logic is preserved in the layout and auth context
// This component only handles UI rendering - no auth functions are removed

export default function DashboardPage() {
  const { user } = useAuth()
  const { data: dashboardData, loading, error, refetch } = useDashboard()
  const [showOnboarding, setShowOnboarding] = useState(false)

  // Determine if we should show onboarding
  const shouldShowOnboarding = dashboardData &&
    (!dashboardData.onboarding.has_form || !dashboardData.onboarding.has_thesis)

  // Loading state
  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="AI-powered investment intelligence at your fingertips"
          showOrgName={true}
        />
        <div className="space-y-6 md:space-y-8">
          {/* Mobile-first skeleton grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32 rounded-xl" />
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            <Skeleton className="h-80 md:h-96 rounded-xl" />
            <Skeleton className="h-80 md:h-96 rounded-xl" />
          </div>
        </div>
      </DashboardShell>
    )
  }

  // Error state
  if (error) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="AI-powered investment intelligence at your fingertips"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </DashboardShell>
    )
  }

  // No data state
  if (!dashboardData) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="AI-powered investment intelligence at your fingertips"
        />
        <div className="text-center py-12">
          <p className="text-muted-foreground">No dashboard data available.</p>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Dashboard"
        text="AI-powered investment intelligence at your fingertips"
        showOrgName={true}
      />

      <div className="space-y-6 md:space-y-8">
        {/* Show onboarding for incomplete setup */}
        {shouldShowOnboarding && (
          <OnboardingFlow
            userName={user?.name}
            onboardingStatus={dashboardData.onboarding}
            onDismiss={() => setShowOnboarding(false)}
            onRefresh={refetch}
          />
        )}

        {/* Summary Tiles - Mobile-first grid */}
        <SummaryTiles dashboardData={dashboardData} />

        {/* Charts Section - Mobile-first responsive */}
        <ChartsSection dashboardData={dashboardData} />

        {/* Activity Feed and Share Block - Mobile-first layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
          <div className="lg:col-span-2 order-2 lg:order-1">
            <ActivityFeed dashboardData={dashboardData} />
          </div>
          <div className="lg:col-span-1 order-1 lg:order-2">
            <ShareThesisBlock dashboardData={dashboardData} />
          </div>
        </div>

        {/* Quick Actions - Mobile-first responsive */}
        <QuickActions dashboardData={dashboardData} />
      </div>
    </DashboardShell>
  )
}
