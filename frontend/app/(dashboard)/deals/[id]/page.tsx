"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { DealDetailData } from "@/lib/types/deal-detail"
import { useAuth } from "@/lib/auth-context"
import { DealHeader } from "@/components/core/deals/deal-detail/deal-header"
import { DealTabs } from "@/components/core/deals/deal-detail/deal-tabs"
import { OrbitAI } from "@/components/core/orbit-ai/orbit-ai"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive-safe"

import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Star } from "lucide-react"

export default function DealDetailPage() {
  const params = useParams()
  const dealId = params?.id as string
  const { isAuthenticated } = useAuth()

  const [dealData, setDealData] = useState<DealDetailData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('score') // Start with score tab as per PRD

  // Validate deal ID
  if (!dealId || dealId === 'undefined') {
    console.error('Invalid deal ID:', dealId);
  }

  useEffect(() => {
    if (!isAuthenticated || !dealId || dealId === 'undefined') {
      if (dealId === 'undefined') {
        setError('Invalid deal ID. Please check the URL and try again.');
        setLoading(false);
      }
      return;
    }

    const fetchDealDetail = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('Fetching deal detail for ID:', dealId);
        const data = await DealDetailAPI.getDealDetail(dealId)
        setDealData(data)
      } catch (err: any) {
        console.error('Error fetching deal detail:', err)
        setError('Failed to load deal details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchDealDetail()
  }, [isAuthenticated, dealId])

  // Premium loading state with sophisticated animations
  if (loading) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-gray-50/50")}>
        {/* Premium loading header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white border-b border-gray-200/60"
        >
          <div className="px-6 py-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div className="space-y-3">
                <Skeleton className="h-8 w-64 rounded-xl bg-gradient-to-r from-gray-200 to-gray-100" />
                <Skeleton className="h-4 w-40 rounded-lg bg-gray-100" />
              </div>
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-32 rounded-xl bg-gradient-to-r from-blue-100 to-blue-50" />
                <Skeleton className="h-10 w-24 rounded-xl bg-gray-100" />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Premium content skeleton */}
        <div className="px-8 py-8 space-y-8">
          {/* Elegant tabs skeleton */}
          <div className="w-full border-b border-gray-200/60 bg-white">
            <div className="w-full overflow-x-auto scrollbar-hide">
              <div className="w-full min-w-max flex h-auto p-0 bg-transparent border-0 rounded-none">
                {Array.from({ length: 6 }).map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.1 + i * 0.05 }}
                    className="flex items-center justify-center gap-3 py-4 px-6 min-w-[140px] flex-shrink-0"
                  >
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-4 w-16 rounded" />
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Premium content cards skeleton */}
          <div className="px-8 py-8 space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + i * 0.1 }}
                className="bg-white rounded-lg border border-gray-200 p-8 space-y-4"
              >
                <Skeleton className="h-6 w-48 rounded-lg bg-gradient-to-r from-gray-200 to-gray-100" />
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full rounded-lg bg-gray-100" />
                  <Skeleton className="h-4 w-3/4 rounded-lg bg-gray-100" />
                  <Skeleton className="h-4 w-1/2 rounded-lg bg-gray-100" />
                </div>
              </motion.div>
            ))}
          </div>

          {/* Loading indicator with premium styling */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="flex items-center justify-center py-12"
          >
            <div className="flex items-center gap-3 px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-200/50">
              <div className="relative">
                <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                <Star className="absolute inset-0 w-5 h-5 text-blue-600/20 animate-pulse" />
              </div>
              <span className="text-sm font-medium text-gray-700">Loading deal intelligence...</span>
            </div>
          </motion.div>
        </div>

        {/* Orbit AI - Always visible */}
        <OrbitAI dealId={dealId} dealContext={null} />
      </div>
    )
  }

  // Premium error state
  if (error) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-gray-50/50")}>
        <div className={mobileRetreat.error.container}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}
          >
            <div className={mobileRetreat.error.icon}>
              <AlertCircle className="w-12 h-12 text-red-500" />
            </div>
            <h3 className={mobileRetreat.error.title}>Unable to Load Deal</h3>
            <p className={mobileRetreat.error.description}>{error}</p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => window.location.reload()}
              className="mt-6 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors touch-target"
            >
              Try Again
            </motion.button>
          </motion.div>
        </div>
        <OrbitAI dealId={dealId} dealContext={null} />
      </div>
    )
  }

  // Premium no data state
  if (!dealData) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-gray-50/50")}>
        <div className={mobileRetreat.empty.container}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}
          >
            <div className={mobileRetreat.empty.icon}>
              <AlertCircle className="w-16 h-16 text-gray-400" />
            </div>
            <h3 className={mobileRetreat.empty.title}>Deal Not Found</h3>
            <p className={mobileRetreat.empty.description}>
              This deal may have been moved or deleted.
            </p>
            <motion.a
              href="/deals"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="mt-6 inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors touch-target"
            >
              Back to Deals
            </motion.a>
          </motion.div>
        </div>
        <OrbitAI dealId={dealId} dealContext={null} />
      </div>
    )
  }

  // Full-screen premium interface
  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Full-width Deal Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white border-b border-gray-200/60"
      >
        <div className="px-6 py-6 lg:px-8">
          <DealHeader deal={dealData} />
        </div>
      </motion.div>

      {/* Full-screen tabs interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex-1"
      >
        <DealTabs
          deal={dealData}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </motion.div>

      {/* Orbit AI */}
      <OrbitAI
        dealId={dealId}
        dealContext={dealData}
      />
    </div>
  )
}
