"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"
import { CheckCircle, AlertCircle, Star, ArrowRight } from "lucide-react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { OnboardingWizard } from "@/components/core/onboarding/onboarding-wizard"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import OnboardingAPI, { ValidateInviteCodeResponse } from "@/lib/api/onboarding-api"

export default function OnboardPage() {
  const params = useParams()
  const router = useRouter()
  const code = params?.code as string

  const [inviteData, setInviteData] = useState<ValidateInviteCodeResponse | null>(null)
  const [isValidating, setIsValidating] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!code) {
      setError("Invalid invite code")
      setIsValidating(false)
      return
    }

    validateInviteCode()
  }, [code])

  const validateInviteCode = async () => {
    try {
      setIsValidating(true)
      setError(null)

      const response = await OnboardingAPI.validateInviteCode(code)
      
      if (response.valid) {
        setInviteData(response)
      } else {
        setError("Invalid or expired invite code")
      }
    } catch (err: any) {
      console.error("Invite code validation error:", err)
      setError(err.message || "Failed to validate invite code")
    } finally {
      setIsValidating(false)
    }
  }

  const handleOnboardingComplete = async (result: any) => {
    console.log("Onboarding completed:", result)

    try {
      // Setup organization with demo data (form, thesis, deals)
      toast.loading("Setting up your workspace with demo data...", {
        id: "setup-org",
      })

      await OnboardingAPI.setupOrganization()

      toast.success("Welcome to TractionX! 🎉", {
        description: "Your organization has been set up with demo data. Explore the platform to see how it works!",
        duration: 5000,
        id: "setup-org",
      })

      // Redirect to dashboard for premium experience
      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    } catch (error: any) {
      console.error("Setup organization error:", error)
      
      // Still show success but with a note about demo data
      toast.success("Welcome to TractionX! 🎉", {
        description: "Your organization has been set up successfully. You can create forms and theses to get started.",
        duration: 5000,
        id: "setup-org",
      })

      // Redirect to dashboard
      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    }
  }

  if (isValidating) {
    return (
      <AuthLayout
        title="Validating Your Invitation"
        subtitle="Preparing your premium TractionX experience..."
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(mobileRetreat.loading.container, "py-16")}
        >
          <div className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}>
            <div className="relative mb-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-12 h-12 mx-auto border-3 border-blue-200 border-t-blue-600 rounded-full"
              />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <Star className="w-6 h-6 text-blue-600" />
              </motion.div>
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Validating Invitation
            </h3>
            <p className="text-sm text-gray-600">
              Setting up your premium workspace...
            </p>

            <div className="mt-6 flex justify-center">
              <div className="flex space-x-1">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut"
                    }}
                    className="w-2 h-2 bg-blue-600 rounded-full"
                  />
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </AuthLayout>
    )
  }

  if (error || !inviteData) {
    return (
      <AuthLayout
        title="Invitation Issue"
        subtitle="We encountered an issue with your invitation"
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={mobileRetreat.error.container}
        >
          <div className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}>
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Invalid Invitation
              </h3>
              <p className="text-gray-600 mb-6">
                {error || "This invitation code is not valid or has expired. Please contact your administrator for a new invitation."}
              </p>
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => router.push("/login")}
                className={cn(
                  visualRetreat.form.button,
                  "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg shadow-blue-500/25"
                )}
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Go to Login
              </Button>

              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className={cn(visualRetreat.form.button, "border-gray-200 hover:bg-gray-50")}
              >
                Try Again
              </Button>
            </div>
          </div>
        </motion.div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout
      title="Welcome to TractionX"
      subtitle="Let's set up your premium investment intelligence platform"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <OnboardingWizard
          inviteCode={code}
          inviteData={inviteData}
          onComplete={handleOnboardingComplete}
        />
      </motion.div>
    </AuthLayout>
  )
}
