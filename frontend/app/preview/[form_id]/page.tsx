"use client"

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ReadOnlyFormRenderer } from '@/components/core/form-preview/read-only-form-renderer';
import { useAuth } from '@/lib/auth-context';
import FormAPI from '@/lib/api/form-api';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function FormPreviewPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [formData, setFormData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formId = params?.form_id as string;

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    const fetchForm = async () => {
      if (!formId || !isAuthenticated) {
        return;
      }

      try {
        console.log(`Fetching form preview for ID: ${formId}`);
        setLoading(true);

        // Use the new preview endpoint that requires authentication
        const previewData = await FormAPI.getFormPreview(formId);
        console.log('Form preview data fetched:', previewData);

        setFormData(previewData);
        setError(null);
      } catch (error: any) {
        console.error('Error fetching form preview:', error);

        if (error.response?.status === 403) {
          setError('You do not have access to preview this form.');
        } else if (error.response?.status === 404) {
          setError('This form is not available for preview.');
        } else {
          setError('Failed to load form preview. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    };

    if (formId && isAuthenticated && !authLoading) {
      fetchForm();
    }
  }, [formId, isAuthenticated, authLoading]);

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="flex flex-col items-center gap-6">
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl animate-pulse"></div>
            <Loader2 className="h-8 w-8 animate-spin text-white absolute inset-0 m-auto" />
          </div>
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
              Loading Form Preview
            </h2>
            <p className="text-slate-600 animate-pulse">Preparing read-only preview...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !formData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Form preview not available'}
            </AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <button
              onClick={() => router.back()}
              className="text-primary hover:text-primary/80 font-medium"
            >
              ← Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the read-only form preview
  return <ReadOnlyFormRenderer formData={formData} />;
}
