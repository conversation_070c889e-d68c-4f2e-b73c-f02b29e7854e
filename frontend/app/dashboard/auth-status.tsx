"use client"

import { useAuth } from "@/lib/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { OrganizationSelector } from "@/components/org-selector"

export function AuthStatus() {
  const { user, orgId, logout } = useAuth()
  
  // Get token from localStorage
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null
  const tokenPreview = token ? `${token.substring(0, 15)}...` : 'No token found'
  
  return (
    <Card className="w-full max-w-3xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Authentication Status</CardTitle>
        <CardDescription>
          Your current authentication details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium">User</h3>
              <p className="text-sm">{user?.name || 'Not logged in'}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Email</h3>
              <p className="text-sm">{user?.email || 'Not available'}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium">Organization ID</h3>
            <p className="text-sm font-mono bg-muted p-2 rounded">{orgId || 'Not available'}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium">Token Preview</h3>
            <p className="text-sm font-mono bg-muted p-2 rounded overflow-x-auto">{tokenPreview}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium">Organization Selector</h3>
            <div className="mt-2">
              <OrganizationSelector />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={() => {
            // Refresh the page
            window.location.reload()
          }}
        >
          Refresh
        </Button>
        <Button 
          variant="destructive" 
          onClick={() => {
            logout()
          }}
        >
          Logout
        </Button>
      </CardFooter>
    </Card>
  )
}
