"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import MobileTable from "@/components/ui/mobile-table"
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  CreditCard,
  Smartphone,
  Tablet,
  Monitor,
  Check,
  Star,
  TrendingUp,
  Users,
  FileText,
  Building,
  Mail,
  Phone,
  Globe
} from "lucide-react"
import { cn } from "@/lib/utils"
import { responsive, responsiveGrid, mobileAnimations, visualRetreat } from "@/lib/utils/responsive"

export default function MobileShowcasePage() {
  const [notifications, setNotifications] = useState(true)
  const [darkMode, setDarkMode] = useState(false)
  const [autoSave, setAutoSave] = useState(true)

  // Sample data for table showcase
  const teamData = [
    {
      id: 1,
      name: "<PERSON> Chen",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
      lastActive: "2 hours ago"
    },
    {
      id: 2,
      name: "Mike Johnson",
      email: "<EMAIL>", 
      role: "Member",
      status: "Active",
      lastActive: "1 day ago"
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "Viewer",
      status: "Pending",
      lastActive: "Never"
    }
  ]

  const tableColumns = [
    {
      key: "name",
      label: "Name",
      render: (value: string, row: any) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-xs text-muted-foreground md:hidden">{row.email}</div>
        </div>
      )
    },
    {
      key: "email",
      label: "Email",
      hideOnMobile: true
    },
    {
      key: "role",
      label: "Role",
      render: (value: string) => (
        <Badge variant="outline" className="text-xs">
          {value}
        </Badge>
      )
    },
    {
      key: "status",
      label: "Status",
      render: (value: string) => (
        <Badge 
          variant={value === "Active" ? "default" : "secondary"}
          className="text-xs"
        >
          {value}
        </Badge>
      )
    },
    {
      key: "lastActive",
      label: "Last Active",
      mobileLabel: "Last Seen",
      className: "text-muted-foreground text-sm"
    }
  ]

  const settingsSections = [
    {
      icon: User,
      title: "Profile",
      description: "Manage your personal information",
      items: ["Name", "Email", "Avatar", "Bio"]
    },
    {
      icon: Bell,
      title: "Notifications",
      description: "Configure your notification preferences",
      items: ["Email alerts", "Push notifications", "Weekly digest"]
    },
    {
      icon: Shield,
      title: "Security",
      description: "Manage your account security",
      items: ["Password", "Two-factor auth", "Login history"]
    },
    {
      icon: CreditCard,
      title: "Billing",
      description: "Manage your subscription and billing",
      items: ["Plan details", "Payment method", "Invoices"]
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile-First Header */}
      <header className={cn(
        "sticky top-0 z-30 border-b bg-background/95 backdrop-blur-sm safe-top"
      )}>
        <div className={responsive.padding.section}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={responsive.text.heading}>Mobile Showcase</h1>
              <p className={cn(responsive.text.small, "text-muted-foreground")}>
                Notion-grade responsive design patterns
              </p>
            </div>
            
            {/* Device Indicators */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="hidden xs:flex md:hidden">
                <Smartphone className="h-3 w-3 mr-1" />
                Mobile
              </Badge>
              <Badge variant="outline" className="hidden md:flex lg:hidden">
                <Tablet className="h-3 w-3 mr-1" />
                Tablet
              </Badge>
              <Badge variant="outline" className="hidden lg:flex">
                <Monitor className="h-3 w-3 mr-1" />
                Desktop
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={cn(responsive.padding.section, "space-y-8")}>
        {/* Settings Overview Cards */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-6")}>Settings Overview</h2>
          <div className={responsiveGrid({ mobile: 1, tablet: 2, desktop: 4 }, { mobile: 4, tablet: 6 })}>
            {settingsSections.map((section, index) => (
              <motion.div
                key={section.title}
                variants={mobileAnimations.stagger.item}
                initial="initial"
                animate="animate"
                transition={{ delay: index * 0.1 }}
              >
                <Card className={cn(
                  visualRetreat.card.base,
                  visualRetreat.card.interactive,
                  "cursor-pointer"
                )}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-xl bg-primary/10">
                        <section.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-base font-semibold truncate">
                          {section.title}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-muted-foreground mb-3">
                      {section.description}
                    </p>
                    <div className="space-y-1">
                      {section.items.slice(0, 3).map((item) => (
                        <div key={item} className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Check className="h-3 w-3" />
                          {item}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Mobile-First Form */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-6")}>Profile Settings</h2>
          <Card className={cn(visualRetreat.card.base, "p-0")}>
            <CardHeader className="p-6 pb-4">
              <CardTitle className="flex items-center gap-3">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className={cn(visualRetreat.form.container, "pt-0")}>
              {/* Form Grid - Mobile: 1 column, Desktop: 2 columns */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div className={visualRetreat.form.field}>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input id="firstName" placeholder="Enter your first name" className={visualRetreat.form.input} />
                </div>
                <div className={visualRetreat.form.field}>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input id="lastName" placeholder="Enter your last name" className={visualRetreat.form.input} />
                </div>
              </div>

              <div className={visualRetreat.form.field}>
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" className={visualRetreat.form.input} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea 
                  id="bio" 
                  placeholder="Tell us about yourself..."
                  className="min-h-[100px] resize-none"
                />
              </div>

              {/* Settings Toggles */}
              <Separator />
              
              <div className="space-y-4">
                <h3 className="font-medium">Preferences</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive updates about your account
                      </p>
                    </div>
                    <Switch
                      id="notifications"
                      checked={notifications}
                      onCheckedChange={setNotifications}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="autoSave">Auto-save</Label>
                      <p className="text-sm text-muted-foreground">
                        Automatically save your work
                      </p>
                    </div>
                    <Switch
                      id="autoSave"
                      checked={autoSave}
                      onCheckedChange={setAutoSave}
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons - Mobile: Stack, Desktop: Inline */}
              <div className="flex flex-col xs:flex-row gap-3 pt-4">
                <Button className="xs:flex-1">Save Changes</Button>
                <Button variant="outline" className="xs:flex-1">Cancel</Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Mobile-First Table */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-6")}>Team Members</h2>
          <MobileTable
            data={teamData}
            columns={tableColumns}
            onRowClick={(row) => console.log("Selected team member:", row)}
            emptyMessage="No team members found"
          />
        </section>

        {/* Mobile Design Principles */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-6")}>Design Principles</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6">
              <div className="text-center space-y-4">
                <div className="w-12 h-12 mx-auto bg-green-100 rounded-xl flex items-center justify-center">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Touch Targets</h3>
                  <p className="text-sm text-muted-foreground">
                    Minimum 44px touch targets for optimal mobile interaction
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="text-center space-y-4">
                <div className="w-12 h-12 mx-auto bg-blue-100 rounded-xl flex items-center justify-center">
                  <Smartphone className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Mobile-First</h3>
                  <p className="text-sm text-muted-foreground">
                    Designed for mobile, enhanced for desktop
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="text-center space-y-4">
                <div className="w-12 h-12 mx-auto bg-purple-100 rounded-xl flex items-center justify-center">
                  <Star className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Premium Feel</h3>
                  <p className="text-sm text-muted-foreground">
                    Notion-grade aesthetics and interactions
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </section>
      </main>
    </div>
  )
}
