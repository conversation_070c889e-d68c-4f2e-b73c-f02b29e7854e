/**
 * Deal API Integration
 * 
 * API client for managing deals with the backend
 */

import apiClient from "@/lib/api-client"
import {
  Deal,
  DealListResponse,
  DealCreateRequest,
  DealUpdateRequest,
  DealStatus,
  DealFilters
} from '@/lib/types/deal';
import { normalizeDealsIds, normalizeDealId } from '@/lib/utils/deal-id';
import type { ThesisScoring } from "@/lib/types/thesis-scoring"

export const DealAPI = {
  /**
   * List deals with optional filtering and pagination
   */
  async listDeals(
    skip: number = 0,
    limit: number = 100,
    filters?: DealFilters
  ): Promise<DealListResponse> {
    console.log('Fetching deals with filters:', filters);
    
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    // Add filters to params
    if (filters?.status) {
      params.append('status', filters.status);
    }
    if (filters?.stage) {
      params.append('stage', filters.stage);
    }
    if (filters?.sector) {
      params.append('sector', filters.sector);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.tags && filters.tags.length > 0) {
      params.append('tags', filters.tags.join(','));
    }
    
    const response = await apiClient.get(`/deals?${params.toString()}`);
    console.log('Deals fetched:', response.data);

    // Normalize deal IDs for frontend use
    if (response.data.deals) {
      response.data.deals = normalizeDealsIds(response.data.deals);
    }

    return response.data;
  },

  /**
   * Get a single deal by ID
   */
  async getDeal(dealId: string): Promise<Deal> {
    console.log(`Fetching deal ${dealId}`);
    const response = await apiClient.get(`/deals/${dealId}`);
    console.log('Deal fetched:', response.data);

    // Normalize deal ID for frontend use
    return normalizeDealId(response.data) as Deal;
  },

  /**
   * Create a new deal
   */
  async createDeal(dealData: DealCreateRequest): Promise<Deal> {
    console.log('Creating deal with data:', dealData);
    const response = await apiClient.post('/deals', dealData);
    console.log('Deal created:', response.data);
    return response.data;
  },

  /**
   * Update an existing deal
   */
  async updateDeal(dealId: string, dealData: DealUpdateRequest): Promise<Deal> {
    console.log(`Updating deal ${dealId} with data:`, dealData);
    const response = await apiClient.put(`/deals/${dealId}`, dealData);
    console.log('Deal updated:', response.data);
    return response.data;
  },

  /**
   * Delete a deal
   */
  async deleteDeal(dealId: string): Promise<{ success: boolean }> {
    console.log(`Deleting deal ${dealId}`);
    const response = await apiClient.delete(`/deals/${dealId}`);
    console.log('Deal deleted:', response.data);
    return response.data;
  },

  /**
   * Get deals by form ID
   */
  async getDealsByForm(
    formId: string,
    skip: number = 0,
    limit: number = 100
  ): Promise<DealListResponse> {
    console.log(`Fetching deals for form ${formId}`);
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    const response = await apiClient.get(`/deals/by_form/${formId}?${params.toString()}`);
    console.log('Form deals fetched:', response.data);
    return response.data;
  },

  /**
   * Search deals with advanced filters
   */
  async searchDeals(
    query: string,
    filters?: any,
    skip: number = 0,
    limit: number = 100
  ): Promise<DealListResponse> {
    console.log('Searching deals with query:', query);
    const searchData = {
      query,
      filters,
      skip,
      limit
    };
    
    const response = await apiClient.post('/deals/search', searchData);
    console.log('Search results:', response.data);
    return response.data;
  },

  /**
   * Add timeline event to deal
   */
  async addTimelineEvent(
    dealId: string,
    event: string,
    notes?: string
  ): Promise<Deal> {
    console.log(`Adding timeline event to deal ${dealId}`);
    const eventData = {
      event,
      notes
    };
    
    const response = await apiClient.post(`/deals/${dealId}/timeline`, eventData);
    console.log('Timeline event added:', response.data);
    return response.data;
  },

  /**
   * Update deal notes
   */
  async updateDealNotes(dealId: string, notes: string): Promise<Deal> {
    console.log(`Updating notes for deal ${dealId}`);
    const response = await apiClient.put(`/deals/${dealId}/notes`, { notes });
    console.log('Deal notes updated:', response.data);
    return response.data;
  },

  /**
   * Get full analysis for a deal
   */
  async getFullAnalysis(dealId: string): Promise<any> {
    console.log(`Getting full analysis for deal ${dealId}`);
    const response = await apiClient.get(`/deals/${dealId}/full-analysis`);
    console.log('Full analysis fetched:', response.data);
    return response.data;
  },

  /**
   * Override a score for a deal
   */
  async overrideScore(
    dealId: string,
    signalType: string,
    newScore: number,
    reason: string
  ): Promise<any> {
    console.log(`Overriding score for deal ${dealId}`);
    const overrideData = {
      signal_type: signalType,
      new_score: newScore,
      reason
    };

    const response = await apiClient.post(`/deals/${dealId}/score-override`, overrideData);
    console.log('Score override applied:', response.data);
    return response.data;
  },

  async getThesisScoring(dealId: string): Promise<ThesisScoring> {
    const response = await apiClient.get(`/api/v1/deals/${dealId}/thesis-scoring`)
    return response.data
  }
};

export default DealAPI;
