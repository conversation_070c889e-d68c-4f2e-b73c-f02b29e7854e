/**
 * Dashboard API Client
 * 
 * Handles all dashboard-related API calls including the unified summary endpoint.
 */

import apiClient from "../api-client";


// Types for dashboard API responses
export interface SectorDistribution {
  sector: string;
  count: number;
}

export interface DealStage {
  stage: string;
  count: number;
}

export interface AIActivity {
  active: boolean;
  last_sync?: string;
}

export interface OnboardingStatus {
  has_form: boolean;
  has_thesis: boolean;
}

export interface DashboardSummary {
  active_deals: number;
  active_deals_change_pct: number;
  forms: number;
  theses: number;
  ai_activity: AIActivity;
  sector_distribution: SectorDistribution[];
  deal_stages: DealStage[];
  onboarding: OnboardingStatus;
}

export const DashboardAPI = {
  /**
   * Get unified dashboard summary with all real-time data
   */
  async getSummary(): Promise<DashboardSummary> {
    console.log('Fetching dashboard summary');
    
    const response = await apiClient.get('/dashboard/summary');
    console.log('Dashboard summary fetched:', response.data);
    
    return response.data;
  },
};
