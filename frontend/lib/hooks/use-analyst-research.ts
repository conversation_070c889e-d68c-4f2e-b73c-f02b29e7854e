/**
 * Hook for managing analyst research data
 * 
 * Provides state management and API interactions for external signals research
 */

import { useState, useEffect, useCallback } from 'react'
import { ExternalSignalsAPI, ExternalSignalsResponse } from '@/lib/api/external-signals-api'
import { useToast } from '@/components/ui/use-toast'

interface UseAnalystResearchOptions {
  dealId: string
  autoLoad?: boolean
}

interface UseAnalystResearchReturn {
  data: ExternalSignalsResponse | null
  loading: boolean
  error: string | null
  refreshing: boolean
  hasData: boolean
  refresh: () => Promise<void>
  reload: () => Promise<void>
}

export function useAnalystResearch({ 
  dealId, 
  autoLoad = true 
}: UseAnalystResearchOptions): UseAnalystResearchReturn {
  const [data, setData] = useState<ExternalSignalsResponse | null>(null)
  const [loading, setLoading] = useState(autoLoad)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()

  // Check if we have any research data
  const hasData = !!(data && (
    data.competitors || 
    data.market || 
    data.news || 
    data.summary
  ))

  // Load research data
  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await ExternalSignalsAPI.getExternalSignals(dealId)
      setData(response)
      
    } catch (err) {
      console.error('Error loading analyst research:', err)
      setError('Failed to load research data')
    } finally {
      setLoading(false)
    }
  }, [dealId])

  // Refresh research data (force regeneration)
  const refresh = useCallback(async () => {
    try {
      setRefreshing(true)
      
      await ExternalSignalsAPI.refreshExternalSignals(dealId)
      
      toast({
        title: "Research refresh initiated",
        description: "New analysis will be available shortly. We'll notify you when it's ready.",
      })
      
      // Reload data after a short delay to allow processing
      setTimeout(() => {
        loadData()
      }, 3000)
      
    } catch (err) {
      console.error('Error refreshing research:', err)
      toast({
        title: "Refresh failed",
        description: "Unable to refresh research data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setRefreshing(false)
    }
  }, [dealId, loadData, toast])

  // Reload current data
  const reload = useCallback(async () => {
    await loadData()
  }, [loadData])

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad && dealId) {
      loadData()
    }
  }, [dealId, autoLoad, loadData])

  return {
    data,
    loading,
    error,
    refreshing,
    hasData,
    refresh,
    reload
  }
}
