// Deal types and interfaces for the frontend

// Enhanced exclusion filter result structure
export interface ExclusionFilterResult {
  excluded: boolean;
  filter_id?: string;
  filter_name?: string;
  reason?: string;
}

// Instance-level scoring for repeatable sections
export interface InstanceScore {
  score: number;
  matched: boolean;
  value: any;
  explanation: string;
}

// Question-level scoring details
export interface QuestionScore {
  rule_id: string;
  question_id: string;
  question_type: string;
  question_label: string;
  raw_score: number;
  weight: number;
  weighted_score: number;
  explanation: string;
  sources: string[];
  ai_generated: boolean;
  aggregation_used?: boolean;
  aggregation_type?: string;
  is_repeatable?: boolean;
  instances?: InstanceScore[];
}

// Bonus/penalty rule scoring
export interface BonusScore {
  rule_id: string;
  rule_name?: string;
  rule_type: 'BONUS' | 'PENALTY';
  points: number;
  status: 'awarded' | 'blocked' | 'failed';
  reason: string;
  explanation?: string;
  // Enriched fields from backend
  question_id?: string | null;
  question_label?: string | null;
  question_type?: string | null;
  condition?: any;
  notes?: string | null;
  matched_answers?: any[];
}

export interface Founder {
  _id: string;
  name: string;
  role?: string[];
  linkedin?: string;
  email?: string | null;
  serial_founder?: boolean;
  profile_picture?: string | null;
  experience?: string | null;
  skills?: string[] | null;
  education?: string | null;
  achievements?: string[] | null;
}

// Comprehensive thesis scoring structure
export interface ThesisScoring {
  thesis_id: string;
  thesis_name: string;
  score: {
    raw_total: number;
    normalized_percent: number;
    max_possible: number;
    core: number;
    bonus: number;
    penalty: number;
  };
  match_status: string;
  last_scored_at: number;
  ai_scoring: boolean;
  question_scores: Record<string, any>;
}

// Comprehensive scoring structure from backend
export interface ComprehensiveScoring {
  thesis: ThesisScoring;
  founders: {
    total_score: number;
    normalized_score: number;
    ai_analysis: string;
    key_insights: string[];
  };
  market: {
    total_score: number;
    normalized_score: number;
    ai_analysis: string;
    key_insights: string[];
  };
  metadata: {
    scoring_version: string;
    scored_at: number;
    total_rules_processed: number;
    ai_scoring_used: boolean;
  };
}

export interface Deal {
  _id: string;
  id: string;
  org_id: string;
  company_name: string;
  company_website?: string;
  sector?: string | string[];
  stage?: string;
  status: DealStatus;
  tags?: string[];
  founders?: Founder[];
  scoring?: {
    thesis?: ThesisScoring;
    founders?: {
      normalized_score: number;
      total_score: number;
      ai_analysis?: string;
      key_insights?: string[];
    };
    market?: {
      normalized_score: number;
      total_score: number;
      ai_analysis?: string;
      key_insights?: string[];
    };
    metadata?: {
      scoring_version: string;
      scored_at: number;
      total_rules_processed: number;
      ai_scoring_used: boolean;
    };
  };
  exclusion_filter_result?: {
    excluded: boolean;
    filter_id?: string;
    filter_name?: string;
    reason?: string;
  };
  created_at: number;
  updated_at: number;

  // Enrichment fields
  enriched_data?: any;
  enrichment_status?: string;
  enrichment_error?: string;
  enrichment_job_id?: string;

  // Form fields
  form_id?: string;
  submission_ids?: string[];
  context_block_url?: string;

  // Invite fields
  invite_status?: string;
  invite_sent_at?: number;
  invited_email?: string;

  // Other fields
  notes?: string;
  pitch_deck_url?: string;
  timeline?: Array<{
    date: string;
    event: string;
    notes?: string;
  }>;
}

export enum DealStatus {
  NEW = "new",
  TRIAGE = "triage",
  REVIEWED = "reviewed",
  EXCLUDED = "excluded",
  REJECTED = "rejected",
  APPROVED = "approved",
  NEGOTIATING = "negotiating",
  CLOSED = "closed"
}

export interface TimelineEvent {
  date: string;
  event: string;
  notes?: string;
}

export interface DealListResponse {
  deals: Deal[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface DealCreateRequest {
  org_id: string;
  form_id: string;
  submission_id: string;
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

export interface DealUpdateRequest {
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

// UI-specific types for enhanced deal display
export interface DealCardDisplayData {
  id: string;
  company_name: string;
  stage: string;
  sector: string;
  description: string;
  source: string;
  country: string;
  status: DealStatus;
  avatar_color: string;
  initials: string;

  // Enhanced scoring display
  thesis_match_percent?: number;
  core_score?: number;
  bonus_points?: number;
  penalty_points?: number;
  is_excluded?: boolean;
  exclusion_reason?: string;
  last_scored?: number;
}

export interface DealFilters {
  status?: DealStatus;
  stage?: string;
  sector?: string;
  search?: string;
  tags?: string[];
}

// Score breakdown for detailed analysis
export interface ScoreBreakdown {
  overall_score: number;
  thesis_match_percent: number;
  core_score: number;
  max_possible_score: number;
  bonus_total: number;
  penalty_total: number;
  final_score: number;

  // Question-level breakdown
  question_breakdown: QuestionScore[];
  bonus_breakdown: BonusScore[];

  // Exclusion info
  exclusion_info?: ExclusionFilterResult;

  // Metadata
  last_updated: number;
  scoring_version: string;
  ai_scoring_used: boolean;
}

// Instance display for repeatable sections
export interface InstanceDisplay {
  instance_id: string;
  instance_label: string; // "Founder 1", "Founder 2", etc.
  value: any;
  score: number;
  matched: boolean;
  explanation: string;
}

// Question display with instances
export interface QuestionDisplay extends QuestionScore {
  instances_display?: InstanceDisplay[];
  color_class: string;
  icon_class: string;
  status_text: string;
}

// Mock data types for demo
export interface MockDealData extends DealCardDisplayData {
  created_at: number;
  updated_at: number;
}
