"use client"

import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { FormWithDetails, Section, Question } from '@/lib/types/form';
import { QuestionRenderer } from './question-renderer';
import { RepeatableSection } from './repeatable-section';
import {
  VisibilityContext,
  getVisibleQuestions,
  FormAnswers,
  RepeatableAnswers
} from './visibility-engine';
import { ValidationResult, validateForm } from './validation-engine';

interface FormRendererProps {
  form: FormWithDetails;
  answers: FormAnswers;
  repeatableAnswers: RepeatableAnswers;
  onAnswerChange: (questionId: string, value: any) => void;
  onRepeatableAnswerChange: (sectionId: string, instanceIndex: number, questionId: string, value: any) => void;
  onAddRepeatableInstance: (sectionId: string) => void;
  onRemoveRepeatableInstance: (sectionId: string, instanceIndex: number) => void;
  validationResults: ValidationResult;
  disabled?: boolean;
  className?: string;
}

export function FormRenderer({
  form,
  answers,
  repeatableAnswers,
  onAnswerChange,
  onRepeatableAnswerChange,
  onAddRepeatableInstance,
  onRemoveRepeatableInstance,
  validationResults,
  disabled = false,
  className
}: FormRendererProps) {
  // Create visibility context
  const visibilityContext: VisibilityContext = {
    answers,
    repeatableAnswers
  };

  // Get all questions for validation
  const allQuestions = form.sections.flatMap(section => section.questions);

  // Get visible sections
  const visibleSections = form.sections
    .filter(section => {
      // For now, sections don't have visibility conditions in the current model
      // This can be extended if section-level visibility is added
      return true;
    })
    .sort((a, b) => a.order - b.order);

  const getFieldError = (questionId: string): string | undefined => {
    const error = validationResults.errors.find(err => err.field === questionId);
    return error?.message;
  };

  return (
    <div className={cn("space-y-8", className)}>
      <AnimatePresence mode="wait">
        {visibleSections.map((section) => (
          <motion.div
            key={section._id || section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          >
            {section.repeatable ? (
              <RepeatableSectionRenderer
                section={section}
                repeatableAnswers={repeatableAnswers}
                onAnswerChange={onRepeatableAnswerChange}
                onAddInstance={onAddRepeatableInstance}
                onRemoveInstance={onRemoveRepeatableInstance}
                visibilityContext={visibilityContext}
                validationResults={validationResults}
                disabled={disabled}
              />
            ) : (
              <RegularSectionRenderer
                section={section}
                answers={answers}
                onAnswerChange={onAnswerChange}
                visibilityContext={visibilityContext}
                validationResults={validationResults}
                disabled={disabled}
              />
            )}
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Empty state if no visible sections */}
      {visibleSections.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 text-slate-500"
        >
          <p>No sections to display</p>
        </motion.div>
      )}
    </div>
  );
}

interface RegularSectionRendererProps {
  section: Section;
  answers: FormAnswers;
  onAnswerChange: (questionId: string, value: any) => void;
  visibilityContext: VisibilityContext;
  validationResults: ValidationResult;
  disabled?: boolean;
}

function RegularSectionRenderer({
  section,
  answers,
  onAnswerChange,
  visibilityContext,
  validationResults,
  disabled = false
}: RegularSectionRendererProps) {
  // Get visible questions for this section
  const visibleQuestions = getVisibleQuestions(section.questions, visibilityContext);

  const getFieldError = (questionId: string): string | undefined => {
    const error = validationResults.errors.find(err => err.field === questionId);
    return error?.message;
  };

  // Don't render section if no visible questions
  if (visibleQuestions.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card className="overflow-hidden border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader className="bg-gradient-to-r from-slate-50/80 to-indigo-50/80 border-b border-slate-100/50">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <CardTitle className="text-2xl font-bold text-slate-900 flex items-center gap-3">
              <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-cyan-500 rounded-full"></div>
              {section.title}
            </CardTitle>
            {section.description && (
              <p className="text-slate-600 mt-3 text-base leading-relaxed">
                {section.description}
              </p>
            )}
          </motion.div>
        </CardHeader>

        <CardContent className="p-8 space-y-10">
          <AnimatePresence mode="wait">
            {visibleQuestions.map((question, questionIndex) => (
              <motion.div
                key={question._id || question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  duration: 0.4,
                  delay: questionIndex * 0.1,
                  ease: "easeOut"
                }}
                className="relative"
              >
                <QuestionRenderer
                  question={question}
                  value={answers[question._id || question.id!]}
                  onChange={(value) => onAnswerChange(question._id || question.id!, value)}
                  error={getFieldError(question._id || question.id!)}
                  disabled={disabled}
                />

                {questionIndex < visibleQuestions.length - 1 && (
                  <motion.div
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                    className="mt-10"
                  >
                    <Separator className="bg-gradient-to-r from-transparent via-slate-200 to-transparent" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
}

interface RepeatableSectionRendererProps {
  section: Section;
  repeatableAnswers: RepeatableAnswers;
  onAnswerChange: (sectionId: string, instanceIndex: number, questionId: string, value: any) => void;
  onAddInstance: (sectionId: string) => void;
  onRemoveInstance: (sectionId: string, instanceIndex: number) => void;
  visibilityContext: VisibilityContext;
  validationResults: ValidationResult;
  disabled?: boolean;
}

function RepeatableSectionRenderer({
  section,
  repeatableAnswers,
  onAnswerChange,
  onAddInstance,
  onRemoveInstance,
  visibilityContext,
  validationResults,
  disabled = false
}: RepeatableSectionRendererProps) {
  const sectionId = section._id || section.id!;
  const sectionInstances = repeatableAnswers[sectionId] || {};

  // Convert to the format expected by RepeatableSection
  const instances = Object.entries(sectionInstances).map(([instanceIndex, answers]) => ({
    instanceIndex: parseInt(instanceIndex),
    answers
  }));

  // Create validation results per instance
  const instanceValidationResults: { [instanceIndex: number]: ValidationResult } = {};

  instances.forEach(instance => {
    // Get visible questions for this instance
    const instanceContext: VisibilityContext = {
      ...visibilityContext,
      currentSectionInstance: {
        sectionId,
        instanceIndex: instance.instanceIndex
      }
    };

    const visibleQuestions = getVisibleQuestions(section.questions, instanceContext);
    const visibleQuestionIds = visibleQuestions.map(q => q._id || q.id!);

    // Validate this instance
    instanceValidationResults[instance.instanceIndex] = validateForm(
      section.questions,
      instance.answers,
      visibleQuestionIds
    );
  });

  // If no instances exist, create one by default (unless it's explicitly empty)
  if (instances.length === 0) {
    // Auto-create first instance
    setTimeout(() => onAddInstance(sectionId), 0);
  }

  return (
    <RepeatableSection
      section={section}
      instances={instances}
      onAddInstance={() => onAddInstance(sectionId)}
      onRemoveInstance={(instanceIndex) => onRemoveInstance(sectionId, instanceIndex)}
      onAnswerChange={(instanceIndex, questionId, value) =>
        onAnswerChange(sectionId, instanceIndex, questionId, value)
      }
      visibilityContext={visibilityContext}
      validationResults={instanceValidationResults}
      disabled={disabled}
    />
  );
}
