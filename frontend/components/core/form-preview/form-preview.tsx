"use client"

import {useCallback, useEffect, useState} from 'react';
import {AnimatePresence, motion} from 'framer-motion';
import {FormWithDetails} from '@/lib/types/form';
import {FormRenderer} from './form-renderer';
import {PremiumProgressBar} from './premium-progress-bar';
import {AutoSaveManager} from './auto-save-manager';
import {calculateProgress, FormAnswers, RepeatableAnswers, VisibilityContext} from './visibility-engine';
import {validateForm, ValidationResult} from './validation-engine';
import {Button} from '@/components/ui/button';
import {Card, CardContent} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {CheckCircle, FileText, RotateCcw, Save, Shield, Star, Zap} from 'lucide-react';
import {useToast} from '@/components/ui/use-toast';
import {cn} from '@/lib/utils';

interface FormPreviewProps {
  form: FormWithDetails;
  className?: string;
}

export function FormPreview({form, className}: FormPreviewProps) {
  const {toast} = useToast();

  // Form state
  const [answers, setAnswers] = useState<FormAnswers>({});
  const [repeatableAnswers, setRepeatableAnswers] = useState<RepeatableAnswers>({});
  const [validationResults, setValidationResults] = useState<ValidationResult>({isValid: true, errors: []});
  const [isLoading, setIsLoading] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Auto-save manager
  const [autoSaveManager] = useState(() => new AutoSaveManager(form._id || form.id!));

  // Load saved state on mount
  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      try {
        const savedState = autoSaveManager.loadState();
        if (savedState) {
          setAnswers(savedState.answers);
          setRepeatableAnswers(savedState.repeatableAnswers);
          setLastSaved(autoSaveManager.getLastSavedTime());

          toast({
            title: "Progress Restored",
            description: "Your previous answers have been restored.",
            duration: 3000,
          });
        }
      } catch (error) {
        console.error('Failed to load saved state:', error);
      }
    }
    setIsLoading(false);
  }, [autoSaveManager, toast]);

  // Auto-save when answers change
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      try {
        autoSaveManager.saveState(answers, repeatableAnswers);
        setLastSaved(new Date());
      } catch (error) {
        console.error('Failed to save state:', error);
      }
    }
  }, [answers, repeatableAnswers, autoSaveManager, isLoading]);

  // Create visibility context
  const visibilityContext: VisibilityContext = {
    answers,
    repeatableAnswers
  };

  // Calculate progress
  const allQuestions = form.sections.flatMap(section => section.questions);
  const progress = calculateProgress(allQuestions, visibilityContext);

  // Validate form in real-time
  useEffect(() => {
    // Get all visible questions
    const visibleQuestions = allQuestions.filter(question => {
      // For now, we'll validate all questions - visibility filtering can be added here
      return true;
    });

    const visibleQuestionIds = visibleQuestions.map(q => q._id || q.id!);
    const validation = validateForm(allQuestions, answers, visibleQuestionIds);
    setValidationResults(validation);
  }, [answers, allQuestions]);

  // Answer change handlers
  const handleAnswerChange = useCallback((questionId: string, value: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  }, []);

  const handleRepeatableAnswerChange = useCallback((
    sectionId: string,
    instanceIndex: number,
    questionId: string,
    value: any
  ) => {
    setRepeatableAnswers(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [instanceIndex]: {
          ...prev[sectionId]?.[instanceIndex],
          [questionId]: value
        }
      }
    }));
  }, []);

  const handleAddRepeatableInstance = useCallback((sectionId: string) => {
    setRepeatableAnswers(prev => {
      const existingInstances = prev[sectionId] || {};
      const maxIndex = Math.max(...Object.keys(existingInstances).map(Number), -1);
      const newIndex = maxIndex + 1;

      return {
        ...prev,
        [sectionId]: {
          ...existingInstances,
          [newIndex]: {}
        }
      };
    });
  }, []);

  const handleRemoveRepeatableInstance = useCallback((sectionId: string, instanceIndex: number) => {
    setRepeatableAnswers(prev => {
      const newSectionAnswers = {...prev[sectionId]};
      delete newSectionAnswers[instanceIndex];

      return {
        ...prev,
        [sectionId]: newSectionAnswers
      };
    });
  }, []);

  // Clear all data
  const handleClearForm = () => {
    setAnswers({});
    setRepeatableAnswers({});
    if (typeof window !== 'undefined') {
      try {
        autoSaveManager.clearState();
      } catch (error) {
        console.error('Failed to clear state:', error);
      }
    }
    setLastSaved(null);

    toast({
      title: "Form Cleared",
      description: "All answers have been cleared.",
      duration: 3000,
    });
  };

  // Manual save (for demonstration)
  const handleManualSave = () => {
    autoSaveManager.saveState(answers, repeatableAnswers);
    setLastSaved(new Date());

    toast({
      title: "Progress Saved",
      description: "Your answers have been saved locally.",
      duration: 3000,
    });
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-slate-600">Loading form...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative min-h-screen", className)}>
      {/* Premium Sticky Progress Bar */}
      <PremiumProgressBar
        completed={progress.completed}
        total={progress.total}
        percentage={progress.percentage}
        isComplete={progress.percentage === 100}
      />

      {/* Hero Header Section */}
      <div className="relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
          <div className="absolute inset-0 opacity-40">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-100/20 to-cyan-100/20"></div>
          </div>
        </div>

        <div className="relative mx-auto max-w-4xl px-4 py-16 lg:py-24">
          <motion.div
            initial={{opacity: 0, y: 30}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.8, ease: "easeOut"}}
            className="text-center"
          >
            {/* Premium Logo/Icon */}
            <motion.div
              initial={{scale: 0.8, opacity: 0}}
              animate={{scale: 1, opacity: 1}}
              transition={{duration: 0.6, delay: 0.2}}
              className="mb-8"
            >
              <div className="relative mx-auto size-24">
                <div
                  className="absolute inset-0 animate-pulse rounded-3xl bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500"></div>
                <div className="absolute inset-1 flex items-center justify-center rounded-2xl bg-white">
                  <div
                    className="flex size-12 items-center justify-center rounded-xl bg-gradient-to-r from-indigo-500 to-cyan-500">
                    <FileText className="size-6 text-white"/>
                  </div>
                </div>
                <motion.div
                  animate={{rotate: 360}}
                  transition={{duration: 20, repeat: Infinity, ease: "linear"}}
                  className="absolute -inset-2 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 opacity-20 blur-lg"
                ></motion.div>
              </div>
            </motion.div>

            {/* Form Title */}
            <motion.h1
              initial={{opacity: 0, y: 20}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.6, delay: 0.3}}
              className="mb-6 text-4xl font-extrabold tracking-tight lg:text-6xl"
            >
              <span
                className="bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                {form.name}
              </span>
            </motion.h1>

            {/* Description */}
            {form.description && (
              <motion.p
                initial={{opacity: 0, y: 20}}
                animate={{opacity: 1, y: 0}}
                transition={{duration: 0.6, delay: 0.4}}
                className="mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-slate-600"
              >
                {form.description}
              </motion.p>
            )}

            {/* Premium Badges */}
            <motion.div
              initial={{opacity: 0, y: 20}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.6, delay: 0.5}}
              className="flex flex-wrap items-center justify-center gap-3"
            >
              <Badge
                variant="outline"
                className="flex items-center gap-1 border-indigo-200 bg-indigo-50/50 px-4 py-2 text-sm font-medium text-indigo-700 backdrop-blur-sm"
              >
                <Zap className="size-3"/>
                <span>Version {form.version}</span>
              </Badge>
              {form.is_active && (
                <Badge
                  variant="default"
                  className="flex items-center gap-1 border-0 bg-gradient-to-r from-green-500 to-emerald-500 px-4 py-2 text-sm font-medium"
                >
                  <Shield className="size-3"/>
                  <span>Active</span>
                </Badge>
              )}
              {/* <Badge
                variant="outline"
                className="px-4 py-2 text-sm font-medium border-purple-200 text-purple-700 bg-purple-50/50 backdrop-blur-sm"
              >
                <Star className="h-3 w-3 mr-1" />
                Premium Experience
              </Badge> */}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Main Form Content */}
      <div className="mx-auto max-w-4xl px-4 pb-12">
        {/* Form Content */}
        <motion.div
          initial={{opacity: 0, y: 30}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.8, delay: 0.3}}
        >
          <FormRenderer
            form={form}
            answers={answers}
            repeatableAnswers={repeatableAnswers}
            onAnswerChange={handleAnswerChange}
            onRepeatableAnswerChange={handleRepeatableAnswerChange}
            onAddRepeatableInstance={handleAddRepeatableInstance}
            onRemoveRepeatableInstance={handleRemoveRepeatableInstance}
            validationResults={validationResults}
          />
        </motion.div>

        {/* Premium Form Actions */}
        <motion.div
          initial={{opacity: 0, y: 30}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.8, delay: 0.5}}
          className="mt-16"
        >
          <Card className="border-0 bg-white/80 shadow-2xl backdrop-blur-xl">
            <CardContent className="p-8">
              <div className="flex flex-col items-center justify-between gap-6 lg:flex-row">
                <div className="flex items-center gap-4">
                  <motion.div
                    whileHover={{scale: 1.05}}
                    whileTap={{scale: 0.95}}
                  >
                    <Button
                      onClick={handleManualSave}
                      variant="outline"
                      size="lg"
                      className="flex items-center gap-2 rounded-xl border-2 border-indigo-200 px-6 py-3 text-indigo-700 transition-all duration-200 hover:border-indigo-300 hover:bg-indigo-50"
                    >
                      <Save className="size-5"/>
                      <span>Save Progress</span>
                    </Button>
                  </motion.div>

                  <motion.div
                    whileHover={{scale: 1.05}}
                    whileTap={{scale: 0.95}}
                  >
                    <Button
                      onClick={handleClearForm}
                      variant="outline"
                      size="lg"
                      className="flex items-center gap-2 rounded-xl border-2 border-slate-200 px-6 py-3 text-slate-700 transition-all duration-200 hover:border-slate-300 hover:bg-slate-50"
                    >
                      <RotateCcw className="size-5"/>
                      <span>Clear Form</span>
                    </Button>
                  </motion.div>
                </div>

                <div className="space-y-2 text-center lg:text-right">
                  {lastSaved && (
                    <motion.p
                      initial={{opacity: 0}}
                      animate={{opacity: 1}}
                      className="flex items-center gap-2 text-sm text-slate-500"
                    >
                      {/*<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse flex-shrink-0"></div>*/}
                      <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
                    </motion.p>
                  )}

                  <AnimatePresence>
                    {progress.percentage === 100 && (
                      <motion.div
                        initial={{opacity: 0, scale: 0.8, y: 10}}
                        animate={{opacity: 1, scale: 1, y: 0}}
                        exit={{opacity: 0, scale: 0.8, y: -10}}
                        transition={{duration: 0.5, ease: "easeOut"}}
                        className="flex items-center gap-3 text-emerald-600"
                      >
                        <motion.div
                          animate={{rotate: [0, 360]}}
                          transition={{duration: 0.6, ease: "easeInOut"}}
                          className="flex items-center justify-center"
                        >
                          <CheckCircle className="size-6"/>
                        </motion.div>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold">Form Complete!</span>
                          <motion.div
                            animate={{scale: [1, 1.2, 1]}}
                            transition={{duration: 1, repeat: Infinity}}
                            className="flex items-center justify-center"
                          >
                            <Star className="size-5 text-yellow-500"/>
                          </motion.div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
