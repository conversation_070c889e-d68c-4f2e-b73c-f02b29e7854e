"use client"

import { motion } from 'framer-motion';
import { Question } from '@/lib/types/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon, Upload, AlertCircle, Check, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface QuestionRendererProps {
  question: Question;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

export function QuestionRenderer({
  question,
  value,
  onChange,
  error,
  disabled = false,
  className
}: QuestionRendererProps) {
  const questionId = question._id || question.id!;

  const renderInput = () => {
    switch (question.type) {
      case 'short_text':
        return (
          <ShortTextInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'long_text':
        return (
          <LongTextInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'number':
        return (
          <NumberInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'range':
        return (
          <RangeInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'single_select':
        return (
          <SingleSelectInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'multi_select':
        return (
          <MultiSelectInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'boolean':
        return (
          <BooleanInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'date':
        return (
          <DateInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'file':
        return (
          <FileInput
            question={question}
            value={value}
            onChange={onChange}
            disabled={disabled}
          />
        );

      default:
        return (
          <div className="text-sm text-slate-500 italic">
            Unsupported question type: {question.type}
          </div>
        );
    }
  };

  const hasValue = value !== undefined && value !== null && value !== '';
  const isValid = !error && hasValue;

  return (
    <motion.div
      className={cn("space-y-4", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      {/* Premium Question Label */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label
            htmlFor={questionId}
            className="text-lg font-semibold text-slate-900 flex items-center gap-2"
          >
            {question.label}
            {question.required && (
              <motion.span
                className="text-red-500 text-base"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                *
              </motion.span>
            )}
          </Label>

          {/* Status Indicator */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: isValid ? 1 : 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center"
          >
            <div className="w-6 h-6 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center">
              <Check className="h-3.5 w-3.5 text-white" />
            </div>
          </motion.div>
        </div>

        {question.help_text && (
          <motion.p
            className="text-slate-600 leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {question.help_text}
          </motion.p>
        )}
      </div>

      {/* Premium Input Field */}
      <motion.div
        className="space-y-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className={cn(
          "relative transition-all duration-300",
          error && "animate-shake"
        )}>
          {renderInput()}

          {/* Floating Success Indicator */}
          {isValid && (
            <motion.div
              initial={{ opacity: 0, scale: 0, x: 10 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none"
            >
              <div className="w-5 h-5 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center">
                <Check className="h-3 w-3 text-white" />
              </div>
            </motion.div>
          )}
        </div>

        {/* Enhanced Error Message */}
        {error && (
          <motion.div
            className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-xl"
            initial={{ opacity: 0, height: 0, y: -10 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -10 }}
            transition={{ duration: 0.3 }}
          >
            <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <X className="h-3 w-3 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-red-800">Validation Error</p>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
}

// Premium Individual Input Components
function ShortTextInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className="relative">
      <Input
        id={question._id || question.id!}
        type="text"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        disabled={disabled}
        placeholder={question.help_text || `Enter ${question.label.toLowerCase()}`}
        className={cn(
          "w-full h-12 px-4 text-base rounded-xl border-2 transition-all duration-300",
          "focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100",
          "hover:border-slate-400",
          isFocused && "shadow-lg",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      />
      {isFocused && (
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-indigo-500 pointer-events-none"
          initial={{ opacity: 0, scale: 1.02 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </div>
  );
}

function LongTextInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className="relative">
      <Textarea
        id={question._id || question.id!}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        disabled={disabled}
        placeholder={question.help_text || `Enter ${question.label.toLowerCase()}`}
        className={cn(
          "w-full min-h-[120px] p-4 text-base rounded-xl border-2 transition-all duration-300 resize-y",
          "focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100",
          "hover:border-slate-400",
          isFocused && "shadow-lg",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        rows={4}
      />
      {isFocused && (
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-indigo-500 pointer-events-none"
          initial={{ opacity: 0, scale: 1.02 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </div>
  );
}

function NumberInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const min = question.validation?.min;
  const max = question.validation?.max;

  return (
    <Input
      id={question._id || question.id!}
      type="number"
      value={value || ''}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      min={min}
      max={max}
      placeholder={question.help_text || `Enter ${question.label.toLowerCase()}`}
      className={cn(
        "w-full h-12 px-4 text-base rounded-xl border-2 transition-all duration-300",
        "focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100",
        "hover:border-slate-400",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    />
  );
}

function RangeInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const min = question.validation?.min || 0;
  const max = question.validation?.max || 100;
  const currentValue = value ? [Number(value)] : [min];

  return (
    <div className="space-y-4">
      <Slider
        id={question._id || question.id!}
        value={currentValue}
        onValueChange={(values) => onChange(values[0])}
        disabled={disabled}
        min={min}
        max={max}
        step={1}
        className="w-full"
      />
      <div className="flex justify-between text-sm text-slate-600">
        <span>{min}</span>
        <span className="font-medium">{currentValue[0]}</span>
        <span>{max}</span>
      </div>
    </div>
  );
}

function SingleSelectInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  if (!question.options || question.options.length === 0) {
    return (
      <div className="text-sm text-slate-500 italic">
        No options available
      </div>
    );
  }

  return (
    <Select value={value || ''} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder={`Select ${question.label.toLowerCase()}`} />
      </SelectTrigger>
      <SelectContent>
        {question.options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

function MultiSelectInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  if (!question.options || question.options.length === 0) {
    return (
      <div className="text-sm text-slate-500 italic">
        No options available
      </div>
    );
  }

  const selectedValues = Array.isArray(value) ? value : [];

  const handleOptionChange = (optionValue: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedValues, optionValue]);
    } else {
      onChange(selectedValues.filter(v => v !== optionValue));
    }
  };

  return (
    <div className="space-y-3">
      {question.options.map((option) => (
        <div key={option.value} className="flex items-center gap-2">
          <Checkbox
            id={`${question._id || question.id!}-${option.value}`}
            checked={selectedValues.includes(option.value)}
            onCheckedChange={(checked) => handleOptionChange(option.value, checked as boolean)}
            disabled={disabled}
          />
          <Label
            htmlFor={`${question._id || question.id!}-${option.value}`}
            className="text-sm font-normal cursor-pointer flex-1"
          >
            {option.label}
          </Label>
        </div>
      ))}
    </div>
  );
}

function BooleanInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const isChecked = value === true || value === 'true';

  return (
    <motion.div
      className="flex items-center justify-start p-4 bg-slate-50 rounded-xl border-2 border-slate-200 hover:border-slate-300 transition-all duration-300"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-center gap-4">
        <Switch
          id={question._id || question.id!}
          checked={isChecked}
          onCheckedChange={onChange}
          disabled={disabled}
          className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-indigo-500 data-[state=checked]:to-cyan-500"
        />
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{
          scale: isChecked ? 1 : 0.8,
          opacity: isChecked ? 1 : 0.5
        }}
        transition={{ duration: 0.2 }}
        className={cn(
          "px-3 py-1 rounded-full text-sm font-medium flex items-center justify-center",
          isChecked
            ? "bg-gradient-to-r from-emerald-500 to-green-500 text-white"
            : "bg-slate-200 text-slate-600"
        )}
      >
        {isChecked ? 'Yes' : 'No'}
      </motion.div>
      </div>
    </motion.div>
  );
}

function DateInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const [open, setOpen] = useState(false);
  const selectedDate = value ? new Date(value) : undefined;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal flex items-center gap-2",
            !selectedDate && "text-muted-foreground"
          )}
          disabled={disabled}
        >
          <CalendarIcon className="h-4 w-4 flex-shrink-0" />
          <span>{selectedDate ? format(selectedDate, "PPP") : "Pick a date"}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={(date) => {
            onChange(date?.toISOString());
            setOpen(false);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

function FileInput({ question, value, onChange, disabled }: Omit<QuestionRendererProps, 'error' | 'className'>) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onChange(file);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-center w-full">
        <label
          htmlFor={question._id || question.id!}
          className={cn(
            "flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer",
            "bg-slate-50 hover:bg-slate-100 border-slate-300 hover:border-slate-400",
            disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <Upload className="w-8 h-8 mb-3 text-slate-500" />
            <p className="mb-2 text-sm text-slate-500 text-center">
              <span className="font-semibold">Click to upload</span> or drag and drop
            </p>
            <p className="text-xs text-slate-500 text-center">
              {question.help_text || 'Any file type'}
            </p>
          </div>
          <input
            id={question._id || question.id!}
            type="file"
            className="hidden"
            onChange={handleFileChange}
            disabled={disabled}
          />
        </label>
      </div>

      {value && (
        <div className="text-sm text-slate-600">
          Selected: {value instanceof File ? value.name : value}
        </div>
      )}
    </div>
  );
}
