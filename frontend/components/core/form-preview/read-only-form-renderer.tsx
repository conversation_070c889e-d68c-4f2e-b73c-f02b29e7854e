"use client"

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye } from 'lucide-react';
import { Icons } from '@/components/icons';
import { SharedFormHeader } from '@/components/core/form-share/shared-form-header';
import { FormSection } from '@/components/core/form-share/form-section';
import { getVisibleQuestions, evaluateVisibility, VisibilityContext } from '@/components/core/form-preview/visibility-engine';

// Safe animation wrapper to prevent chunk loading errors
const SafeMotionDiv = ({ children, ...props }: any) => {
  try {
    return <motion.div {...props}>{children}</motion.div>;
  } catch (error) {
    console.warn('Animation error, falling back to static div:', error);
    return <div>{children}</div>;
  }
};

interface ReadOnlyFormRendererProps {
  formData: {
    form: any;
    organization: any;
    sharing_config: any;
    branding?: any;
  };
  className?: string;
}

export function ReadOnlyFormRenderer({ formData, className }: ReadOnlyFormRendererProps) {
  const [answers] = useState<Record<string, any>>({});
  const [progress] = useState(0);

  const { form, organization, branding } = formData;

  // Mock read-only state
  const canEdit = false;
  const canSubmit = false;
  const saving = false;
  const lastSaved = null;

  // Create visibility context for evaluating conditions
  const visibilityContext: VisibilityContext = {
    answers,
    repeatableAnswers: {}
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200"
      >
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Preview Mode - Read Only
            </span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                Preview
              </span>
            </div>
          </div>
          <Progress value={0} className="h-2" />
        </div>
      </motion.div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <SharedFormHeader
          form={form}
          organization={organization}
          branding={branding}
        />

        {/* Preview Mode Banner */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Alert className="bg-blue-50 border-blue-200">
            <Eye className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <div className="flex items-center justify-between">
                <span>
                  <strong>Preview Mode:</strong> This is how the form appears to applicants. All fields are read-only.
                </span>
              </div>
            </AlertDescription>
          </Alert>
        </motion.div>

        {/* Form Sections */}
        <div className="space-y-8">
          {form.sections.map((section: any, index: number) => {
            // Check if this section should be rendered based on visibility and type
            const isVisible = section.visibility_condition
              ? evaluateVisibility(section.visibility_condition, visibilityContext)
              : true;

            if (!isVisible) {
              return null;
            }

            // Skip repeatable sections that are controlled by questions with repeat_section_id
            if (section.repeatable) {
              const isControlledSection = form.sections.some((s: any) =>
                s.questions?.some((q: any) => q.repeat_section_id === section._id)
              );
              
              if (isControlledSection) {
                return null;
              }
            }

            return (
              <FormSection
                key={section._id}
                section={section}
                form={form}
                answers={answers}
                errors={{}}
                onAnswerChange={() => {}} // No-op for read-only
                onRemoveRepeatAnswers={() => {}} // No-op for read-only
                sectionIndex={index}
                submissionId={undefined}
                accessToken={undefined}
                readOnly={true} // Pass read-only flag
              />
            );
          })}
        </div>

        {/* Disabled Submit Button */}
        <SafeMotionDiv
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-12 text-center"
        >
          <Button
            disabled={true}
            size="lg"
            className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-gray-400 to-gray-500 shadow-lg opacity-50 cursor-not-allowed"
          >
            Submit Form (Preview Mode)
          </Button>

          <p className="mt-3 text-sm text-gray-500">
            This is a preview - form submission is disabled
          </p>
        </SafeMotionDiv>

        {/* Footer */}
        <SafeMotionDiv
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-xs text-gray-400 font-mono tracking-wider">
              POWERED BY
            </span>
            <img
              src="https://www.tractionx.ai/logo_black.png"
              alt="TractionX"
              className="h-4 w-auto"
            />
          </div>
        </SafeMotionDiv>
      </div>
    </div>
  );
}
