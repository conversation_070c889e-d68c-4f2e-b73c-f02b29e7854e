"use client"

import { motion } from 'framer-motion';
import { CheckCircle, Circle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProgressTrackerProps {
  completed: number;
  total: number;
  percentage: number;
  className?: string;
  showDetails?: boolean;
  variant?: 'bar' | 'circle' | 'minimal';
}

export function ProgressTracker({
  completed,
  total,
  percentage,
  className,
  showDetails = true,
  variant = 'bar'
}: ProgressTrackerProps) {
  if (variant === 'circle') {
    return (
      <CircularProgress
        completed={completed}
        total={total}
        percentage={percentage}
        className={className}
        showDetails={showDetails}
      />
    );
  }

  if (variant === 'minimal') {
    return (
      <MinimalProgress
        completed={completed}
        total={total}
        percentage={percentage}
        className={className}
      />
    );
  }

  return (
    <BarProgress
      completed={completed}
      total={total}
      percentage={percentage}
      className={className}
      showDetails={showDetails}
    />
  );
}

function BarProgress({
  completed,
  total,
  percentage,
  className,
  showDetails
}: ProgressTrackerProps) {
  return (
    <div className={cn("w-full", className)}>
      {showDetails && (
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-slate-900">
              Progress
            </span>
          </div>
          <div className="text-sm text-slate-600">
            {completed} of {total} completed
          </div>
        </div>
      )}
      
      <div className="relative">
        {/* Background bar */}
        <div className="w-full h-2 bg-slate-200 rounded-full overflow-hidden">
          {/* Progress bar with animation */}
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{
              duration: 0.8,
              ease: "easeOut"
            }}
          />
        </div>
        
        {/* Percentage label */}
        {showDetails && (
          <motion.div
            className="absolute -top-8 right-0 text-xs font-medium text-slate-700 bg-white px-2 py-1 rounded shadow-sm border"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {percentage}%
          </motion.div>
        )}
      </div>
    </div>
  );
}

function CircularProgress({
  completed,
  total,
  percentage,
  className,
  showDetails
}: ProgressTrackerProps) {
  const radius = 40;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn("flex items-center gap-4", className)}>
      <div className="relative">
        <svg
          className="w-20 h-20 transform -rotate-90"
          viewBox="0 0 100 100"
        >
          {/* Background circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-slate-200"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            strokeLinecap="round"
            className="text-blue-600"
            style={{
              strokeDasharray: circumference,
            }}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{
              duration: 1,
              ease: "easeOut"
            }}
          />
        </svg>
        
        {/* Percentage in center */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-bold text-slate-900">
            {percentage}%
          </span>
        </div>
      </div>
      
      {showDetails && (
        <div className="flex flex-col">
          <div className="text-sm font-medium text-slate-900">
            Form Progress
          </div>
          <div className="text-xs text-slate-600">
            {completed} of {total} questions completed
          </div>
          {percentage === 100 && (
            <motion.div
              className="flex items-center gap-1 text-xs text-green-600 mt-1"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              <CheckCircle className="h-3 w-3" />
              Complete!
            </motion.div>
          )}
        </div>
      )}
    </div>
  );
}

function MinimalProgress({
  completed,
  total,
  percentage,
  className
}: Omit<ProgressTrackerProps, 'showDetails'>) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-1">
        {percentage === 100 ? (
          <CheckCircle className="h-4 w-4 text-green-600" />
        ) : (
          <Clock className="h-4 w-4 text-blue-600" />
        )}
        <span className="text-xs font-medium text-slate-700">
          {percentage}%
        </span>
      </div>
      
      <div className="flex-1 h-1 bg-slate-200 rounded-full overflow-hidden">
        <motion.div
          className="h-full bg-blue-600 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{
            duration: 0.6,
            ease: "easeOut"
          }}
        />
      </div>
      
      <span className="text-xs text-slate-500">
        {completed}/{total}
      </span>
    </div>
  );
}

// Sticky progress bar for mobile
export function StickyProgressBar({
  completed,
  total,
  percentage,
  className
}: ProgressTrackerProps) {
  return (
    <motion.div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 px-4 py-3",
        className
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <ProgressTracker
        completed={completed}
        total={total}
        percentage={percentage}
        variant="minimal"
        showDetails={false}
      />
    </motion.div>
  );
}
