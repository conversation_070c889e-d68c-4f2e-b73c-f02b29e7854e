"use client"

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

interface SharedFormHeaderProps {
  form: {
    name: string;
    description: string;
  };
  organization: {
    name: string;
    logo_url?: string;
    intro_message?: string;
  };
  branding?: {
    primaryColor?: string;
    theme?: string;
    [key: string]: any;
  };
}

export function SharedFormHeader({ form, organization, branding }: SharedFormHeaderProps) {
  const [logoError, setLogoError] = useState(false);
  const [showLogo, setShowLogo] = useState(true);

  // Always show a logo - either organization logo or TractionX fallback
  const logoSrc = (!logoError && organization.logo_url) ? organization.logo_url : '/images/TX-Placeholder.png';
  const logoAlt = (!logoError && organization.logo_url) ? `${organization.name} logo` : 'TractionX logo';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center mb-12"
    >
      {/* Organization Logo */}
      {showLogo && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <div className="relative w-16 h-16 mx-auto rounded-full overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800 p-2">
            <Image
              src={logoSrc}
              alt={logoAlt}
              fill
              className="object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
              onError={() => {
                if (!logoError) {
                  setLogoError(true);
                } else {
                  setShowLogo(false);
                }
              }}
              unoptimized={logoSrc.startsWith('http')}
            />
          </div>
        </motion.div>
      )}

      {/* Organization Name */}
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-lg font-medium text-gray-600 mb-2"
      >
        Shared by
      </motion.p>

      {/* Organization Banner */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.3 }}
        className="mb-6"
      >
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-4">
            <div className="h-px bg-gradient-to-r from-transparent to-gray-300 w-12"></div>
            <span className="font-mono text-xl tracking-widest text-gray-400 font-light uppercase">
              {organization.name}
            </span>
            <div className="h-px bg-gradient-to-l from-transparent to-gray-300 w-12"></div>
          </div>
        </div>
      </motion.div>

      {/* Form Title */}
      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="text-4xl font-extrabold tracking-tight text-gray-900 mb-4"
        style={{
          color: branding?.primaryColor || undefined
        }}
      >
        {form.name}
      </motion.h1>

      {/* Form Description */}
      {form.description && (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed"
        >
          {form.description}
        </motion.p>
      )}

      {/* Organization Intro Message */}
      {organization.intro_message && (
        <motion.blockquote
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-6 italic text-gray-500 bg-gray-50 p-4 rounded-lg border-l-4 border-gray-300 max-w-2xl mx-auto"
        >
          "{organization.intro_message}"
        </motion.blockquote>
      )}
    </motion.div>
  );
}
