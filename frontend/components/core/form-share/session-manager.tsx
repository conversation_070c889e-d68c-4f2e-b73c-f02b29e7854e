"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, RefreshCw, Wifi, WifiOff, Clock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';

interface SessionManagerProps {
  children: React.ReactNode;
  onSessionExpired?: () => void;
  onNetworkError?: () => void;
}

export function SessionManager({
  children,
  onSessionExpired,
  onNetworkError
}: SessionManagerProps) {
  const { isAuthenticated, accessToken, refreshTokens } = usePublicAuth();
  const [isOnline, setIsOnline] = useState(true);
  const [sessionWarning, setSessionWarning] = useState(false);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Session timeout warning (25 minutes = 1500 seconds)
  const SESSION_WARNING_TIME = 25 * 60 * 1000;
  const SESSION_TIMEOUT_TIME = 30 * 60 * 1000;

  // Track user activity
  const updateActivity = useCallback(() => {
    setLastActivity(Date.now());
    setSessionWarning(false);
  }, []);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
    };

    const handleOffline = () => {
      setIsOnline(false);
      onNetworkError?.();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial status
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [onNetworkError]);

  // Activity tracking
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [updateActivity]);

  // Session timeout monitoring
  useEffect(() => {
    if (!isAuthenticated) return;

    const checkSession = () => {
      const now = Date.now();
      const timeSinceActivity = now - lastActivity;

      if (timeSinceActivity > SESSION_TIMEOUT_TIME) {
        onSessionExpired?.();
      } else if (timeSinceActivity > SESSION_WARNING_TIME) {
        setSessionWarning(true);
      }
    };

    const interval = setInterval(checkSession, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [isAuthenticated, lastActivity, onSessionExpired]);

  // Auto-refresh tokens
  const handleRefreshSession = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      await refreshTokens();
      setSessionWarning(false);
      updateActivity();
    } catch (error) {
      console.error('Failed to refresh session:', error);
      onSessionExpired?.();
    } finally {
      setIsRefreshing(false);
    }
  };

  const getTimeUntilExpiry = () => {
    const timeSinceActivity = Date.now() - lastActivity;
    const timeRemaining = SESSION_TIMEOUT_TIME - timeSinceActivity;
    const minutesRemaining = Math.floor(timeRemaining / (60 * 1000));
    return Math.max(0, minutesRemaining);
  };

  return (
    <div className="relative">
      {/* Network Status Indicator */}
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 bg-red-600 text-white p-2 text-center text-sm"
          >
            <div className="flex items-center justify-center space-x-2">
              <WifiOff className="h-4 w-4" />
              <span>No internet connection. Changes will be saved when connection is restored.</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Session Warning */}
      <AnimatePresence>
        {sessionWarning && isAuthenticated && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-4 right-4 z-40 max-w-sm"
          >
            <Alert variant="destructive" className="border-orange-200 bg-orange-50">
              <Clock className="h-4 w-4 text-orange-600" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium text-orange-800">
                      Session expiring soon
                    </span>
                    <p className="text-sm text-orange-700 mt-1">
                      {getTimeUntilExpiry()} minutes remaining
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefreshSession}
                    disabled={isRefreshing}
                    className="ml-2 text-orange-700 border-orange-300 hover:bg-orange-100"
                  >
                    {isRefreshing ? (
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    ) : (
                      'Extend'
                    )}
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Connection Status Badge */}
      <div className="fixed bottom-4 right-4 z-30">
        <Badge
          variant={isOnline ? "default" : "destructive"}
          className="flex items-center space-x-1"
        >
          {isOnline ? (
            <Wifi className="h-3 w-3" />
          ) : (
            <WifiOff className="h-3 w-3" />
          )}
          <span>{isOnline ? 'Online' : 'Offline'}</span>
        </Badge>
      </div>

      {children}
    </div>
  );
}

interface AutoSaveIndicatorProps {
  saving: boolean;
  lastSaved: Date | null;
  error?: string;
}

export function AutoSaveIndicator({
  saving,
  lastSaved,
  error
}: AutoSaveIndicatorProps) {
  const getStatusText = () => {
    if (error) return 'Save failed';
    if (saving) return 'Saving...';
    if (lastSaved) {
      const now = new Date();
      const diffMs = now.getTime() - lastSaved.getTime();
      const diffMinutes = Math.floor(diffMs / 60000);
      
      if (diffMinutes < 1) return 'Saved just now';
      if (diffMinutes === 1) return 'Saved 1 minute ago';
      if (diffMinutes < 60) return `Saved ${diffMinutes} minutes ago`;
      
      return `Saved at ${lastSaved.toLocaleTimeString()}`;
    }
    return 'Not saved';
  };

  const getStatusColor = () => {
    if (error) return 'text-red-600';
    if (saving) return 'text-blue-600';
    if (lastSaved) return 'text-green-600';
    return 'text-gray-500';
  };

  return (
    <div className="flex items-center space-x-2 text-xs">
      {saving && (
        <RefreshCw className="h-3 w-3 animate-spin text-blue-600" />
      )}
      <span className={getStatusColor()}>
        {getStatusText()}
      </span>
    </div>
  );
}

interface FormRecoveryProps {
  onRecover: () => void;
  onDiscard: () => void;
  hasUnsavedChanges: boolean;
}

export function FormRecovery({
  onRecover,
  onDiscard,
  hasUnsavedChanges
}: FormRecoveryProps) {
  if (!hasUnsavedChanges) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <div className="flex items-center space-x-3 mb-4">
          <AlertTriangle className="h-6 w-6 text-orange-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Unsaved Changes Detected
          </h3>
        </div>
        
        <p className="text-gray-600 mb-6">
          We found unsaved changes from your previous session. Would you like to recover them?
        </p>
        
        <div className="flex space-x-3">
          <Button
            onClick={onRecover}
            className="flex-1"
          >
            Recover Changes
          </Button>
          <Button
            variant="outline"
            onClick={onDiscard}
            className="flex-1"
          >
            Start Fresh
          </Button>
        </div>
      </div>
    </motion.div>
  );
}

// Hook for managing form session state
export function useFormSession() {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaveAttempt, setLastSaveAttempt] = useState<Date | null>(null);
  const [saveError, setSaveError] = useState<string | null>(null);

  const markUnsaved = useCallback(() => {
    setHasUnsavedChanges(true);
  }, []);

  const markSaved = useCallback(() => {
    setHasUnsavedChanges(false);
    setLastSaveAttempt(new Date());
    setSaveError(null);
  }, []);

  const markSaveError = useCallback((error: string) => {
    setSaveError(error);
    setLastSaveAttempt(new Date());
  }, []);

  // Warn user before leaving with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  return {
    hasUnsavedChanges,
    lastSaveAttempt,
    saveError,
    markUnsaved,
    markSaved,
    markSaveError
  };
}
