"use client"

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Icons } from '@/components/icons';
import { Alert, AlertDescription } from '@/components/ui/alert';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface MagicLinkAuthProps {
  isOpen: boolean;
  onClose: () => void;
  token: string;
  onSuccess: () => void;
}

export function MagicLinkAuth({ isOpen, onClose, token, onSuccess }: MagicLinkAuthProps) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const [error, setError] = useState('');

  const handleSendMagicLink = async () => {
    if (!email) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`${API_BASE}/magic-link`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          redirect_url: `${window.location.origin}/share/${token}`
        })
      });

      if (response.ok) {
        setSent(true);
      } else {
        setError('Failed to send magic link. Please try again.');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setSent(false);
    setError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">Save Your Progress</DialogTitle>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {!sent ? (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <p className="text-sm text-gray-600 text-center">
                Enter your email to receive a link and save your progress securely.
              </p>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMagicLink()}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <Icons.alertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleClose} className="flex-1">
                  Cancel
                </Button>
                <Button
                  onClick={handleSendMagicLink}
                  disabled={!email || loading}
                  className="flex-1"
                >
                  {loading ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    'Send Magic Link'
                  )}
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="success"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center space-y-4"
            >
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Icons.mail className="h-8 w-8 text-green-600" />
              </div>

              <div>
                <h3 className="font-semibold text-gray-900">Check Your Email</h3>
                <p className="text-sm text-gray-600 mt-1">
                  We've sent the link to <strong>{email}</strong>
                </p>
              </div>

              <Alert>
                <Icons.info className="h-4 w-4" />
                <AlertDescription>
                  Click the link in your email to log in and save your progress automatically.
                </AlertDescription>
              </Alert>

              <Button onClick={handleClose} className="w-full">
                Continue Filling Form
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
