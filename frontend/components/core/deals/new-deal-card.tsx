"use client"

import { motion, easeOut, easeIn, easeInOut } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NewDealCardProps {
  onClick: () => void;
  index?: number;
}

const linear = (t: number) => t;

export function NewDealCard({ onClick, index = 0 }: NewDealCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: easeOut,
        delay: index * 0.05
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group h-full"
    >
      <Card 
        className="relative overflow-hidden border-2 border-dashed border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-lg transition-all duration-300 bg-gray-50/50 hover:bg-white rounded-xl cursor-pointer hover:-translate-y-1 h-full"
        onClick={onClick}
      >

        
        <CardContent className="p-6 flex flex-col items-center justify-center text-center min-h-[200px] h-full gap-y-2">
          {/* Large Plus Icon */}
          <div className="w-16 h-16 rounded-full bg-gray-100 group-hover:bg-blue-50 flex items-center justify-center transition-colors duration-300">
            <Plus className="h-7 w-7 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 relative top-[1px] inline-block leading-none" />
          </div>
          
          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
            Add New Deal
          </h3>
          
          {/* Description */}
          <p className="text-sm text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
            Create a new investment opportunity
          </p>
        </CardContent>
      </Card>
    </motion.div>
  );
}
