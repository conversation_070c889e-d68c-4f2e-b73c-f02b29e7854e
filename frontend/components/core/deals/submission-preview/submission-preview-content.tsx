"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { FormattedSubmission, FormattedSection, FormattedAnswer } from "@/lib/types/submission-preview"
import { SubmissionPreviewAPI } from "@/lib/api/submission-preview-api"

interface SubmissionPreviewContentProps {
  submission: FormattedSubmission
}

interface AnswerDisplayProps {
  answer: FormattedAnswer
}

interface SectionCardProps {
  section: FormattedSection
  index: number
}

// Component to display individual answers
function AnswerDisplay({ answer }: AnswerDisplayProps) {
  const displayValue = answer.isEmpty
    ? SubmissionPreviewAPI.getEmptyDisplayValue()
    : Array.isArray(answer.value)
      ? answer.value.join(", ")
      : String(answer.value)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 py-5 border-b border-gray-100/60 last:border-b-0 hover:bg-gray-50/30 transition-colors duration-200 rounded-lg px-3 -mx-3">
      <div className="lg:col-span-2 font-semibold text-gray-900 text-base leading-relaxed">
        {answer.label}
      </div>
      <div className={cn(
        "lg:col-span-3 text-base select-text leading-relaxed",
        answer.isEmpty ? "text-gray-400 italic" : "text-gray-700"
      )}>
        {answer.type === 'boolean' && !answer.isEmpty ? (
          <Badge
            variant={answer.value === 'Yes' ? 'default' : 'secondary'}
            className={cn(
              "text-sm px-4 py-1.5 font-medium",
              answer.value === 'Yes'
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-gray-100 text-gray-700 border-gray-200"
            )}
          >
            {displayValue}
          </Badge>
        ) : answer.type === 'multi_select' && Array.isArray(answer.value) && answer.value.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {answer.value.map((item, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-sm px-3 py-1.5 bg-blue-50 text-blue-800 border-blue-200 font-medium"
              >
                {item}
              </Badge>
            ))}
          </div>
        ) : answer.type === 'number' && !answer.isEmpty ? (
          <span className="font-mono text-lg font-semibold text-gray-900">{displayValue}</span>
        ) : (
          <span className="break-words font-medium">{displayValue}</span>
        )}
      </div>
    </div>
  )
}

// Component to display a section card
function SectionCard({ section, index }: SectionCardProps) {
  const hasContent = SubmissionPreviewAPI.sectionHasContent(section)

  if (!hasContent) {
    return null // Don't render empty sections
  }

  // Get section icon based on title
  const getSectionIcon = (title: string) => {
    const lowerTitle = title.toLowerCase()
    if (lowerTitle.includes('company') || lowerTitle.includes('overview')) return ''
    if (lowerTitle.includes('founder') || lowerTitle.includes('team')) return ''
    if (lowerTitle.includes('business') || lowerTitle.includes('model')) return ''
    if (lowerTitle.includes('funding') || lowerTitle.includes('financial')) return ''
    if (lowerTitle.includes('product') || lowerTitle.includes('traction')) return ''
    if (lowerTitle.includes('market') || lowerTitle.includes('competition')) return ''
    return ''
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="h-fit"
    >
      <Card className="bg-white/95 backdrop-blur-sm border-gray-200/40 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300/60 rounded-2xl overflow-hidden">
        <CardHeader className="pb-2 bg-white border-b border-gray-100/50 rounded-t-2xl">
          <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3">
            <span className="text-2xl">{getSectionIcon(section.title)}</span>
            <div className="flex-1">
              <div className="flex items-center gap-3">
                {section.title}
                {section.isRepeatable && (
                  <Badge variant="outline" className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200">
                    Repeatable
                  </Badge>
                )}
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6 pb-8">
          {section.isRepeatable && section.instances ? (
            // Render repeatable section instances
            <div className="space-y-8">
              {section.instances.map((instance, instanceIndex) => (
                <div key={instance.index} className="space-y-4">
                  <div className="flex items-center gap-3 pb-3 border-b border-gray-200/60">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm font-bold">#{instance.index + 1}</span>
                    </div>
                    <h4 className="font-semibold text-gray-800 text-lg">
                      {instance.title}
                    </h4>
                  </div>
                  <div className="space-y-1 pl-2">
                    {instance.answers.map((answer, answerIndex) => (
                      <AnswerDisplay key={answerIndex} answer={answer} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Render regular section questions
            <div className="space-y-1">
              {section.questions?.map((question, questionIndex) => (
                <AnswerDisplay key={questionIndex} answer={question} />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function SubmissionPreviewContent({ submission }: SubmissionPreviewContentProps) {
  const summary = SubmissionPreviewAPI.getSubmissionSummary(submission)
  const sectionsWithContent = submission.sections.filter(section =>
    SubmissionPreviewAPI.sectionHasContent(section)
  )

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* Section cards in a beautiful grid layout */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {sectionsWithContent.map((section, index) => (
          <SectionCard
            key={section.id}
            section={section}
            index={index}
          />
        ))}
      </div>

      {/* Empty state if no content */}
      {sectionsWithContent.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-12"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/60">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content to display</h3>
            <p className="text-sm text-gray-600">This submission appears to be empty or contains no answered questions.</p>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
