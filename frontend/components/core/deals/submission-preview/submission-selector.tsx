"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, FileText, Calendar } from "lucide-react"
import { cn } from "@/lib/utils"
import { SubmissionPreview } from "@/lib/types/submission-preview"

interface SubmissionSelectorProps {
  submissions: SubmissionPreview[]
  selectedSubmissionId: string
  onSubmissionChange: (submissionId: string) => void
}

export function SubmissionSelector({ 
  submissions, 
  selectedSubmissionId, 
  onSubmissionChange 
}: SubmissionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  const selectedSubmission = submissions.find(s => s.submission_id === selectedSubmissionId)
  
  if (!selectedSubmission) {
    return null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleSelect = (submissionId: string) => {
    onSubmissionChange(submissionId)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      {/* Trigger Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "gap-3 bg-white/95 backdrop-blur-sm border-gray-200/40 hover:bg-gray-50/80 w-full sm:min-w-[320px] justify-between shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl px-4 py-3 h-auto",
          isOpen && "ring-2 ring-blue-500/20 border-blue-300/60"
        )}
      >
        <div className="flex items-center gap-3 min-w-0">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
            <FileText className="h-5 w-5 text-white" />
          </div>
          <div className="text-left min-w-0">
            <div className="font-semibold text-gray-900 truncate text-base">
              {selectedSubmission.form_name}
            </div>
            <div className="text-sm text-gray-600">
              v{selectedSubmission.form_version} • {formatDate(selectedSubmission.submitted_at)}
            </div>
          </div>
        </div>
        <ChevronDown className={cn(
          "h-4 w-4 text-gray-500 transition-transform flex-shrink-0",
          isOpen && "rotate-180"
        )} />
      </Button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown Content */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 sm:right-0 left-0 sm:left-auto top-full mt-3 w-full sm:w-96 bg-white/98 backdrop-blur-xl border border-gray-200/40 rounded-2xl shadow-2xl z-20 overflow-hidden"
            >
              <div className="p-2 space-y-1 max-h-64 overflow-y-auto">
                {submissions.map((submission, index) => {
                  const isSelected = submission.submission_id === selectedSubmissionId
                  
                  return (
                    <motion.button
                      key={submission.submission_id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => handleSelect(submission.submission_id)}
                      className={cn(
                        "w-full text-left p-4 rounded-xl transition-all duration-200 hover:bg-gray-50/80 hover:shadow-md",
                        isSelected && "bg-gradient-to-r from-blue-50/80 to-blue-100/60 border border-blue-200/60 shadow-sm"
                      )}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex items-center gap-3 min-w-0 flex-1">
                          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <FileText className="h-5 w-5 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="font-semibold text-gray-900 truncate">
                                {submission.form_name}
                              </div>
                              <Badge variant="secondary" className="text-xs flex-shrink-0 bg-gray-100 text-gray-700">
                                v{submission.form_version}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Calendar className="h-3 w-3" />
                              <span>Submitted {formatDate(submission.submitted_at)}</span>
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                              {submission.sections.length} sections
                            </div>
                          </div>
                        </div>
                        
                        {isSelected && (
                          <div className="flex-shrink-0">
                            <div className="w-2 h-2 bg-blue-600 rounded-full" />
                          </div>
                        )}
                      </div>
                    </motion.button>
                  )
                })}
              </div>
              
              {/* Footer */}
              <div className="border-t border-gray-200/60 p-3 bg-gray-50/50">
                <div className="text-xs text-gray-500 text-center">
                  {submissions.length} submission{submissions.length !== 1 ? 's' : ''} available
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
