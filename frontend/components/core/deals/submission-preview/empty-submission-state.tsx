"use client"

import { motion } from "framer-motion"
import { FileText } from "lucide-react"

export function EmptySubmissionState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-md mx-auto text-center"
    >
      <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-12 border border-gray-200/40 shadow-xl max-w-lg mx-auto">
        {/* Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center"
        >
          <FileText className="w-10 h-10 text-gray-500" />
        </motion.div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            No submissions received yet
          </h3>
          <p className="text-base text-gray-600 leading-relaxed mb-6">
            No submissions have been received for this deal yet.
            Submissions will appear here once they are submitted through the deal form.
          </p>
          <div className="text-sm text-gray-500">
            Check back later or share the deal link to receive submissions.
          </div>
        </motion.div>

        {/* Decorative elements */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mt-8 flex justify-center space-x-3"
        >
          <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-60" />
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full" />
          <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-60" />
        </motion.div>
      </div>
    </motion.div>
  )
}
