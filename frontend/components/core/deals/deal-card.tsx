"use client"

import Link from 'next/link';
import { motion, easeOut } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { visualRetreat } from '@/lib/utils/responsive';
import { Deal, DealStatus } from '@/lib/types/deal';
import { getDealId } from '@/lib/utils/deal-id';
import {
  MoreHorizontal,
  ExternalLink,
  MapPin,
  Flag,
  Archive,
  Trash2,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Calendar,
  Building2,
  Tag,
  FileText,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { DealAPI } from '@/lib/api/deal-api';
import { useToast } from '@/components/ui/use-toast';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
  onDelete?: (dealId: string) => Promise<boolean>;
}

// Backend-aligned utility functions
const getStatusColor = (status: DealStatus) => {
  switch (status) {
    case DealStatus.NEW:
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case DealStatus.TRIAGE:
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case DealStatus.REVIEWED:
      return 'bg-purple-50 text-purple-700 border-purple-200';
    case DealStatus.APPROVED:
      return 'bg-green-50 text-green-700 border-green-200';
    case DealStatus.NEGOTIATING:
      return 'bg-orange-50 text-orange-700 border-orange-200';
    case DealStatus.CLOSED:
      return 'bg-gray-50 text-gray-700 border-gray-200';
    case DealStatus.EXCLUDED:
    case DealStatus.REJECTED:
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

const getScoreColor = (score: number) => {
  if (score >= 80) return 'bg-green-50 text-green-700 border-green-200';
  if (score >= 50) return 'bg-blue-50 text-blue-700 border-blue-200';
  return 'bg-yellow-50 text-yellow-700 border-yellow-200';
};

// Enhanced scoring utilities for thesis match display
const getThesisMatchColor = (percent: number) => {
  if (percent >= 80) return 'bg-green-500 text-white';
  if (percent >= 60) return 'bg-blue-500 text-white';
  if (percent >= 40) return 'bg-yellow-500 text-white';
  return 'bg-red-500 text-white';
};

const getBonusPenaltyColor = (points: number) => {
  if (points > 0) return 'bg-green-100 text-green-700 border-green-200';
  if (points < 0) return 'bg-red-100 text-red-700 border-red-200';
  return 'bg-gray-100 text-gray-700 border-gray-200';
};

const getExclusionIcon = (excluded: boolean) => {
  return excluded ? XCircle : CheckCircle;
};

const getExclusionColor = (excluded: boolean) => {
  return excluded
    ? 'text-red-600 bg-red-50 border-red-200'
    : 'text-green-600 bg-green-50 border-green-200';
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500 text-white',
    'bg-green-500 text-white',
    'bg-purple-500 text-white',
    'bg-orange-500 text-white',
    'bg-pink-500 text-white',
    'bg-indigo-500 text-white',
    'bg-teal-500 text-white',
    'bg-red-500 text-white',
  ];

  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

const formatSector = (sector: string | string[] | undefined): string[] => {
  if (!sector) return [];
  if (Array.isArray(sector)) return sector;
  return [sector];
};

const createSummary = (deal: Deal): string => {
  const sectors = formatSector(deal.sector);
  const parts: string[] = [];

  if (sectors.length > 0) {
    parts.push(`${sectors[0]} company`);
  }

  if (deal.stage) {
    parts.push(`at ${deal.stage} stage`);
  }

  if (parts.length === 0) {
    return 'Investment opportunity in review';
  }

  return parts.join(' ') + '.';
};

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
};

export function DealCard({ deal, index, onClick, onDelete }: DealCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const { toast } = useToast();
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: easeOut,
        delay: index * 0.05
      }
    }
  };

  // Get normalized deal ID - critical for navigation
  const dealId = getDealId(deal);

  // Extract backend data with proper fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = formatSector(deal.sector);
  const stage = deal.stage;
  const website = deal.company_website;
  const status = deal.status;
  const tags = deal.tags || [];
  const founders = deal.founders || [];

  // Enhanced scoring data extraction
  const thesisScoring = deal.scoring?.thesis?.score;
  const exclusionResult = deal.exclusion_filter_result;
  const thesisMatchPercent = thesisScoring?.normalized_percent ? Math.round(thesisScoring.normalized_percent) : null;
  const coreScore = thesisScoring?.core || 0;
  const bonusScore = thesisScoring?.bonus || 0;
  const penaltyScore = thesisScoring?.penalty || 0;
  const netBonusPenalty = bonusScore - penaltyScore;
  const isExcluded = exclusionResult?.excluded || false;
  const exclusionReason = exclusionResult?.reason;

  // Market and founder scores
  const founderScore = deal.scoring?.founders?.normalized_score;
  const marketScore = deal.scoring?.market?.normalized_score;

  // Prevent navigation if no valid ID
  if (!dealId) {
    console.error('Deal card missing valid ID:', deal);
    return null;
  }

  const handleDelete = async () => {
    if (!onDelete || !dealId) return;

    setDeleting(true);
    const success = await onDelete(dealId);
    setDeleting(false);

    if (success) {
      // The parent will remove this component from the DOM.
      // We can close the dialog, but it will be unmounted anyway.
      setDeleteDialogOpen(false);
    }
    // If it fails, the dialog remains open, and the user can try again.
    // The parent component is responsible for showing the error toast.
  };

  // Generate company initials for avatar
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  const avatarColor = getAvatarColor(companyName);
  const summary = createSummary(deal);

  // Extract country from enriched_data or tags (placeholder logic)
  const country = deal.enriched_data?.country ||
                 tags.find(tag => tag.includes('Country:'))?.replace('Country:', '') ||
                 null;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group w-full"
    >
      <Card
        className={cn(
          "relative flex flex-col",
          "bg-white/95 backdrop-blur-md border border-gray-100/50",
          "rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",
          "min-h-[200px] w-full max-w-2xl mx-auto",
          "hover:bg-white/98 hover:border-gray-200/50 hover:-translate-y-1",
          "cursor-pointer"
        )}
      >
        <Link href={`/deals/${dealId}`} className="flex flex-col h-full">
          {/* Top Section: Main Deal Info */}
          <div className="flex items-start gap-4 p-6 pb-3">
            {/* Left Column with Avatar */}
            <div className="shrink-0">
              <div className={cn(
                "w-12 h-12 md:w-14 md:h-14 rounded-full",
                "bg-gradient-to-br from-pink-400 to-fuchsia-500",
                "flex items-center justify-center shadow-md",
                "ring-2 ring-white/50"
              )}>
                <span className="text-white font-bold text-lg md:text-xl">
                    {initials}
                </span>
              </div>
            </div>

            {/* Right Column with Details */}
            <div className="flex flex-col flex-1 min-w-0 gap-1.5">
              {/* Title and Status Row */}
              <div className="flex items-center gap-2 flex-wrap">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <h3 className="text-lg md:text-xl font-bold text-gray-900 tracking-tight truncate max-w-[240px] group-hover:text-blue-600 transition-colors">
                    {companyName}
                  </h3>
                    </TooltipTrigger>
                    {companyName.length > 24 && (
                      <TooltipContent>
                        <span>{companyName}</span>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
                <Badge className={cn(
                  "text-xs font-medium px-2.5 py-0.5 rounded-full",
                  "border border-current/20",
                  getStatusColor(status)
                )}>
                      {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
                </Badge>
              </div>

              {/* Stage and Sectors */}
              <div className="flex flex-wrap items-center gap-2">
                {stage && (
                  <div className="flex items-center gap-1.5">
                    <Building2 className="h-3 w-3 text-gray-500" />
                    <Badge variant="secondary" className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200 px-2.5 py-0.5 rounded-full">
                      {stage.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                )}
                {sectors.length > 0 && (
                  <div className="flex items-center gap-1.5">
                    <Tag className="h-3 w-3 text-gray-500" />
                    <div className="flex gap-1">
                      {sectors.map((sector, idx) => (
                      <Badge
                          key={idx}
                        variant="secondary"
                          className="text-xs font-medium bg-purple-50 text-purple-700 border-purple-200 px-2.5 py-0.5 rounded-full"
                      >
                          {sector.toUpperCase()}
                      </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Founders Section */}
              {founders.length > 0 && (
                <div className="flex items-center gap-2">
                  <div className="flex -space-x-2">
                    {founders.map((founder) => (
                      <TooltipProvider key={founder._id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Avatar className="w-6 h-6 border-2 border-white">
                              <AvatarFallback className={cn(
                                "text-xs font-medium",
                                getAvatarColor(founder.name)
                              )}>
                                {founder.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-xs">
                              <p className="font-medium">{founder.name}</p>
                              {founder.role && (
                                <p className="capitalize">{founder.role.join(', ')}</p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {founders.length} founder{founders.length > 1 ? 's' : ''}
                  </span>
                </div>
              )}
              </div>

            {/* Right Section: Scoring & Status */}
            <div className="flex flex-col items-end justify-start gap-2 md:min-w-[140px] shrink-0">
              {/* Menu Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                  <DropdownMenuItem
                    className="text-red-600 focus:text-red-600"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Deal
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Score Row */}
              <div className="flex items-center gap-2">
                {/* Show either Exclusion or Match+Bonus */}
                {isExcluded ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge className="bg-red-50 text-red-700 border border-red-200/50 text-sm font-semibold px-3 py-1 rounded-full flex items-center gap-1.5">
                          <Flag className="h-3.5 w-3.5" />
                          Excluded
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-xs max-w-xs">
                          <p className="font-medium">Excluded by filter</p>
                          {exclusionReason && <p className="mt-1">{exclusionReason}</p>}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <div className="flex items-center gap-2">
                    {/* Thesis Match */}
                    {typeof thesisMatchPercent === 'number' && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                            <Badge className={cn(
                              "text-sm font-semibold px-3 py-1 rounded-full flex items-center gap-1.5",
                              "border border-current/20",
                              getThesisMatchColor(thesisMatchPercent)
                            )}>
                              <Target className="h-3.5 w-3.5" />
                              {thesisMatchPercent}%
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                            <div className="text-xs space-y-1">
                              <p className="font-medium">Scoring Breakdown</p>
                              <p>Core Score: {coreScore}</p>
                              {bonusScore > 0 && <p className="text-green-600">Bonus: +{bonusScore}</p>}
                              {penaltyScore > 0 && <p className="text-red-600">Penalty: -{penaltyScore}</p>}
                              {founderScore && <p>Founder Score: {Math.round(founderScore)}%</p>}
                              {marketScore && <p>Market Score: {Math.round(marketScore)}%</p>}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                    {/* Bonus/Penalty Badge */}
                    {netBonusPenalty !== 0 && (
                      <Badge className={cn(
                        "text-sm font-medium px-2.5 py-0.5 rounded-full flex items-center gap-1.5",
                        "border border-current/20",
                        netBonusPenalty > 0
                          ? "bg-blue-50 text-blue-700 border-blue-200/50"
                          : "bg-red-50 text-red-700 border-red-200/50"
                      )}>
                        {netBonusPenalty > 0 ? (
                          <TrendingUp className="h-3.5 w-3.5" />
                        ) : (
                          <TrendingDown className="h-3.5 w-3.5" />
                        )}
                        {netBonusPenalty > 0 ? `+${netBonusPenalty}` : netBonusPenalty}
                        </Badge>
                    )}
                        </div>
                )}
              </div>

              {/* Additional Metrics */}
              <div className="flex items-center gap-2">
                {website && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-3 text-xs font-medium text-muted-foreground hover:text-primary"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.open(website.startsWith('http') ? website : `https://${website}`, '_blank');
                    }}
                  >
                    <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                    Website
                  </Button>
                )}
              </div>
            </div>
              </div>

          {/* Bottom Section: Metadata */}
          <div className="px-6 pb-6 mt-auto">
            <div className="pt-3 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center gap-4">
                {/* Creation Date */}
                <div className="flex items-center gap-1.5">
                  <Calendar className="h-3 w-3" />
                  <span>Created {formatDate(deal.created_at)}</span>
                </div>

                {/* Source - To be implemented */}
                {(deal.submission_ids || []).length > 0 && (
                  <div className="flex items-center gap-1.5">
                    <FileText className="h-3 w-3" />
                    <span>Form Submission</span>
                  </div>
                )}
              </div>

              {/* Right side metadata */}
              <div className="flex items-center gap-4">
                {website && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.open(website.startsWith('http') ? website : `https://${website}`, '_blank');
                    }}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Visit
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Link>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Deal"
        description={`Are you sure you want to delete the deal "${companyName}"? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        loading={deleting}
      />
    </motion.div>
  );
}
