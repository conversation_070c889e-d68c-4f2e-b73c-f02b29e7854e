"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Zap, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { BonusScore } from "@/lib/types/deal"
import { useMemo } from "react"

interface BonusPenaltySectionProps {
  bonusScores: Record<string, BonusScore>
  className?: string
}

const getStatusIcon = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'awarded':
      return CheckCircle
    case 'blocked':
    case 'failed':
      return XCircle
    default:
      return AlertTriangle
  }
}

// Helper to determine if this is a bonus or penalty or unmatched
const getRuleNature = (bonusData: any) => {
  const bonusPoints = typeof bonusData.points === 'number' ? bonusData.points : 0;
  if (bonusPoints > 0) return 'bonus';
  if (bonusPoints < 0) return 'penalty';
  return 'unmatched';
}

const getStatusColor = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  switch (nature) {
    case 'bonus':
      return 'bg-green-50/50 text-green-700 border-green-200';
    case 'penalty':
      return 'bg-red-50/50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200';
  }
}

const getRuleTypeIcon = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  if (nature === 'bonus') return TrendingUp;
  if (nature === 'penalty') return TrendingDown;
  return AlertTriangle;
}

const getRuleTypeColor = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  if (nature === 'bonus') return 'bg-green-100 text-green-700 border-green-200';
  if (nature === 'penalty') return 'bg-red-100 text-red-700 border-red-200';
  return 'bg-gray-100 text-gray-600 border-gray-200';
}

export function BonusPenaltySection({ bonusScores, className }: BonusPenaltySectionProps) {
  const bonusEntries = Object.entries(bonusScores)
  // Get all question_scores from the global deal context if available
  // We'll assume window.__TX_QUESTION_SCORES__ is set by the parent page, or pass as prop if needed
  const questionScores = (typeof window !== 'undefined' && (window as any).__TX_QUESTION_SCORES__) || {}

  // Helper to resolve label/type for a question_id
  const resolveQuestionMeta = (qid?: string | null) => {
    if (!qid) return { label: '', type: '' }
    // Try to find in questionScores
    const q = questionScores[qid]
    if (q) return { label: q.question_label, type: q.question_type }
    return { label: '', type: '' }
  }
  
  if (bonusEntries.length === 0) {
    return null
  }

  // Separate bonuses and penalties
  const bonuses = bonusEntries.filter(([_, score]) => score.rule_type === 'BONUS')
  const penalties = bonusEntries.filter(([_, score]) => score.rule_type === 'PENALTY')

  // Calculate totals
  const totalBonusAwarded = bonuses
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)
  
  const totalPenaltyAwarded = penalties
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className={cn("border-0 shadow-sm", visualRetreat.card.base)}>
        <CardHeader className="p-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                <Zap className="h-6 w-6 text-blue-600" />
                Bonus & Penalty Rules
              </CardTitle>
              <p className="text-sm md:text-base text-muted-foreground mt-2">
                Additional points awarded or deducted based on specific criteria
              </p>
            </div>

            <div className="flex items-center gap-3 flex-wrap">
              {totalBonusAwarded > 0 && (
                <Badge className="bg-green-100 text-green-800 border-green-200 px-3 py-2 rounded-full">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  +{totalBonusAwarded} pts
                </Badge>
              )}
              {totalPenaltyAwarded > 0 && (
                <Badge className="bg-red-100 text-red-800 border-red-200 px-3 py-2 rounded-full">
                  <TrendingDown className="h-4 w-4 mr-2" />
                  -{totalPenaltyAwarded} pts
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-6 space-y-6">
          {bonusEntries.map(([ruleId, bonusData], index) => {
            const StatusIcon = getStatusIcon(bonusData.status)
            const RuleTypeIcon = getRuleTypeIcon(bonusData)
            // Debug: log bonusData structure
            if (index === 0) { console.log('bonusData sample:', bonusData); }
            // Use bonus_points (canonical)
            const bonusPoints = typeof bonusData.points === 'number' ? bonusData.points : 0;
            const pointsSign = bonusPoints > 0 ? '+' : bonusPoints < 0 ? '-' : '';
            // Compose condition details (human readable)
            const conditionDetails = bonusData.condition
              ? Array.isArray(bonusData.condition.conditions)
                ? bonusData.condition.conditions.map((cond: any, i: number) => {
                    // Fallback: resolve label/type from question_scores if missing
                    let label = cond.question_label
                    let type = cond.question_type
                    if ((!label || label === 'Unknown Question') && cond.question_id) {
                      const meta = resolveQuestionMeta(cond.question_id)
                      label = meta.label || cond.question_id
                      type = meta.type
                    }
                    return (
                      <div key={i} className="text-xs text-gray-800 mt-1 ml-2 flex flex-wrap items-center gap-2">
                        <span className="font-semibold">Question:</span>
                        <span className="font-medium text-blue-900">{label}</span>
                        {type && (
                          <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-[11px] ml-1" title="Question Type">{type}</span>
                        )}
                        {typeof cond.value !== 'undefined' && (
                          <span className="ml-2"><span className="font-semibold">Expected:</span> <span className="text-blue-700">{JSON.stringify(cond.value)}</span></span>
                        )}
                        {typeof cond.aggregation !== 'undefined' && cond.aggregation && (
                          <span className="ml-2" title="Aggregation method"><span className="font-semibold">Aggregation:</span> <span className="text-purple-700">{cond.aggregation}</span></span>
                        )}
                        {typeof cond.aggregate_threshold !== 'undefined' && cond.aggregate_threshold !== null && (
                          <span className="ml-2" title="Threshold for aggregation"><span className="font-semibold">Threshold:</span> <span className="text-orange-700">{cond.aggregate_threshold}</span></span>
                        )}
                      </div>
                    )
                  })
                : <div className="text-xs text-gray-800 mt-1 ml-2">{JSON.stringify(bonusData.condition)}</div>
              : null
            // Compose matched answers
            const matchedAnswers = Array.isArray(bonusData.matched_answers)
              ? bonusData.matched_answers.map((m, i) => (
                  <span key={i} className={cn("inline-block px-2 py-0.5 rounded text-xs ml-1", m ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700")}>{String(m)}</span>
                ))
              : null
            return (
              <motion.div
                key={ruleId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  "flex flex-col gap-4 p-6 rounded-xl border",
                  "md:flex-row md:items-start md:gap-6",
                  getStatusColor(bonusData)
                )}
              >
                <div className="flex items-center gap-3 flex-shrink-0">
                  <div className={cn(
                    "p-3 rounded-xl border",
                    getRuleTypeColor(bonusData)
                  )}>
                    <RuleTypeIcon className="h-5 w-5" />
                  </div>
                  <StatusIcon className="h-5 w-5" />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-base md:text-lg">
                        {bonusData.rule_name || `${bonusData.rule_type || 'Rule'}`} <span className="text-xs text-gray-400 ml-2">{bonusData.rule_id}</span>
                      </h4>
                      <p className="text-sm md:text-base mt-2 leading-relaxed">
                        {bonusData.reason}
                      </p>
                      {bonusData.explanation && bonusData.explanation !== bonusData.reason && (
                        <p className="text-xs md:text-sm text-muted-foreground mt-3">
                          {bonusData.explanation}
                        </p>
                      )}
                      {/* Show question label/id if available */}
                      {bonusData.question_id && (
                        (() => {
                          const meta = resolveQuestionMeta(bonusData.question_id)
                          return (
                            <div className="text-xs text-gray-700 mt-2">
                              <span className="font-semibold">Primary Question:</span> <span className="font-medium text-blue-900">{meta.label || bonusData.question_id}</span>
                              {meta.type && (
                                <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-[11px] ml-2" title="Question Type">{meta.type}</span>
                              )}
                            </div>
                          )
                        })()
                      )}
                      {/* Show condition details */}
                      {conditionDetails}
                      {/* Show matched answers */}
                      {matchedAnswers && (
                        <div className="text-xs text-gray-700 mt-2">
                          <span className="font-semibold">Matched:</span> {matchedAnswers}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-3 md:flex-col md:items-end">
                      <Badge
                        variant="outline"
                        className={cn(
                          "text-sm font-bold px-3 py-2 rounded-full",
                          bonusData.status === 'awarded'
                            ? pointsSign === '+'
                              ? "border-green-300 text-green-700"
                              : pointsSign === '-'
                                ? "border-red-300 text-red-700"
                                : "border-gray-300 text-gray-600"
                            : "border-gray-300 text-gray-600"
                        )}
                      >
                        {pointsSign}{Math.abs(bonusPoints)} pts
                      </Badge>

                      <Badge
                        variant="secondary"
                        className="text-sm capitalize px-3 py-1 rounded-full"
                      >
                        {bonusData.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
          
          {/* Summary */}
          {(totalBonusAwarded > 0 || totalPenaltyAwarded > 0) && (
            <div className="pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between text-base md:text-lg">
                <span className="font-medium text-gray-700">Net Impact:</span>
                <span className={cn(
                  "font-bold text-lg md:text-xl",
                  (totalBonusAwarded - totalPenaltyAwarded) > 0
                    ? "text-green-600"
                    : (totalBonusAwarded - totalPenaltyAwarded) < 0
                      ? "text-red-600"
                      : "text-gray-600"
                )}>
                  {totalBonusAwarded - totalPenaltyAwarded > 0 ? '+' : ''}
                  {totalBonusAwarded - totalPenaltyAwarded} points
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
