"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { XCircle, CheckCircle, Shield, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { ExclusionFilterResult } from "@/lib/types/deal"

interface ExclusionDisplayProps {
  exclusionResult: ExclusionFilterResult
  className?: string
}

export function ExclusionDisplay({ exclusionResult, className }: ExclusionDisplayProps) {
  const isExcluded = exclusionResult.excluded

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Alert className={cn(
        "border-2 rounded-2xl shadow-sm",
        visualRetreat.card.base,
        isExcluded
          ? "border-red-200 bg-red-50/80 backdrop-blur-sm"
          : "border-green-200 bg-green-50/80 backdrop-blur-sm"
      )}>
        <div className="flex items-start gap-4 p-6">
          {isExcluded ? (
            <XCircle className="h-6 w-6 md:h-8 md:w-8 text-red-600 mt-1 flex-shrink-0" />
          ) : (
            <CheckCircle className="h-6 w-6 md:h-8 md:w-8 text-green-600 mt-1 flex-shrink-0" />
          )}

          <div className="flex-1 space-y-4">
            <div>
              <h3 className={cn(
                "text-lg md:text-xl font-bold leading-tight",
                isExcluded ? "text-red-900" : "text-green-900"
              )}>
                {isExcluded ? "Excluded from Thesis" : "Passed Exclusion Filters"}
              </h3>

              {exclusionResult.filter_name && (
                <p className={cn(
                  "text-sm md:text-base font-medium mt-2",
                  isExcluded ? "text-red-700" : "text-green-700"
                )}>
                  Filter: {exclusionResult.filter_name}
                </p>
              )}
            </div>

            {exclusionResult.reason && (
              <div className={cn(
                "p-4 rounded-xl border",
                isExcluded
                  ? "bg-red-100 border-red-200 text-red-800"
                  : "bg-green-100 border-green-200 text-green-800"
              )}>
                <p className="text-sm md:text-base font-medium mb-2">
                  {isExcluded ? "Exclusion Reason:" : "Filter Status:"}
                </p>
                <p className="text-sm md:text-base leading-relaxed">{exclusionResult.reason}</p>
              </div>
            )}

            {isExcluded && (
              <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm md:text-base">
                  <p className="font-medium text-amber-800 mb-1">Impact on Scoring</p>
                  <p className="text-amber-700 leading-relaxed">
                    This deal has been excluded by the filter and will not show thesis match scores.
                    All scoring analysis is hidden for excluded deals.
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3 flex-wrap">
              <Badge
                variant={isExcluded ? "destructive" : "default"}
                className={cn(
                  "text-sm font-semibold px-3 py-2 rounded-full",
                  isExcluded
                    ? "bg-red-100 text-red-800 border-red-200"
                    : "bg-green-100 text-green-800 border-green-200"
                )}
              >
                <Shield className="h-4 w-4 mr-2" />
                {isExcluded ? "Excluded" : "Approved"}
              </Badge>

              {exclusionResult.filter_id && (
                <Badge variant="outline" className="text-sm px-3 py-1 rounded-full">
                  ID: {exclusionResult.filter_id}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </Alert>
    </motion.div>
  )
}
