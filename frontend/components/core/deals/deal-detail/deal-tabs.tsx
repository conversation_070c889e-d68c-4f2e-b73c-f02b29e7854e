"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { AnalystResearchTab } from "./analyst-research-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import {
  Clock,
  Target,
  Users,
  Radio,
  Search,
  FileText,
  BarChart3,
  Star
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'research',
    label: 'Analyst Research',
    icon: Search,
    component: AnalystResearchTab
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  },
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  }
]

export function DealTabs({ deal, activeTab, onTabChange }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'research':
        // Count available research components
        const research = deal.analyst_research
        if (!research) return 0
        let count = 0
        if (research.competitors?.competitors?.length) count++
        if (research.market?.market_trends?.length) count++
        if (research.news?.news_signals?.length) count++
        if (research.summary?.executive_summary) count++
        return count
      case 'documents':
        return deal.documents?.length || 0
      default:
        return null
    }
  }

  return (
    <div className="w-full h-full flex flex-col">
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full h-full flex flex-col">
        {/* Full-width tab navigation with perfect mobile scrolling */}
        <div className="w-full border-b border-gray-200/60 bg-white sticky top-0 z-10">
          <div className="w-full overflow-x-auto scrollbar-hide">
            <TabsList className="w-full min-w-max flex h-auto p-2 bg-transparent border-0 rounded-none gap-1">
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const count = getTabCount(tab.id)

                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    disabled={tab.disabled}
                    className={cn(
                      // Premium mobile-first tab design
                      "flex items-center justify-center gap-3 py-4 px-6 min-w-[140px] flex-shrink-0",
                      "rounded-xl transition-all duration-300 ease-out",
                      "text-gray-600 hover:text-gray-900 hover:bg-gray-100/80",
                      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      // Active state with glassmorphism
                      "data-[state=active]:text-blue-600 data-[state=active]:bg-white",
                      "data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-blue-200/50",
                      // Mobile optimizations - proper touch targets
                      "touch-manipulation min-h-[48px]",
                      // Ensure better mobile scrolling
                      "scroll-ml-4 first:scroll-ml-0"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="text-sm font-medium whitespace-nowrap">{tab.label}</span>

                    {count !== null && count > 0 && (
                      <Badge
                        variant="secondary"
                        className="h-5 min-w-[20px] text-xs px-1.5 rounded-full bg-gray-100 text-gray-700"
                      >
                        {count}
                      </Badge>
                    )}

                    {tab.disabled && (
                      <Badge
                        variant="outline"
                        className="h-5 text-xs px-2 text-gray-400 border-gray-300 rounded-full"
                      >
                        Soon
                      </Badge>
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>
        </div>

        {/* Full-screen tab content */}
        <div className="flex-1 w-full">
          {tabs.map((tab) => {
            const Component = tab.component

            return (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="mt-0 focus-visible:outline-none w-full h-full data-[state=active]:flex data-[state=active]:flex-col"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex-1 w-full"
                >
                  <div className="w-full h-full px-8 py-8">
                    <Component deal={deal} />
                  </div>
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </div>
  )
}
