"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  ChevronRight, 
  ChevronDown, 
  ChevronUp,
  FileText, 
  ToggleLeft, 
  ToggleRight, 
  Hash, 
  Calendar, 
  Target,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Zap,
  Users
} from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { QuestionScore, InstanceScore } from "@/lib/types/deal"

interface QuestionBreakdownProps {
  questionScores: Record<string, QuestionScore>
  className?: string
}

const getQuestionTypeIcon = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return FileText
    case 'multi_select':
    case 'single_select':
      return ToggleLeft
    case 'boolean':
      return ToggleRight
    case 'number':
      return Hash
    case 'date':
      return Calendar
    default:
      return Target
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'multi_select':
    case 'single_select':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'boolean':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    case 'number':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'date':
      return 'bg-pink-100 text-pink-700 border-pink-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
  if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  return 'text-red-600 bg-red-50 border-red-200'
}

const getScoreIcon = (score: number) => {
  if (score >= 0.8) return CheckCircle
  if (score >= 0.5) return AlertTriangle
  return XCircle
}

const formatScore = (score: number) => {
  return `${(score * 100).toFixed(1)}%`
}

const formatValue = (value: any) => {
  if (Array.isArray(value)) return value.join(', ')
  if (typeof value === 'boolean') return value ? 'Yes' : 'No'
  return String(value)
}

export function QuestionBreakdown({ questionScores, className }: QuestionBreakdownProps) {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set())
  const [showAllQuestions, setShowAllQuestions] = useState(false)

  const questions = Object.entries(questionScores)
  const shouldCollapse = questions.length > 5
  const questionsToShow = shouldCollapse && !showAllQuestions
    ? questions.slice(0, 5)
    : questions

  const toggleQuestionExpansion = (questionId: string) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {questionsToShow.map(([questionId, scoreData], index) => {
        const Icon = getQuestionTypeIcon(scoreData.question_type)
        const ScoreIcon = getScoreIcon(scoreData.raw_score)
        const isExpanded = expandedQuestions.has(questionId)
        const hasInstances = scoreData.is_repeatable && scoreData.instances && scoreData.instances.length > 0

        return (
          <motion.div
            key={questionId}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Card
              className={cn(
                "border border-gray-200 transition-all duration-200 cursor-pointer rounded-xl",
                "hover:shadow-md hover:border-gray-300",
                visualRetreat.card.base,
                isExpanded && "shadow-md border-blue-200 bg-blue-50/30"
              )}
              onClick={() => toggleQuestionExpansion(questionId)}
            >
              <CardContent className="p-6 md:p-8">
                {/* Question Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                  <div className="flex flex-1 items-start gap-4 min-w-0">
                    <div className={cn(
                      "rounded-xl border p-3 flex-shrink-0",
                      getQuestionTypeColor(scoreData.question_type)
                    )}>
                      <Icon className="h-5 w-5" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-3">
                        <h4 className="text-lg md:text-xl font-semibold text-gray-900 leading-tight">
                          {scoreData.question_label || `Question ${questionId}`}
                        </h4>
                        <motion.div
                          animate={{ rotate: isExpanded ? 90 : 0 }}
                          transition={{ duration: 0.2 }}
                          className="flex-shrink-0 md:hidden"
                        >
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        </motion.div>
                      </div>

                      <div className="mt-3 flex items-center gap-3 flex-wrap">
                        <Badge variant="outline" className="text-sm px-3 py-1 rounded-full">
                          {scoreData.question_type.replace('_', ' ')}
                        </Badge>
                        <Badge variant="secondary" className="text-sm px-3 py-1 rounded-full">
                          Weight: {scoreData.weight}
                        </Badge>
                        {scoreData.ai_generated && (
                          <Badge className="bg-purple-100 text-purple-700 border-purple-200 text-sm px-3 py-1 rounded-full">
                            <Zap className="h-4 w-4 mr-2" />
                            AI
                          </Badge>
                        )}
                        {hasInstances && (
                          <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-sm px-3 py-1 rounded-full">
                            <Users className="h-4 w-4 mr-2" />
                            {scoreData.instances!.length} instances
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between md:justify-end gap-4">
                    <div className={cn(
                      "flex items-center gap-3 rounded-xl border px-4 py-3 font-semibold",
                      getScoreColor(scoreData.raw_score)
                    )}>
                      <ScoreIcon className="h-5 w-5" />
                      <span className="text-lg">{formatScore(scoreData.raw_score)}</span>
                    </div>
                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="hidden md:block flex-shrink-0"
                    >
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </motion.div>
                  </div>
                </div>

                {/* Expandable Content */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="mt-6 space-y-6 pt-6 border-t border-gray-200">
                        {/* Explanation */}
                        <div className="rounded-xl bg-gray-50 p-4 md:p-6">
                          <p className="text-sm md:text-base leading-relaxed text-gray-700">
                            {scoreData.explanation || 'No explanation available'}
                          </p>
                        </div>

                        {/* Instances for Repeatable Sections */}
                        {hasInstances && (
                          <div className="space-y-4">
                            <h5 className="font-medium text-base md:text-lg text-gray-900">
                              {scoreData.instances!.length === 1 ? 'Single Instance:' : 'Instance Breakdown:'}
                            </h5>
                            {scoreData.instances!.length === 1 ? (
                              // Single instance - simplified display
                              <div className={cn(
                                "p-4 md:p-6 rounded-xl border",
                                scoreData.instances![0].matched
                                  ? "bg-green-50 border-green-200 text-green-800"
                                  : "bg-red-50 border-red-200 text-red-800"
                              )}>
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <span className="font-medium text-base">
                                        Value: {formatValue(scoreData.instances![0].value)}
                                      </span>
                                    </div>
                                    {scoreData.instances![0].explanation && (
                                      <p className="text-sm opacity-80 leading-relaxed">
                                        {scoreData.instances![0].explanation}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-3">
                                    {scoreData.instances![0].matched ? (
                                      <CheckCircle className="h-5 w-5" />
                                    ) : (
                                      <XCircle className="h-5 w-5" />
                                    )}
                                    <span className="font-bold text-lg">{scoreData.instances![0].score}</span>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              // Multiple instances - full breakdown
                              <div className="space-y-4">
                                {scoreData.instances!.map((instance, idx) => (
                                  <div
                                    key={idx}
                                    className={cn(
                                      "flex flex-col gap-3 p-4 md:p-6 rounded-xl border",
                                      "md:flex-row md:items-center md:justify-between",
                                      instance.matched
                                        ? "bg-green-50 border-green-200 text-green-800"
                                        : "bg-red-50 border-red-200 text-red-800"
                                    )}
                                  >
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2 mb-2">
                                        <span className="font-medium text-base">
                                          {scoreData.question_type.includes('founder') ? `Founder ${idx + 1}` : `Instance ${idx + 1}`}:
                                        </span>
                                        <span className="text-sm md:text-base">{formatValue(instance.value)}</span>
                                      </div>
                                      {instance.explanation && (
                                        <p className="text-sm opacity-80 leading-relaxed">{instance.explanation}</p>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-3 justify-between md:justify-end">
                                      {instance.matched ? (
                                        <CheckCircle className="h-5 w-5" />
                                      ) : (
                                        <XCircle className="h-5 w-5" />
                                      )}
                                      <span className="font-bold text-lg">{instance.score}</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Score Details */}
                        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between border-t pt-4 text-sm md:text-base text-gray-500">
                          <span>Weighted Score: {scoreData.weighted_score.toFixed(2)}</span>
                          {scoreData.aggregation_used && (
                            <span>Aggregation: {scoreData.aggregation_type}</span>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        )
      })}

      {/* Show More/Less Button */}
      {shouldCollapse && (
        <div className="flex justify-center pt-6">
          <Button
            variant="outline"
            onClick={() => setShowAllQuestions(!showAllQuestions)}
            className="gap-3 px-6 py-3 rounded-xl text-base font-medium"
          >
            {showAllQuestions ? (
              <>
                <ChevronUp className="h-5 w-5" />
                Show Less ({questions.length - 5} hidden)
              </>
            ) : (
              <>
                <ChevronDown className="h-5 w-5" />
                Show All Questions ({questions.length - 5} more)
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
