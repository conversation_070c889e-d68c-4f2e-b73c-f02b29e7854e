"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  MessageSquare,
  Send,
  Bot,
  User,
  Minimize2,
  Maximize2,
  Loader2,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { ChatMessage } from "@/lib/types/deal-detail"
import { useToast } from "@/components/ui/use-toast"
import { 
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet"

interface AiChatProps {
  dealId: string
  isMobile?: boolean
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

const suggestedQuestions = [
  "What are the key risks for this investment?",
  "Summarize the founder backgrounds",
  "How does this score compare to others?",
  "What external signals are most important?",
  "Analyze the market opportunity"
]

const quickActions = [
  {
    label: "Create Memo",
    icon: "📝",
    prompt: "Generate a comprehensive investment memo for this deal including executive summary, key strengths, risks, and recommendation."
  },
  {
    label: "Generate Diligence Checklist",
    icon: "✅",
    prompt: "Create a detailed due diligence checklist tailored to this specific deal and sector."
  },
  {
    label: "Compare to Portfolio",
    icon: "📊",
    prompt: "How does this deal compare to similar investments in our portfolio? What are the key differentiators?"
  }
]

export function AiChat({ dealId, isMobile = false }: AiChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    // Load chat history
    const loadChatHistory = async () => {
      try {
        const history = await DealDetailAPI.getChatHistory(dealId)
        setMessages(history)
      } catch (error) {
        console.error('Error loading chat history:', error)
      }
    }

    loadChatHistory()
  }, [dealId])

  useEffect(() => {
    // Scroll to bottom when new messages are added
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = inputValue.trim()
    setInputValue("")
    setIsLoading(true)

    // Add user message immediately
    const newUserMessage: ChatMessage = {
      id: Math.random().toString(36),
      message: userMessage,
      response: "",
      timestamp: new Date().toISOString(),
      user_id: "current_user"
    }

    setMessages(prev => [...prev, newUserMessage])

    try {
      const response = await DealDetailAPI.sendChatMessage(dealId, {
        message: userMessage,
        agent_type: "deal_analysis"
      })

      // Add AI response
      const aiMessage: ChatMessage = {
        id: response.message_id,
        message: userMessage,
        response: response.response,
        timestamp: response.timestamp,
        user_id: "ai_assistant"
      }

      setMessages(prev => [...prev.slice(0, -1), aiMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Message failed",
        description: "There was an error sending your message. Please try again.",
        variant: "destructive"
      })
      
      // Remove the user message on error
      setMessages(prev => prev.slice(0, -1))
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestedQuestion = (question: string) => {
    setInputValue(question)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const ChatContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <Zap className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-sm">AI Assistant</h3>
            <p className="text-xs text-muted-foreground">Ask about this deal</p>
          </div>
        </div>
        
        {!isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 && (
              <div className="text-center space-y-4">
                <div className="w-12 h-12 rounded-full bg-muted mx-auto flex items-center justify-center">
                  <Bot className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-sm font-medium">Hi! I'm your AI assistant</p>
                  <p className="text-xs text-muted-foreground">
                    Ask me anything about this deal
                  </p>
                </div>
              </div>
            )}

            {messages.map((message) => (
              <div key={message.id} className="space-y-3">
                {/* User Message */}
                <div className="flex items-start gap-3 justify-end">
                  <div className="bg-primary text-primary-foreground rounded-lg px-3 py-2 max-w-[80%]">
                    <p className="text-sm">{message.message}</p>
                  </div>
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs">
                      <User className="h-3 w-3" />
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* AI Response */}
                {message.response && (
                  <div className="flex items-start gap-3">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                        <Bot className="h-3 w-3" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-muted rounded-lg px-3 py-2 max-w-[80%]">
                      <p className="text-sm">{message.response}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex items-start gap-3">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                    <Bot className="h-3 w-3" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-3 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span className="text-xs text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Quick Actions & Suggested Questions */}
          {messages.length === 0 && (
            <div className="p-4 border-t space-y-4">
              {/* Quick Actions */}
              <div className="space-y-2">
                <p className="text-xs text-muted-foreground font-medium">Quick Actions:</p>
                <div className="space-y-1">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => handleSuggestedQuestion(action.prompt)}
                      className="w-full justify-start text-xs h-auto py-2 px-3 gap-2"
                    >
                      <span>{action.icon}</span>
                      {action.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Suggested Questions */}
              <div className="space-y-2">
                <p className="text-xs text-muted-foreground font-medium">Try asking:</p>
                <div className="space-y-1">
                  {suggestedQuestions.slice(0, 3).map((question, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSuggestedQuestion(question)}
                      className="w-full justify-start text-xs h-auto py-2 px-2"
                    >
                      {question}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask about this deal..."
                className="min-h-[40px] max-h-[120px] resize-none"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                size="sm"
                className="px-3"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  )

  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button className="fixed bottom-4 right-4 rounded-full w-14 h-14 shadow-lg">
            <MessageSquare className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent className="h-[80vh]">
          <SheetHeader>
            <SheetTitle>AI Assistant</SheetTitle>
          </SheetHeader>
          <div className="h-full mt-4">
            <ChatContent />
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <Card className="h-[600px] flex flex-col">
      <ChatContent />
    </Card>
  )
}
