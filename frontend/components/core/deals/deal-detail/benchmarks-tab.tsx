"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Users,
  DollarSign,
  Calendar,
  Bell
} from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"

interface BenchmarksTabProps {
  deal: DealDetailData
}

export function BenchmarksTab({ deal }: BenchmarksTabProps) {
  const handleNotifyMe = () => {
    // TODO: Implement notification signup
    console.log('Notify me when benchmarks are available')
  }

  return (
    <div className="space-y-8">
      {/* Coming Soon Hero */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="border-dashed border-2">
          <CardContent className="p-12 text-center">
            <div className="space-y-6">
              <div className="flex justify-center">
                <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center">
                  <BarChart3 className="h-10 w-10 text-muted-foreground" />
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2">
                  <h2 className="text-2xl font-bold">Benchmarks</h2>
                  <Badge variant="outline" className="text-xs">
                    Coming Soon
                  </Badge>
                </div>
                
                <p className="text-muted-foreground max-w-md mx-auto">
                  Compare this deal against industry benchmarks, similar companies, and market standards. 
                  Get insights on valuation, growth metrics, and competitive positioning.
                </p>
              </div>

              <Button onClick={handleNotifyMe} className="gap-2">
                <Bell className="h-4 w-4" />
                Notify Me When Available
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Preview Features */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">What's Coming</h3>
        
        <div className="grid gap-4 md:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                    </div>
                    <h4 className="font-semibold">Market Comparisons</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Compare valuation, growth rates, and key metrics against similar companies in the same stage and sector.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
                      <Target className="h-5 w-5 text-green-600" />
                    </div>
                    <h4 className="font-semibold">Performance Metrics</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Track key performance indicators and see how they stack up against industry standards and top performers.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center">
                      <Users className="h-5 w-5 text-purple-600" />
                    </div>
                    <h4 className="font-semibold">Team Analysis</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Benchmark founder experience, team size, and composition against successful companies at similar stages.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-orange-100 flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-orange-600" />
                    </div>
                    <h4 className="font-semibold">Financial Benchmarks</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Compare revenue growth, burn rate, runway, and other financial metrics against industry benchmarks.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Timeline */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-semibold">Development Timeline</h4>
              </div>
              
              <div className="space-y-3 pl-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Q1 2024 - Market Comparison Engine</p>
                    <p className="text-xs text-muted-foreground">
                      Compare deals against similar companies and market standards
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-gray-300" />
                  <div>
                    <p className="text-sm font-medium">Q2 2024 - Performance Analytics</p>
                    <p className="text-xs text-muted-foreground">
                      Deep dive into financial and operational metrics
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-gray-300" />
                  <div>
                    <p className="text-sm font-medium">Q3 2024 - Predictive Insights</p>
                    <p className="text-xs text-muted-foreground">
                      AI-powered predictions and trend analysis
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
