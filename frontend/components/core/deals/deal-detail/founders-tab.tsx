"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Linkedin, 
  Twitter, 
  Github, 
  ExternalLink,
  Star,
  Building,
  Award
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface FoundersTabProps {
  deal: DealDetailData
}

const getScoreColor = (score?: number) => {
  if (!score) return 'text-muted-foreground'
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBackground = (score?: number) => {
  if (!score) return 'bg-gray-50'
  if (score >= 80) return 'bg-green-50'
  if (score >= 60) return 'bg-yellow-50'
  return 'bg-red-50'
}

export function FoundersTab({ deal }: FoundersTabProps) {
  const founders = deal.founders || []

  if (founders.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="users" />
        <EmptyPlaceholder.Title>No founder data available for this deal</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Founder profiles will appear here once they're added to the deal.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  // Helper function to display field or "Not Found"
  const displayField = (value: any): string => {
    if (value === null || value === undefined || value === '') {
      return 'Not Found'
    }
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(', ') : 'Not Found'
    }
    return String(value).toWellFormed()
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className="space-y-6">
      {/* Founders Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {founders.map((founder, index) => (
          <motion.div
            key={founder._id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={founder.profile_picture ?? undefined} alt={founder.name || 'Founder'} />
                      <AvatarFallback className="text-lg">
                        {founder.name ? founder.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'F'}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div>
                          <h3 className="font-semibold text-lg">{displayField(founder.name)}</h3>
                          {/* <p className="text-sm text-muted-foreground">{displayField(founder.role || founder.title)}</p> */}
                          {(() => {
                            const roleOrTitle = founder.role;
                            if (Array.isArray(roleOrTitle)) {
                              return roleOrTitle.filter(Boolean)
                                .map((r, idx) => (
                                  <span key={idx}>
                                    {String(r).toUpperCase()}
                                    {idx < roleOrTitle.length - 1 ? ', ' : ''}
                                  </span>
                                ));
                            } else if (roleOrTitle) {
                              return String(roleOrTitle).toUpperCase();
                            }
                            return null;
                          })()}
                          <p className="text-sm text-muted-foreground">{}</p>

                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Founder Details */}
                  <div className="space-y-3">
                    {/* LinkedIn */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">LinkedIn:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.linkedin)}</span>
                    </div>

                    {/* Email */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Email:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.email)}</span>
                    </div>

                    {/* Serial Founder */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Serial Founder:</span>
                      <span className="text-sm text-muted-foreground">
                        {founder.serial_founder !== undefined ? (founder.serial_founder ? 'Yes' : 'No') : 'Not Found'}
                      </span>
                    </div>

                    {/* Experience */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Experience:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.experience)}</span>
                    </div>

                    {/* Skills */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Skills:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.skills)}</span>
                    </div>

                    {/* Education */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Education:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.education)}</span>
                    </div>

                    {/* Achievements */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Achievements:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.achievements)}</span>
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="flex items-center gap-2 pt-2 border-t">
                    {founder.linkedin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSocialClick(founder.linkedin!)}
                        className="gap-2"
                      >
                        <Linkedin className="h-4 w-4" />
                        LinkedIn
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Team Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Team Summary</h3>
            
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 rounded-lg bg-muted/50">
                <div className="text-2xl font-bold">{founders.length}</div>
                <div className="text-sm text-muted-foreground">
                  Founder{founders.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
