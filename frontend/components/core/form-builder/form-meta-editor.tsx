"use client"

import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import { FormWithDetails } from '@/lib/types/form';

const formMetaSchema = z.object({
  name: z.string().min(1, 'Form name is required').max(100, 'Form name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters'),
  is_active: z.boolean(),
});

type FormMetaData = z.infer<typeof formMetaSchema>;

interface FormMetaEditorProps {
  form: FormWithDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: FormMetaData) => Promise<void>;
  isSaving: boolean;
}

export function FormMetaEditor({
  form,
  open,
  onOpenChange,
  onSave,
  isSaving
}: FormMetaEditorProps) {
  const formMethods = useForm<FormMetaData>({
    resolver: zodResolver(formMetaSchema),
    defaultValues: {
      name: form.name,
      description: form.description,
      is_active: form.is_active,
    },
  });

  // Reset form when dialog opens or form changes
  useEffect(() => {
    if (open) {
      formMethods.reset({
        name: form.name,
        description: form.description,
        is_active: form.is_active,
      });
    }
  }, [open, form, formMethods]);

  const handleSubmit = async (data: FormMetaData) => {
    try {
      await onSave(data);
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Error saving form metadata:', error);
    }
  };

  const handleCancel = () => {
    formMethods.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Form Settings</DialogTitle>
          <DialogDescription>
            Update the form name, description, and status.
          </DialogDescription>
        </DialogHeader>

        <Form {...formMethods}>
          <form onSubmit={formMethods.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Form Name */}
            <FormField
              control={formMethods.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Form Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter form name..."
                      {...field}
                      disabled={isSaving}
                    />
                  </FormControl>
                  <FormDescription>
                    A clear, descriptive name for your form.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Description */}
            <FormField
              control={formMethods.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter form description..."
                      className="min-h-[100px]"
                      {...field}
                      disabled={isSaving}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe the purpose and content of this form.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Active Status */}
            <FormField
              control={formMethods.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <FormDescription>
                      Active forms can receive submissions. Draft forms are hidden from users.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isSaving}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Form Info */}
            <div className="rounded-lg border p-4 bg-muted/50">
              <h4 className="text-sm font-medium mb-2">Form Information</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span>{form.version}</span>
                </div>
                <div className="flex justify-between">
                  <span>Sections:</span>
                  <span>{form.sections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Questions:</span>
                  <span>{form.sections.reduce((total, section) => total + section.questions.length, 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Created:</span>
                  <span>{form.created_at ? new Date(form.created_at * 1000).toLocaleDateString() : 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Last Updated:</span>
                  <span>{form.updated_at ? new Date(form.updated_at * 1000).toLocaleDateString() : 'N/A'}</span>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSaving || !formMethods.formState.isDirty}
              >
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
