"use client"

import React from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  defaultDropAnimationSideEffects,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  rectSortingStrategy,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface SortableContextProviderProps<T> {
  items: T[];
  itemIds: string[];
  onDragEnd: (event: DragEndEvent) => void;
  children: ReactNode;
  overlayContent?: ReactNode;
  strategy?: 'vertical' | 'horizontal';
  className?: string;
}

export function SortableContextProvider<T>({
  items,
  itemIds,
  onDragEnd,
  children,
  overlayContent,
  strategy = 'vertical',
  className,
}: SortableContextProviderProps<T>) {
  const [activeId, setActiveId] = React.useState<string | null>(null);
  const [itemsState, setItemsState] = React.useState(items);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250, // 250ms delay before drag starts on touch
        tolerance: 5, // 5px movement tolerance
      },
    })
  );

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = itemIds.indexOf(active.id as string);
      const newIndex = itemIds.indexOf(over.id as string);

      const newItems = arrayMove(itemsState, oldIndex, newIndex);
      setItemsState(newItems);
      onDragEnd(event);
    }

    setActiveId(null);
  };

  // Drop animation configuration
  const dropAnimation = {
    sideEffects: defaultDropAnimationSideEffects({
      styles: {
        active: {
          opacity: '0.5',
        },
      },
    }),
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={itemIds}
        strategy={strategy === 'vertical' ? verticalListSortingStrategy : rectSortingStrategy}
      >
        <div className={cn("space-y-4", className)}>
          {children}
        </div>
      </SortableContext>

      <DragOverlay dropAnimation={dropAnimation}>
        {activeId && overlayContent}
      </DragOverlay>
    </DndContext>
  );
}

interface SortableItemProps {
  id: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function SortableItem({ id, children, className, disabled }: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id, disabled });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isDragging && "z-50",
        className
      )}
      {...attributes}
      {...listeners}
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.div>
  );
}

// Reusable drag handle component
export function SortableDragHandle({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        "flex items-center justify-center w-8 h-full cursor-grab active:cursor-grabbing",
        "bg-muted/30 border-r border-border/50 transition-all duration-200",
        "hover:bg-muted/50 hover:border-border",
        className
      )}
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-muted-foreground"
      >
        <path
          d="M4 6H12M4 10H12"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>
    </div>
  );
} 