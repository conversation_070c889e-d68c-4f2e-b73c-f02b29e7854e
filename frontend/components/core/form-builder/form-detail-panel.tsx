"use client"

import React, { useState } from 'react';
import { X, FileText, Layers, HelpCircle, Eye, Settings, Save, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { FormWithDetails, Section, Question, QuestionType, QUESTION_TYPES, isCoreFieldQuestion, CoreFieldType } from '@/lib/types/form';
import { VisibilityEditor } from './visibility-editor';
import { QuestionConfig } from './question-config';
import { ExclusionFilterBlock } from './exclusion-filter';
import { FormSharingPanel } from './form-sharing-panel';

interface FormDetailPanelProps {
  form: FormWithDetails;
  selectedItem: { type: 'form' | 'section' | 'question'; id: string } | null;
  onUpdateForm: (updates: any) => void;
  onUpdateSection: (sectionId: string, updates: any) => void;
  onUpdateQuestion: (questionId: string, updates: any) => void;
  onClose: () => void;
}

export function FormDetailPanel({
  form,
  selectedItem,
  onUpdateForm,
  onUpdateSection,
  onUpdateQuestion,
  onClose
}: FormDetailPanelProps) {
  const [localChanges, setLocalChanges] = useState<any>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Get the selected item data
  const getSelectedData = () => {
    if (!selectedItem) return null;

    switch (selectedItem.type) {
      case 'form':
        return form;
      case 'section':
        return form.sections.find(s => (s._id || s.id) === selectedItem.id);
      case 'question':
        for (const section of form.sections) {
          const question = section.questions.find(q => (q._id || q.id) === selectedItem.id);
          if (question) return question;
        }
        return null;
      default:
        return null;
    }
  };

  const selectedData = getSelectedData();

  // Validate core fields are present
  const validateCoreFields = () => {
    const allQuestions = form.sections.flatMap(section => section.questions);
    const coreFields = {
      [CoreFieldType.COMPANY_NAME]: false,
      [CoreFieldType.STAGE]: false,
      [CoreFieldType.SECTOR]: false,
    };

    allQuestions.forEach(question => {
      if (question.core_field) {
        coreFields[question.core_field] = true;
      }
    });

    return {
      isValid: Object.values(coreFields).every(Boolean),
      missing: Object.entries(coreFields)
        .filter(([_, present]) => !present)
        .map(([field, _]) => field)
    };
  };

  const coreFieldValidation = validateCoreFields();

  const handleLocalChange = (field: string, value: any) => {
    // Prevent activating form without core fields
    if (field === 'is_active' && value === true && !coreFieldValidation.isValid) {
      alert(`Cannot activate form. Missing core fields: ${coreFieldValidation.missing.join(', ')}`);
      return;
    }
    
    setLocalChanges(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  const handleSave = () => {
    if (!selectedItem || !hasUnsavedChanges) return;

    switch (selectedItem.type) {
      case 'form':
        onUpdateForm(localChanges);
        break;
      case 'section':
        onUpdateSection(selectedItem.id, localChanges);
        break;
      case 'question':
        onUpdateQuestion(selectedItem.id, localChanges);
        break;
    }

    setLocalChanges({});
    setHasUnsavedChanges(false);
  };

  const renderFormDetails = () => (
    <div className="space-y-6">
      {/* Basic Form Settings Card */}
      <Card className="rounded-xl shadow-sm bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-3 text-gray-900">
            <FileText className="h-5 w-5 text-gray-700" />
            Basic Information
          </CardTitle>
          <CardDescription className="text-sm text-gray-600 leading-relaxed">
            Configure basic form information and settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <Label htmlFor="form-name" className="text-sm font-medium">Form Name</Label>
            <Input
              id="form-name"
              value={localChanges.name ?? form.name}
              onChange={(e) => handleLocalChange('name', e.target.value)}
              placeholder="Enter form name"
              className="w-full min-h-12 mt-2 mb-4 leading-relaxed"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="form-description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="form-description"
              value={localChanges.description ?? form.description}
              onChange={(e) => handleLocalChange('description', e.target.value)}
              placeholder="Describe what this form is for"
              className="min-h-[120px] resize-none mt-2 mb-4 leading-relaxed"
            />
          </div>

          {!coreFieldValidation.isValid && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-900">
                  Missing Core Fields
                </span>
              </div>
              <p className="text-xs text-amber-700 mb-2">
                This form is missing required core dashboard fields: {coreFieldValidation.missing.join(', ')}
              </p>
              <p className="text-xs text-amber-600">
                Core fields are automatically created in new forms. If missing, please create a new form or contact support.
              </p>
            </div>
          )}

          <div className="flex items-center justify-between p-6 rounded-lg border border-gray-200 bg-gray-50">
            <div className="space-y-1">
              <Label className="text-sm font-medium text-gray-900">Active Status</Label>
              <p className="text-xs text-gray-600 leading-relaxed">
                Active forms can receive submissions
                {!coreFieldValidation.isValid && (
                  <span className="block text-amber-600 mt-1">
                    Cannot activate without core fields
                  </span>
                )}
              </p>
            </div>
            <Switch
              checked={localChanges.is_active ?? form.is_active}
              onCheckedChange={(checked) => handleLocalChange('is_active', checked)}
              disabled={!coreFieldValidation.isValid && (localChanges.is_active ?? form.is_active) === false}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Statistics Card */}
      <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-3">
            <Settings className="h-5 w-5 text-primary" />
            Form Statistics
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground leading-relaxed">
            Overview of form structure and metadata
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6 text-sm">
            <div className="space-y-2 p-4 rounded-lg bg-muted/30">
              <span className="text-muted-foreground text-xs uppercase tracking-wide">Sections</span>
              <p className="font-semibold text-lg">{form.sections.length}</p>
            </div>
            <div className="space-y-2 p-4 rounded-lg bg-muted/30">
              <span className="text-muted-foreground text-xs uppercase tracking-wide">Questions</span>
              <p className="font-semibold text-lg">
                {form.sections.reduce((total, section) => total + section.questions.length, 0)}
              </p>
            </div>
            <div className="space-y-2 p-4 rounded-lg bg-muted/30">
              <span className="text-muted-foreground text-xs uppercase tracking-wide">Version</span>
              <p className="font-semibold text-lg">{form.version}</p>
            </div>
            <div className="space-y-2 p-4 rounded-lg bg-muted/30">
              <span className="text-muted-foreground text-xs uppercase tracking-wide">Created</span>
              <p className="font-semibold text-lg">
                {form.created_at ? new Date(form.created_at * 1000).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exclusion Filters Card */}
      <Card className="rounded-xl shadow-sm bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-3 text-gray-900">
            <Eye className="h-5 w-5 text-gray-700" />
            Exclusion Filters
          </CardTitle>
          <CardDescription className="text-sm text-gray-600 leading-relaxed">
            Configure filters to exclude certain submissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExclusionFilterBlock form={form} />
        </CardContent>
      </Card>

      {/* Form Sharing Card */}
      <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-3">
            <Settings className="h-5 w-5 text-primary" />
            Form Sharing
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground leading-relaxed">
            Create public links to share this form with external users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormSharingPanel
            formId={form._id || form.id!}
            formName={form.name}
          />
        </CardContent>
      </Card>
    </div>
  );

  const renderSectionDetails = () => {
    const section = selectedData as Section;
    if (!section) return null;

    return (
      <div className="space-y-6">
        {/* Basic Section Settings Card */}
        <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <Layers className="h-5 w-5 text-primary" />
              Section Information
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed">
              Configure section title, description, and behavior
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="section-title" className="text-sm font-medium">Section Title</Label>
              <Input
                id="section-title"
                value={localChanges.title ?? section.title}
                onChange={(e) => handleLocalChange('title', e.target.value)}
                placeholder="Enter section title"
                className="w-full min-h-12 mt-2 mb-4 leading-relaxed"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="section-description" className="text-sm font-medium">Description</Label>
              <Textarea
                id="section-description"
                value={localChanges.description ?? section.description}
                onChange={(e) => handleLocalChange('description', e.target.value)}
                placeholder="Describe this section (optional)"
                className="min-h-[100px] resize-none mt-2 mb-4 leading-relaxed"
              />
            </div>

            <div className="flex items-center justify-between p-6 rounded-lg border bg-muted/30">
              <div className="space-y-1">
                <Label className="text-sm font-medium">Repeatable Section</Label>
                <p className="text-xs text-muted-foreground leading-relaxed">
                  Allow users to add multiple instances
                </p>
              </div>
              <Switch
                checked={localChanges.repeatable ?? section.repeatable}
                onCheckedChange={(checked) => handleLocalChange('repeatable', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Section Statistics Card */}
        <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <Settings className="h-5 w-5 text-primary" />
              Section Statistics
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed">
              Overview of section structure and position
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6 text-sm">
              <div className="space-y-2 p-4 rounded-lg bg-muted/30">
                <span className="text-muted-foreground text-xs uppercase tracking-wide">Questions</span>
                <p className="font-semibold text-lg">{section.questions.length}</p>
              </div>
              <div className="space-y-2 p-4 rounded-lg bg-muted/30">
                <span className="text-muted-foreground text-xs uppercase tracking-wide">Position</span>
                <p className="font-semibold text-lg">{section.order + 1}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderQuestionDetails = () => {
    const question = selectedData as Question;
    if (!question) return null;

    // Merge local changes with the original question
    const questionWithChanges = { ...question, ...localChanges };

    return (
      <div className="space-y-6">
        {/* Question Configuration */}
        <QuestionConfig
          question={questionWithChanges}
          onUpdate={(updates) => {
            // Handle multiple field updates
            Object.entries(updates).forEach(([key, value]) => {
              handleLocalChange(key, value);
            });
          }}
        />

        {/* Visibility Conditions Card */}
        <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <Eye className="h-5 w-5 text-primary" />
              Visibility Conditions
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed">
              Configure when this question should be visible to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VisibilityEditor
              question={questionWithChanges}
              form={form}
              onUpdate={(visibilityCondition) => handleLocalChange('visibility_condition', visibilityCondition)}
            />
          </CardContent>
        </Card>

        {/* Question Details Card */}
        <Card className="rounded-xl shadow-sm bg-white border border-muted/20">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <HelpCircle className="h-5 w-5 text-primary" />
              Question Details
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed">
              Overview of question metadata and structure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 text-sm">
              <div className="space-y-2 p-4 rounded-lg bg-muted/30">
                <span className="text-muted-foreground text-xs uppercase tracking-wide">Position</span>
                <p className="font-semibold text-lg">{question.order + 1}</p>
              </div>
              {question.options && (
                <div className="space-y-2 p-4 rounded-lg bg-muted/30">
                  <span className="text-muted-foreground text-xs uppercase tracking-wide">Options</span>
                  <p className="font-semibold text-lg">{question.options.length} option{question.options.length !== 1 ? 's' : ''}</p>
                </div>
              )}
              <div className="space-y-2 p-4 rounded-lg bg-muted/30">
                <span className="text-muted-foreground text-xs uppercase tracking-wide">Created</span>
                <p className="font-semibold text-lg">
                  {question.created_at ? new Date(question.created_at * 1000).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderContent = () => {
    if (!selectedItem || !selectedData) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex-1 flex items-center justify-center text-center p-6"
        >
          <div>
            <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-6" />
            <h3 className="font-semibold text-lg mb-3">No Selection</h3>
            <p className="text-muted-foreground max-w-sm text-sm leading-relaxed">
              Select a form element from the left panel to view and edit its properties here.
            </p>
          </div>
        </motion.div>
      );
    }

    const getIcon = () => {
      switch (selectedItem.type) {
        case 'form': return FileText;
        case 'section': return Layers;
        case 'question': return HelpCircle;
        default: return FileText;
      }
    };

    const Icon = getIcon();
    const title = selectedItem.type === 'form' ? 'Form Settings' :
                  selectedItem.type === 'section' ? 'Section Settings' : 'Question Settings';

    return (
      <>
        <div className="border-b px-4 sm:px-6 py-4 sm:py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Icon className="h-5 w-5 text-primary" />
              <h3 className="font-semibold text-base sm:text-lg">{title}</h3>
            </div>
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <Button size="sm" onClick={handleSave} className="h-10">
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={onClose} className="lg:hidden h-10">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <ScrollArea className="flex-1 px-4 sm:px-6">
          <div className="py-6 max-w-full mx-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedItem.type + selectedItem.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                {selectedItem.type === 'form' && renderFormDetails()}
                {selectedItem.type === 'section' && renderSectionDetails()}
                {selectedItem.type === 'question' && renderQuestionDetails()}
              </motion.div>
            </AnimatePresence>
          </div>
        </ScrollArea>
      </>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {renderContent()}
    </div>
  );
}
