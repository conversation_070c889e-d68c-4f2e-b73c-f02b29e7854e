"use client"

import React from 'react';
import { <PERSON>, <PERSON>Off, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Too<PERSON><PERSON>, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import { VisibilityCondition, Question } from '@/lib/types/form';
import { cn } from '@/lib/utils';

interface VisibilityConditionPreviewProps {
  condition?: VisibilityCondition;
  questions: Question[];
  className?: string;
}

export function VisibilityConditionPreview({ 
  condition, 
  questions, 
  className 
}: VisibilityConditionPreviewProps) {
  if (!condition || !condition.conditions || condition.conditions.length === 0) {
    return null;
  }

  // Check if any referenced questions are missing
  const missingQuestions = condition.conditions.filter(
    cond => !questions.find(q => (q._id || q.id) === cond.question_id)
  );

  const hasErrors = missingQuestions.length > 0;

  // Generate a human-readable preview
  const generatePreview = (): string => {
    if (condition.conditions.length === 0) return '';
    
    const conditionTexts = condition.conditions.map(cond => {
      const question = questions.find(q => (q._id || q.id) === cond.question_id);
      const questionLabel = question?.label || `Question ${cond.question_id}`;
      
      let valueText = String(cond.value);
      
      // For boolean questions, show Yes/No instead of true/false
      if (question?.type === 'boolean') {
        valueText = cond.value === 'true' || cond.value === true ? 'Yes' : 'No';
      }
      
      // For select questions, try to find the option label
      if ((question?.type === 'single_select' || question?.type === 'multi_select') && question.options) {
        const option = question.options.find(opt => opt.value === cond.value);
        if (option) {
          valueText = option.label;
        }
      }

      const operatorInfo = {
        '==': { text: 'equals', symbol: '=' },
        '!=': { text: 'does not equal', symbol: '≠' },
        '>': { text: 'is greater than', symbol: '>' },
        '<': { text: 'is less than', symbol: '<' },
        '>=': { text: 'is greater than or equal to', symbol: '≥' },
        '<=': { text: 'is less than or equal to', symbol: '≤' }
      }[condition.operator] || { text: condition.operator, symbol: condition.operator };

      return `${questionLabel} ${operatorInfo.text} "${valueText}"`;
    });

    const joinWord = condition.operator === 'or' ? ' OR ' : ' AND ';
    return conditionTexts.join(joinWord);
  };

  const preview = generatePreview();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant={hasErrors ? "destructive" : "secondary"}
            className={cn(
              "flex items-center gap-1 max-w-[200px] cursor-help",
              hasErrors && "border-destructive/50",
              className
            )}
          >
            {hasErrors ? (
              <AlertTriangle className="h-3 w-3" />
            ) : (
              <Eye className="h-3 w-3" />
            )}
            <span className="truncate text-xs">
              {hasErrors ? 'Invalid condition' : 'Conditional'}
            </span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-[300px]">
          <div className="space-y-1">
            <p className="font-medium text-xs">Visibility Condition:</p>
            {hasErrors ? (
              <div className="text-destructive text-xs">
                <p>⚠️ References missing questions:</p>
                <ul className="list-disc list-inside mt-1">
                  {missingQuestions.map((cond, idx) => (
                    <li key={idx}>Question ID: {cond.question_id}</li>
                  ))}
                </ul>
              </div>
            ) : (
              <p className="text-xs">{preview}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
