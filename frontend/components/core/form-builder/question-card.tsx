"use client"

import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import {
  GripVertical,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  ChevronDown,
  ChevronRight,
  Eye,
  <PERSON>h,
  <PERSON>,
  AlignLeft,
  Circle,
  CheckSquare,
  ToggleLeft,
  Upload,
  Calendar,
  Minus
} from 'lucide-react';

import { FormWithDetails, Question, QuestionType, QUESTION_TYPES, isCoreFieldQuestion, getCoreFieldDisplayName } from '@/lib/types/form';
import { EditQuestionDialog } from './edit-question-dialog';
import { cn } from '@/lib/utils';

interface QuestionCardProps {
  question: Question;
  form: FormWithDetails;
  isSelected?: boolean;
  onUpdate: (updates: any) => void | Promise<void>;
  onDelete: () => void;
  onDuplicate: () => void;
  onSelect?: () => void;
}

// Icon mapping for question types
const QUESTION_TYPE_ICONS = {
  [QuestionType.SHORT_TEXT]: Type,
  [QuestionType.LONG_TEXT]: AlignLeft,
  [QuestionType.NUMBER]: Hash,
  [QuestionType.RANGE]: Minus,
  [QuestionType.SINGLE_SELECT]: Circle,
  [QuestionType.MULTI_SELECT]: CheckSquare,
  [QuestionType.BOOLEAN]: ToggleLeft,
  [QuestionType.FILE]: Upload,
  [QuestionType.DATE]: Calendar,
};

export function QuestionCard({
  question,
  form,
  isSelected = false,
  onUpdate,
  onDelete,
  onDuplicate,
  onSelect
}: QuestionCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `question-${question._id || question.id}`,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const TypeIcon = QUESTION_TYPE_ICONS[question.type] || Type;
  const typeInfo = QUESTION_TYPES.find(t => t.type === question.type);

  return (
    <>
      <Card
        ref={setNodeRef}
        style={style}
        className={cn(
          "group transition-all duration-300 cursor-pointer rounded-xl border w-full",
          "touch-manipulation active:scale-[0.99]", // Mobile touch optimization
          // Mobile-first spacing
          "ml-2 md:ml-4 min-h-14 md:min-h-16",
          // Drag states
          isDragging ? 'opacity-50 rotate-1 shadow-xl scale-105' : 'shadow-sm hover:shadow-md',
          // Expanded state
          isExpanded ? 'ring-1 ring-primary/20' : '',
          // Selected state
          isSelected
            ? 'ring-2 ring-primary shadow-lg bg-primary/10 border-primary/30'
            : 'border-border hover:border-primary/30'
        )}
        onClick={(e) => {
          e.stopPropagation();
          onSelect?.();
        }}
      >
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between gap-3">
            <div className="flex items-start gap-3 flex-1 min-w-0">
              {/* Drag Handle */}
              <div
                {...attributes}
                {...listeners}
                className="mt-1 cursor-grab active:cursor-grabbing hover:text-primary transition-colors opacity-60 group-hover:opacity-100 flex-shrink-0 h-10 w-10 flex items-center justify-center touch-manipulation"
                onClick={(e) => e.stopPropagation()}
              >
                <GripVertical className="h-5 w-5" />
              </div>

              {/* Question Info */}
              <div className="flex-1 min-w-0">
                <div className="flex flex-wrap items-center gap-2 mb-3">
                  <div className="flex items-center gap-2">
                    <TypeIcon className="h-4 w-4 text-primary" />
                    <Badge variant="outline" className="text-xs font-medium">
                      {typeInfo?.label || question.type}
                    </Badge>
                  </div>
                  {question.required && (
                    <Badge variant="destructive" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {isCoreFieldQuestion(question) && (
                    <Badge variant="default" className="text-xs bg-blue-600 hover:bg-blue-700">
                      Core Field
                    </Badge>
                  )}
                  {question.visibility_condition && (
                    <Badge variant="secondary" className="text-xs">
                      <Eye className="h-3 w-3 mr-1" />
                      Conditional
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Just a visual indicator - clicking the question card will select it
                    }}
                    className="text-xs text-muted-foreground hover:text-primary h-6 px-2"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    Set Visibility
                  </Button>
                </div>

                <h4 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary transition-colors leading-relaxed">
                  {question.label}
                </h4>

                {question.help_text && (
                  <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 mb-3 leading-relaxed">
                    {question.help_text}
                  </p>
                )}

                <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <span className="text-muted-foreground">Position</span>
                    <span className="font-semibold text-foreground">{question.order + 1}</span>
                  </div>
                  {question.options && (
                    <div className="flex items-center gap-1">
                      <span className="font-semibold text-foreground">{question.options.length}</span>
                      <span className="text-muted-foreground">option{question.options.length !== 1 ? 's' : ''}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 opacity-60 group-hover:opacity-100 transition-opacity flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
                className="text-muted-foreground hover:text-primary h-10 w-10 p-0 touch-manipulation"
              >
                {isExpanded ? (
                  <ChevronDown className="h-5 w-5" />
                ) : (
                  <ChevronRight className="h-5 w-5" />
                )}
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                    className="text-muted-foreground hover:text-primary h-10 w-10 p-0 touch-manipulation"
                  >
                    <MoreHorizontal className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Question
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onDuplicate}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate Question
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {isCoreFieldQuestion(question) ? (
                    <DropdownMenuItem
                      disabled
                      className="text-muted-foreground cursor-not-allowed"
                      title="Core dashboard field. Can't delete."
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Question
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem
                      onClick={() => setShowDeleteDialog(true)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Question
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <CardContent className="pt-0 border-t">
                <div className="space-y-3 mt-3">
                  {/* Question Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-muted-foreground">Type:</span>
                      <p className="mt-1">{typeInfo?.label || question.type}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Required:</span>
                      <p className="mt-1">{question.required ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  {/* Options */}
                  {question.options && question.options.length > 0 && (
                    <div>
                      <span className="font-medium text-muted-foreground text-sm">Options:</span>
                      <div className="mt-2 space-y-1">
                        {question.options.map((option, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <div className="w-2 h-2 bg-muted rounded-full" />
                            <span>{option.label}</span>
                            <span className="text-muted-foreground">({option.value})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Validation Rules */}
                  {question.validation && (
                    <div>
                      <span className="font-medium text-muted-foreground text-sm">Validation:</span>
                      <div className="mt-2 space-y-1 text-sm">
                        {question.validation.min !== undefined && (
                          <div>Min: {question.validation.min}</div>
                        )}
                        {question.validation.max !== undefined && (
                          <div>Max: {question.validation.max}</div>
                        )}
                        {question.validation.regex && (
                          <div>Pattern: {question.validation.regex}</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Visibility Condition */}
                  {question.visibility_condition && (
                    <div>
                      <span className="font-medium text-muted-foreground text-sm">Visibility:</span>
                      <div className="mt-2 p-2 bg-muted/50 rounded text-sm">
                        <div className="flex items-center gap-2">
                          <Eye className="h-3 w-3" />
                          <span>Show if conditions are met</span>
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground">
                          {question.visibility_condition.conditions.length} condition{question.visibility_condition.conditions.length !== 1 ? 's' : ''} with {question.visibility_condition.operator} logic
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Dialogs */}
      <EditQuestionDialog
        question={question}
        form={form}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={async (data) => {
          const result = onUpdate(data);
          if (result instanceof Promise) {
            await result;
          }
        }}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Question</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{question.label}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete();
                setShowDeleteDialog(false);
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Question
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
