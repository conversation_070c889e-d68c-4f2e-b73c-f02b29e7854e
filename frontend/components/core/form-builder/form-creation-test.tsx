"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FormBuilderAPI } from '@/lib/api/form-builder-api';
import { toast } from '@/components/ui/use-toast';

export function FormCreationTest() {
  const [isCreating, setIsCreating] = useState(false);
  const [createdForms, setCreatedForms] = useState<any[]>([]);
  const [clickCount, setClickCount] = useState(0);

  const testFormCreation = async () => {
    setClickCount(prev => prev + 1);
    console.log(`🧪 Test click #${clickCount + 1}`);
    
    if (isCreating) {
      console.log('⚠️ Already creating, should be prevented');
      toast({
        title: 'Test Result',
        description: 'Creation prevented - already in progress',
        variant: 'default'
      });
      return;
    }

    try {
      setIsCreating(true);
      console.log('🚀 Starting form creation test...');
      
      const newForm = await FormBuilderAPI.createForm({
        name: `Test Form ${Date.now()}`,
        description: 'Test form created by protection test',
        is_active: false
      });
      
      setCreatedForms(prev => [...prev, newForm]);
      console.log('✅ Form created successfully:', newForm);
      
      toast({
        title: 'Test Success',
        description: `Form created: ${newForm.name}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('❌ Form creation failed:', error);
      toast({
        title: 'Test Error',
        description: 'Form creation failed',
        variant: 'destructive'
      });
    } finally {
      setIsCreating(false);
    }
  };

  const rapidClickTest = async () => {
    console.log('🧪 Starting rapid click test...');
    // Simulate rapid clicking
    for (let i = 0; i < 5; i++) {
      setTimeout(() => testFormCreation(), i * 100);
    }
  };

  const resetTest = () => {
    setCreatedForms([]);
    setClickCount(0);
    setIsCreating(false);
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Form Creation Protection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Click count: {clickCount}
          </p>
          <p className="text-sm text-muted-foreground">
            Forms created: {createdForms.length}
          </p>
          <p className="text-sm text-muted-foreground">
            Status: {isCreating ? 'Creating...' : 'Ready'}
          </p>
        </div>
        
        <div className="space-y-2">
          <Button 
            onClick={testFormCreation}
            disabled={isCreating}
            className="w-full"
          >
            {isCreating ? 'Creating...' : 'Create Form (Single Click)'}
          </Button>
          
          <Button 
            onClick={rapidClickTest}
            disabled={isCreating}
            variant="outline"
            className="w-full"
          >
            Rapid Click Test (5 clicks)
          </Button>
          
          <Button 
            onClick={resetTest}
            variant="secondary"
            className="w-full"
          >
            Reset Test
          </Button>
        </div>

        {createdForms.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Created Forms:</h4>
            {createdForms.map((form, index) => (
              <div key={index} className="text-xs p-2 bg-muted rounded">
                {form.name} (ID: {form._id || form.id})
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 