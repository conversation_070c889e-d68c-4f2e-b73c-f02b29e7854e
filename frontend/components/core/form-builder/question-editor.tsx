"use client"

import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, ControllerRenderProps, FieldValues, Path } from 'react-hook-form';
import * as z from 'zod';
import { Trash2, Plus, Minus } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Form as FormUI, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Question, QuestionType, QuestionOption, QuestionCreateRequest, toApiQuestionType, toDisplayQuestionType } from '@/lib/types/form';
import FormAPI from '@/lib/api/form-api';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Question validation schema
const questionSchema = z.object({
  type: z.string(),
  label: z.string().min(1, 'Question label is required'),
  help_text: z.string().optional(),
  required: z.boolean().default(false),
  options: z.array(
    z.object({
      label: z.string().min(1, 'Option label is required'),
      value: z.string().min(1, 'Option value is required'),
    })
  ).optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    regex: z.string().optional(),
  }).optional(),
});

type QuestionFormValues = z.infer<typeof questionSchema> & {
  options?: Array<{ label: string; value: string; }>;
};

interface QuestionEditorProps {
  question?: Partial<Question>;
  sectionId: string;
  index: number;
  onUpdate: (index: number, question: Partial<Question>) => void;
  onDelete: (index: number) => void;
  onSave?: () => void;
}

// Update the form field render prop type with proper path constraint
type FormFieldRenderProps<T extends FieldValues, K extends Path<T>> = {
  field: ControllerRenderProps<T, K>;
};

// Add a helper function to convert string type to enum
const stringToQuestionType = (type: string): QuestionType => {
  // Handle both snake_case and kebab-case
  const normalizedType = type.replace(/-/g, '_').toUpperCase();
  // Map common variations
  const typeMap: Record<string, QuestionType> = {
    'SHORT_TEXT': QuestionType.SHORT_TEXT,
    'LONG_TEXT': QuestionType.LONG_TEXT,
    'NUMBER': QuestionType.NUMBER,
    'RANGE': QuestionType.RANGE,
    'DATE': QuestionType.DATE,
    'SINGLE_SELECT': QuestionType.SINGLE_SELECT,
    'MULTI_SELECT': QuestionType.MULTI_SELECT,
    'BOOLEAN': QuestionType.BOOLEAN,
    'FILE': QuestionType.FILE,
  };

  const enumType = typeMap[normalizedType];
  if (!enumType) {
    console.warn(`Unknown question type: ${type}, defaulting to SHORT_TEXT`);
    return QuestionType.SHORT_TEXT;
  }
  return enumType;
};

// Add a helper function to convert enum to string type
const questionTypeToString = (type: QuestionType): string => {
  // The enum values are already in the correct format (e.g., "single_select")
  return type;
};

export function QuestionEditor({ question, sectionId, index, onUpdate, onDelete, onSave }: QuestionEditorProps) {
  // Log the props to help debug issues
  const initialType = toApiQuestionType(question?.type || 'short_text');
  const currentOrder = question?.order ?? index;

  console.log(`QuestionEditor initialized with:`, {
    questionId: question?._id || question?.id || 'new-question',
    sectionId,
    index,
    initialType,
    rawType: question?.type,
    options: question?.options,
    currentOrder,
  });

  // Ensure sectionId is valid
  if (!sectionId) {
    console.error('QuestionEditor: sectionId is missing or invalid', { sectionId, question });
  }

  // Initialize default options for select questions
  const initialOptions = React.useMemo(() => {
    if (
      (question?.type === "single_select" ||
       question?.type === "multi_select") &&
      Array.isArray(question?.options) &&
      question.options.length > 0
    ) {
      return question.options;
    }
    return [];
  }, [question?.type, question?.options]);

  console.log('Initial options:', initialOptions);

  // Initialize form with proper default values
  const form = useForm<QuestionFormValues>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      type: initialType,
      label: question?.label || '',
      help_text: question?.help_text || '',
      required: question?.required || false,
      options: initialOptions,
      validation: question?.validation || {},
    },
  });

  // Reset form when question prop changes
  React.useEffect(() => {
    if (question) {
      const currentType = toApiQuestionType(question.type || 'short_text');
      console.log('Resetting form with question data:', {
        questionId: question._id || question.id,
        type: currentType,
        rawType: question.type,
        label: question.label
      });

      form.reset({
        type: currentType,
        label: question.label || '',
        help_text: question.help_text || '',
        required: question.required || false,
        options: question.options || [],
        validation: question.validation || {},
      });
    }
  }, [question, form]);

  // Watch for type changes
  const questionType = form.watch('type');
  const hasOptions = ["single_select", "multi_select"].includes(questionType);
  const hasRange = questionType === "range" || questionType === "number";
  const isBoolean = questionType === "boolean";
  const isText = ["short_text", "long_text"].includes(questionType);
  const isDate = questionType === "date";
  const isFile = questionType === "file";

  // Update saveChangesWithType to handle onSave callback
  const saveChangesWithType = async (typeValue: string) => {
    try {
      console.log('🚀 saveChangesWithType called with typeValue:', typeValue);

      const values = form.getValues();
      console.log('📋 Current form values:', values);

      const apiQuestionType = toApiQuestionType(typeValue);
      console.log('🔄 Converted to API type:', apiQuestionType);

      const questionRequest: QuestionCreateRequest = {
        section_id: sectionId,
        type: apiQuestionType,
        label: values.label,
        help_text: values.help_text || '',
        required: values.required || false,
        options: typeValue === 'boolean' ? undefined : (values.options || []),
        validation: values.validation || {},
        order: currentOrder,
      };

      console.log('📤 Final request payload:', JSON.stringify(questionRequest, null, 2));

      const questionId = question?._id || question?.id;
      console.log('🆔 Question ID:', questionId);

      if (questionId) {
        console.log('🔄 Updating existing question...');
        const updatedQuestion = await FormAPI.updateQuestion(questionId, questionRequest);
        console.log('✅ Question updated successfully:', updatedQuestion);
        onUpdate(index, {
          ...updatedQuestion,
          order: currentOrder,
        });
      } else {
        console.log('➕ Creating new question...');
        const updatedForm = await FormAPI.createQuestion(sectionId, questionRequest);
        console.log('✅ Question created successfully, received full form:', updatedForm);
        
        // Extract the created question from the full form response
        const createdQuestion = updatedForm.sections
          .find(s => (s._id || s.id) === sectionId)?.questions
          .find(q => q.label === questionRequest.label);
          
        if (createdQuestion) {
          onUpdate(index, {
            ...createdQuestion,
            order: currentOrder,
          });
        } else {
          console.error('Could not find created question in response');
          // Fallback to using the request data
          onUpdate(index, {
            ...questionRequest,
            type: questionRequest.type as QuestionType,
            _id: 'temp-id',
            order: currentOrder,
          });
        }
      }

      // Call onSave callback if provided
      if (onSave) {
        await onSave();
      }

      toast({
        title: "Question saved",
        description: "Your question has been saved successfully.",
      });
    } catch (error) {
      console.error('❌ Error in saveChangesWithType:', error);
      toast({
        title: "Error saving question",
        description: "There was an error saving your question. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Add a new option
  const addOption = () => {
    console.log('Adding new option');

    // Ensure we have an array to work with
    let currentOptions = form.getValues('options');
    if (!currentOptions || !Array.isArray(currentOptions)) {
      currentOptions = [];
    }

    // Create a new option with empty values
    const newOption = { label: '', value: '' };
    console.log('Current options:', currentOptions);
    console.log('Adding new option:', newOption);

    // Update the form with the new option
    const updatedOptions = [...currentOptions, newOption];
    form.setValue('options', updatedOptions);

    // Force a re-render to ensure the UI updates and log the result
    setTimeout(() => {
      const options = form.getValues('options');
      console.log('Options after adding:', options);

      // Trigger form validation
      form.trigger('options');
    }, 0);
  };

  // Handle option label change to auto-generate value
  const handleOptionLabelChange = (index: number, label: string) => {
    console.log(`Changing option ${index} label to: ${label}`);

    // Ensure we have an array to work with
    let currentOptions = form.getValues('options');
    if (!currentOptions || !Array.isArray(currentOptions)) {
      currentOptions = [];
      form.setValue('options', currentOptions);
    }

    // Update the label
    form.setValue(`options.${index}.label`, label);

    // Generate and set the value
    const value = generateValueFromLabel(label);
    console.log(`Generated value: ${value}`);
    form.setValue(`options.${index}.value`, value);

    // Log the current state of options after change
    setTimeout(() => {
      const options = form.getValues('options');
      console.log('Options after label change:', options);

      // Trigger form validation
      form.trigger('options');
    }, 0);
  };

  // Remove an option
  const removeOption = (optionIndex: number) => {
    console.log(`Removing option at index ${optionIndex}`);

    // Ensure we have an array to work with
    let currentOptions = form.getValues('options');
    if (!currentOptions || !Array.isArray(currentOptions)) {
      console.warn('No options array found when trying to remove option');
      return;
    }

    console.log('Current options before removal:', currentOptions);

    // Remove the option at the specified index
    const updatedOptions = currentOptions.filter((_, i) => i !== optionIndex);
    form.setValue('options', updatedOptions);

    console.log('Options after removal:', updatedOptions);

    // Trigger form validation
    form.trigger('options');
  };

  // Function to generate value from label (lowercase and replace spaces with underscores)
  const generateValueFromLabel = (label: string): string => {
    return label.toLowerCase().replace(/\s+/g, '_');
  };

  // Add delete confirmation dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Enhanced delete handler
  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Just call the parent's onDelete callback
    // The parent component (DraggableQuestionList) will handle the API call and state update
    onDelete(index);

    // Close the dialog
    setIsDeleteDialogOpen(false);
  };

  // Add confirmation dialog component
  const DeleteConfirmationDialog = () => (
    <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Question</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this question? This action cannot be undone.
            {question?.visibility_condition && (
              <div className="mt-2 p-2 bg-yellow-50 text-yellow-800 rounded-md text-sm">
                Warning: This question is referenced by visibility conditions in other questions.
                Deleting it may affect form behavior.
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsDeleteDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
          >
            Delete Question
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return (
    <>
      <Card className="w-full mb-4 border-l-4 border-l-primary">
        <CardHeader className="flex flex-row items-start justify-between space-y-0">
          <div>
            <CardTitle>Question {currentOrder + 1}</CardTitle>
            <CardDescription>
              Define your question
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('💾 Save button clicked, current questionType:', questionType);
                const currentFormType = form.getValues('type');
                console.log('📊 Current form type value:', currentFormType);
                await saveChangesWithType(currentFormType || questionType);
              }}
              type="button"
              className="h-8"
            >
              Save
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDeleteDialogOpen(true);
              }}
              type="button"
              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Question
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <FormUI {...form}>
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="type"
                render={({ field }: FormFieldRenderProps<QuestionFormValues, 'type'>) => (
                  <FormItem>
                    <FormLabel>Question Type</FormLabel>
                    <Select
                      onValueChange={async (value: string) => {
                        console.log('🔄 Question type changing from', field.value, 'to', value);

                        // Update form state first
                        form.setValue('type', value, { shouldDirty: true, shouldValidate: true });

                        // Wait for the next tick to ensure state is updated
                        await new Promise(resolve => setTimeout(resolve, 0));

                        // Verify the form state was updated
                        const currentFormType = form.getValues('type');
                        console.log('📊 Form state after setValue:', currentFormType);

                        // Save with the explicit value to avoid race conditions
                        console.log('💾 Calling saveChangesWithType with value:', value);
                        await saveChangesWithType(value);
                      }}
                      value={form.watch('type')}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue>
                            {field.value ? toDisplayQuestionType(field.value) : 'Select question type'}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="short_text">{toDisplayQuestionType('short_text')}</SelectItem>
                        <SelectItem value="long_text">{toDisplayQuestionType('long_text')}</SelectItem>
                        <SelectItem value="number">{toDisplayQuestionType('number')}</SelectItem>
                        <SelectItem value="range">{toDisplayQuestionType('range')}</SelectItem>
                        <SelectItem value="date">{toDisplayQuestionType('date')}</SelectItem>
                        <SelectItem value="single_select">{toDisplayQuestionType('single_select')}</SelectItem>
                        <SelectItem value="multi_select">{toDisplayQuestionType('multi_select')}</SelectItem>
                        <SelectItem value="boolean">{toDisplayQuestionType('boolean')}</SelectItem>
                        <SelectItem value="file">{toDisplayQuestionType('file')}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Current type: {field.value ? field.value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Not set'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="label"
                render={({ field }: FormFieldRenderProps<QuestionFormValues, 'label'>) => (
                  <FormItem>
                    <FormLabel>Question Label</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter question label"
                        value={field.value as string}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormDescription>
                      The text displayed to the user
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="help_text"
                render={({ field }: FormFieldRenderProps<QuestionFormValues, 'help_text'>) => (
                  <FormItem>
                    <FormLabel>Help Text</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter help text"
                        className="min-h-[60px]"
                        value={field.value as string}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormDescription>
                      Additional guidance for the user
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="required"
                render={({ field }: FormFieldRenderProps<QuestionFormValues, 'required'>) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Required</FormLabel>
                      <FormDescription>
                        Make this question mandatory
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value as boolean}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Text specific options */}
              {isText && (
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="font-medium">Text Field Options</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <FormLabel>Minimum Length</FormLabel>
                      <Input
                        type="number"
                        placeholder="Min length"
                        {...form.register('validation.min', { valueAsNumber: true })}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Minimum number of characters
                      </p>
                    </div>
                    <div>
                      <FormLabel>Maximum Length</FormLabel>
                      <Input
                        type="number"
                        placeholder="Max length"
                        {...form.register('validation.max', { valueAsNumber: true })}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum number of characters
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <FormLabel>Regex Pattern (Optional)</FormLabel>
                    <Input
                      placeholder="Regular expression pattern"
                      {...form.register('validation.regex')}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Validate input against a regular expression pattern
                    </p>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-sm">
                      {questionType === "short_text" ?
                        "This will be displayed as a single-line text input." :
                        "This will be displayed as a multi-line text area."}
                    </p>
                  </div>
                </div>
              )}

              {/* Range/Number validation fields */}
              {hasRange && (
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="font-medium">Validation Rules</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <FormLabel>Minimum Value</FormLabel>
                      <Input
                        type="number"
                        placeholder="Min value"
                        {...form.register('validation.min', { valueAsNumber: true })}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Smallest allowed value
                      </p>
                    </div>
                    <div>
                      <FormLabel>Maximum Value</FormLabel>
                      <Input
                        type="number"
                        placeholder="Max value"
                        {...form.register('validation.max', { valueAsNumber: true })}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Largest allowed value
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-sm">
                      {questionType === "range" ?
                        "This will be displayed as a slider with min and max values." :
                        "This will be displayed as a number input field."}
                    </p>
                  </div>
                </div>
              )}

              {/* Boolean specific options */}
              {isBoolean && (
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="font-medium">Boolean Options</h3>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="border p-3 rounded-md bg-muted/30">
                      <div className="font-medium">True Option</div>
                      <div className="flex mt-2">
                        <div className="bg-green-100 text-green-800 px-3 py-1 rounded-md text-sm">
                          Yes
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Value: "true"
                      </p>
                    </div>
                    <div className="border p-3 rounded-md bg-muted/30">
                      <div className="font-medium">False Option</div>
                      <div className="flex mt-2">
                        <div className="bg-red-100 text-red-800 px-3 py-1 rounded-md text-sm">
                          No
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Value: "false"
                      </p>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    This question will be displayed as a Yes/No choice with the values "true" and "false".
                  </p>
                </div>
              )}

              {/* Date specific options */}
              {isDate && (
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="font-medium">Date Field Options</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <FormLabel>Minimum Date</FormLabel>
                      <Input
                        type="date"
                        placeholder="Min date"
                        {...form.register('validation.min')}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Earliest allowed date
                      </p>
                    </div>
                    <div>
                      <FormLabel>Maximum Date</FormLabel>
                      <Input
                        type="date"
                        placeholder="Max date"
                        {...form.register('validation.max')}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Latest allowed date
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-sm">
                      This will be displayed as a date picker.
                    </p>
                  </div>
                </div>
              )}

              {/* File upload specific options */}
              {isFile && (
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="font-medium">File Upload Options</h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <FormLabel>Allowed File Types</FormLabel>
                      <Input
                        placeholder="e.g., .pdf,.doc,.docx"
                        {...form.register('validation.regex')}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Comma-separated list of allowed file extensions
                      </p>
                    </div>
                    <div>
                      <FormLabel>Maximum File Size (MB)</FormLabel>
                      <Input
                        type="number"
                        placeholder="Max size in MB"
                        {...form.register('validation.max', { valueAsNumber: true })}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum file size in megabytes
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-sm">
                      This will be displayed as a file upload field.
                    </p>
                  </div>
                </div>
              )}

              {/* Options for select types */}
              {hasOptions && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <FormLabel>Options</FormLabel>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addOption}
                      className="h-8"
                    >
                      <Plus className="h-4 w-4 mr-1" /> Add Option
                    </Button>
                  </div>

                  {(() => {
                    const options = form.watch('options');

                    // If options is not an array or is empty, show a message
                    if (!options || !Array.isArray(options) || options.length === 0) {
                      return (
                        <div className="text-center p-4 border rounded-md bg-muted/30">
                          <p className="text-muted-foreground">No options added yet. Click "Add Option" to get started.</p>
                        </div>
                      );
                    }

                    // Otherwise, render the options
                    return options.map((option, optionIndex) => (
                      <div key={optionIndex} className="flex items-start space-x-2 border p-3 rounded-md">
                        <div className="grid grid-cols-2 gap-2 flex-1">
                          <div>
                            <FormLabel className="mb-2">Option Label</FormLabel>
                            <Input
                              placeholder="Option Label"
                              value={option?.label || ''}
                              onChange={(e) => handleOptionLabelChange(optionIndex, e.target.value)}
                            />
                            <p className="text-xs text-muted-foreground mt-1">Enter the display text</p>
                          </div>
                          <div>
                            <FormLabel className="mb-2">Option Value</FormLabel>
                            <Input
                              placeholder="Option Value"
                              value={option?.value || ''}
                              disabled
                              className="bg-muted/50"
                            />
                            <p className="text-xs text-muted-foreground mt-1">Auto-generated from label</p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeOption(optionIndex)}
                          className="text-destructive hover:text-destructive/90 hover:bg-destructive/10 h-10 mt-8"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      </div>
                    ));
                  })()}
                </div>
              )}

              <div className="mt-6 flex justify-end">
                <Button
                  type="button"
                  onClick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('💾 Bottom save button clicked');
                    const currentFormType = form.getValues('type');
                    console.log('📊 Current form type value:', currentFormType);
                    await saveChangesWithType(currentFormType);
                  }}
                  className="w-full"
                >
                  Save Question
                </Button>
              </div>
            </div>
          </FormUI>
        </CardContent>
      </Card>
      <DeleteConfirmationDialog />
    </>
  );
}
