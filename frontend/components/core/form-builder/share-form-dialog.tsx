"use client"

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface ShareFormDialogProps {
  formId: string;
  formName: string;
  children: React.ReactNode;
}

interface SharingConfig {
  _id: string;
  enabled: boolean;
  sharing_types: string[];
  expires_at?: number;
}

interface SharingLink {
  _id: string;
  token: string;
  url: string;
  expires_at?: number;
  view_count: number;
  created_at: number;
}

export function ShareFormDialog({ formId, formName, children }: ShareFormDialogProps) {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [sharingConfig, setSharingConfig] = useState<SharingConfig | null>(null);
  const [sharingLinks, setSharingLinks] = useState<SharingLink[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [expirationDays, setExpirationDays] = useState(30);

  useEffect(() => {
    if (open) {
      loadSharingData();
    }
  }, [open, formId]);

  const loadSharingData = async () => {
    try {
      setLoading(true);

      // Load sharing config
      const configResponse = await fetch(`${API_BASE}/sharing/configs?resource_type=form&resource_id=${formId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
      });

      if (configResponse.ok) {
        const configs = await configResponse.json();
        if (configs.length > 0) {
          setSharingConfig(configs[0]);

          // Load sharing links for this config
          const linksResponse = await fetch(`${API_BASE}/sharing/configs/${configs[0]._id}/links`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'X-ORG-ID': localStorage.getItem('orgId') || '',
            },
          });

          if (linksResponse.ok) {
            const links = await linksResponse.json();
            setSharingLinks(links);
          }
        }
      }
    } catch (error) {
      console.error('Error loading sharing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const enableSharing = async () => {
    try {
      setCreating(true);

      const response = await fetch(`${API_BASE}/sharing/configs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          resource_type: 'form',
          resource_id: formId,
          enabled: true,
          sharing_types: ['link'],
          tracking_enabled: true,
        }),
      });

      if (response.ok) {
        const config = await response.json();
        setSharingConfig(config);
        toast({
          title: 'Success',
          description: 'Sharing enabled for this form',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to enable sharing');
      }
    } catch (error) {
      console.error('Error enabling sharing:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to enable sharing',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const generateLink = async () => {
    if (!sharingConfig) return;

    try {
      setCreating(true);

      const expiresAt = expirationDays > 0
        ? Math.floor((Date.now() + expirationDays * 24 * 60 * 60 * 1000) / 1000)
        : undefined;

      const response = await fetch(`${API_BASE}/sharing/configs/${sharingConfig._id}/links`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          expires_at: expiresAt,
          metadata: { source: 'form_builder' },
        }),
      });

      if (response.ok) {
        const link = await response.json();
        setSharingLinks(prev => [link, ...prev]);
        toast({
          title: 'Success',
          description: 'Sharing link created successfully',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to create sharing link');
      }
    } catch (error) {
      console.error('Error creating sharing link:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create sharing link',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: 'Copied!',
        description: 'Sharing link copied to clipboard',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent size="default">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icons.share className="h-5 w-5" />
            Share Form
          </DialogTitle>
        </DialogHeader>

        <DialogBody>
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        ) : !sharingConfig ? (
          <div className="space-y-4">
            <Alert>
              <Icons.info className="h-4 w-4" />
              <AlertDescription>
                Enable sharing to create public links for this form that external users can access.
              </AlertDescription>
            </Alert>

              <Button onClick={enableSharing} disabled={creating} className="w-full h-12 rounded-xl font-semibold">
              {creating ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Enabling...
                </>
              ) : (
                <>
                  <Icons.share className="mr-2 h-4 w-4" />
                  Enable Sharing
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Sharing Status</span>
              <Badge variant={sharingConfig.enabled ? "default" : "secondary"}>
                {sharingConfig.enabled ? "Enabled" : "Disabled"}
              </Badge>
            </div>

            <div className="space-y-2">
                <Label htmlFor="expiration" className="text-sm md:text-base font-medium mb-1">Link Expiration (days)</Label>
              <div className="flex space-x-2">
                <Input
                  id="expiration"
                  type="number"
                  value={expirationDays}
                  onChange={(e) => setExpirationDays(Number(e.target.value))}
                  placeholder="Days"
                  min="0"
                  max="365"
                    className="h-12 rounded-xl border-gray-200 px-4"
                />
                  <Button onClick={generateLink} disabled={creating} className="h-12 px-4 rounded-xl">
                  {creating ? (
                    <Icons.spinner className="h-4 w-4 animate-spin" />
                  ) : (
                    <Icons.add className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Set to 0 for no expiration
              </p>
            </div>

            {sharingLinks.length > 0 && (
              <div className="space-y-2">
                  <Label className="text-sm md:text-base font-medium mb-1">Active Links</Label>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {sharingLinks.map((link) => (
                      <div key={link._id} className="border border-gray-200 rounded-lg p-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {link.view_count} views
                        </span>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(link.url)}
                              className="h-10 px-3 rounded-xl"
                          >
                            <Icons.copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(link.url, '_blank')}
                              className="h-10 px-3 rounded-xl"
                          >
                            <Icons.externalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                        <div className="bg-gray-50 rounded-lg p-2">
                        <code className="text-xs break-all">{link.url}</code>
                      </div>

                      {link.expires_at && (
                        <p className="text-xs text-muted-foreground">
                          Expires: {new Date(link.expires_at * 1000).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
