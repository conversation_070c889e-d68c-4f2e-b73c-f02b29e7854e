"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { Plus, Save, AlertTriangle, Filter, Trash2, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';

import { Form, Question } from '@/lib/types/form';
import {
  ExclusionFilter,
  ExclusionFilterCreateRequest,
  ExclusionFilterUpdateRequest,
  ExclusionCondition,
  LogicalOperator,
  ExclusionOperator
} from '@/lib/types/exclusion-filter';
import { ExclusionFilterAPI } from '@/lib/api/exclusion-filter-api';
import { ModernConditionBuilder } from './modern-condition-builder';


interface ExclusionFilterBlockProps {
  form: Form;
  className?: string;
}

// Define eligible question types for exclusion filters
const ELIGIBLE_QUESTION_TYPES = ['single_select', 'multi_select', 'boolean', 'number'];

interface LocalCondition {
  id: string;
  question_id: string;
  operator: ExclusionOperator;
  value: any;
  isValid: boolean;
  error?: string;
}

export function ExclusionFilterBlock({ form, className }: ExclusionFilterBlockProps) {
  const [filters, setFilters] = useState<ExclusionFilter[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [localConditions, setLocalConditions] = useState<LocalCondition[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<ExclusionFilter | null>(null);

  // Get eligible questions only (Single-Select, Multi-Select, Boolean)
  const eligibleQuestions: Question[] = React.useMemo(() => {
    return form.sections
      .flatMap(section => section.questions || [])
      .filter(question => ELIGIBLE_QUESTION_TYPES.includes(question.type));
  }, [form.sections]);

  // Load existing exclusion filters for this form
  useEffect(() => {
    const loadFilters = async () => {
      if (!form._id && !form.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const formId = form._id || form.id!;
        const existingFilters = await ExclusionFilterAPI.listExclusionFilters(formId);
        setFilters(existingFilters);

        // If there's an active filter, load its conditions
        const activeFilter = existingFilters.find(f => f.is_active);
        if (activeFilter) {
          setCurrentFilter(activeFilter);
          setLocalConditions(
            activeFilter.conditions.map((condition, index) => ({
              id: `condition-${index}`,
              question_id: condition.question_id,
              operator: condition.operator,
              value: condition.value,
              isValid: true
            }))
          );
        }
      } catch (error) {
        console.error('Error loading exclusion filters:', error);
        toast({
          title: 'Error',
          description: 'Failed to load exclusion filters',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadFilters();
  }, [form._id, form.id]);

  // Handle adding a new condition
  const addCondition = () => {
    const newCondition: LocalCondition = {
      id: `condition-${Date.now()}`,
      question_id: '',
      operator: ExclusionOperator.EQUALS,
      value: '',
      isValid: false
    };
    setLocalConditions([...localConditions, newCondition]);
    setHasChanges(true);
  };

  // Handle updating a condition
  const updateCondition = useCallback((id: string, updates: Partial<LocalCondition>) => {
    setLocalConditions(prev => 
      prev.map(condition => 
        condition.id === id ? { ...condition, ...updates } : condition
      )
    );
    setHasChanges(true);
  }, []);

  // Handle removing a condition
  const removeCondition = useCallback((id: string) => {
    setLocalConditions(prev => prev.filter(condition => condition.id !== id));
    setHasChanges(true);
  }, []);

  // Handle saving exclusion filters
  const saveFilters = async () => {
    if (!form._id && !form.id) {
      toast({
        title: 'Error',
        description: 'Form must be saved before adding exclusion filters',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSaving(true);
      const formId = form._id || form.id!;

      // Validate conditions
      const validConditions = localConditions.filter(c => c.isValid);

      // Convert to API format
      const conditions: ExclusionCondition[] = validConditions.map(c => ({
        question_id: c.question_id,
        operator: c.operator,
        value: c.value
      }));

      // If no valid conditions, deactivate the filter
      if (conditions.length === 0) {
        if (currentFilter) {
          const updateRequest: ExclusionFilterUpdateRequest = {
            conditions: [],
            is_active: false
          };

          await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
          setCurrentFilter(null);
          setFilters([]);
        }

        setLocalConditions([]);
        setHasChanges(false);

        toast({
          title: 'Success',
          description: 'Exclusion filters have been deactivated',
        });
        return;
      }

      if (currentFilter) {
        // Update existing filter
        const updateRequest: ExclusionFilterUpdateRequest = {
          conditions,
          is_active: true
        };

        const updatedFilter = await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
        setCurrentFilter(updatedFilter);
        setFilters([updatedFilter]);
      } else {
        // Create new filter
        const createRequest: ExclusionFilterCreateRequest = {
          form_id: formId,
          name: 'Auto-Exclusion Filter',
          description: 'Automatically exclude submissions based on specified conditions',
          operator: LogicalOperator.AND,
          conditions,
          is_active: true
        };

        const newFilter = await ExclusionFilterAPI.createExclusionFilter(createRequest);
        setCurrentFilter(newFilter);
        setFilters([newFilter]);
      }

      setHasChanges(false);

      toast({
        title: 'Success',
        description: 'Exclusion filters saved successfully',
      });
    } catch (error) {
      console.error('Error saving exclusion filters:', error);
      toast({
        title: 'Error',
        description: 'Failed to save exclusion filters',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle clearing all filters
  const clearAllFilters = async () => {
    try {
      setSaving(true);

      if (currentFilter) {
        const updateRequest: ExclusionFilterUpdateRequest = {
          conditions: [],
          is_active: false
        };

        await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
        setCurrentFilter(null);
        setFilters([]);
      }

      setLocalConditions([]);
      setHasChanges(false);

      toast({
        title: 'Success',
        description: 'All exclusion filters have been cleared',
      });
    } catch (error) {
      console.error('Error clearing filters:', error);
      toast({
        title: 'Error',
        description: 'Failed to clear exclusion filters',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        <div className="flex items-start gap-4">
          <Filter className="h-6 w-6 text-gray-700 mt-1" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Exclusion Filters</h3>
            <p className="text-sm text-gray-600 leading-relaxed">
              Automatically exclude submissions based on specific answers. Applies to single-select, multi-select, boolean, and number questions.
            </p>
          </div>
        </div>

        {/* Status Badge */}
        {currentFilter && currentFilter.is_active && (
          <div className="flex items-center gap-3 mt-4">
            <Badge variant="secondary" className="px-3 py-1 bg-gray-100 text-gray-700 border border-gray-200">
              Active
            </Badge>
            <span className="text-sm text-gray-600">
              {localConditions.filter(c => c.isValid).length} condition{localConditions.filter(c => c.isValid).length !== 1 ? 's' : ''} active
            </span>
          </div>
        )}

        {/* No Eligible Questions State */}
        {eligibleQuestions.length === 0 && (
          <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center bg-gray-50">
            <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-gray-900">No filterable questions available</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              Add a single-select, multi-select, boolean, or number question to your form to use exclusion filters.
            </p>
          </div>
        )}

        {/* Conditions */}
        {eligibleQuestions.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h4 className="text-base font-medium text-gray-900">Exclusion Conditions</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={addCondition}
                className="h-9 border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Condition
              </Button>
            </div>

            {/* Conditions List */}
            <AnimatePresence>
              {localConditions.map((condition, index) => (
                <motion.div
                  key={condition.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <ModernConditionBuilder
                    condition={condition}
                    questions={eligibleQuestions}
                    index={index}
                    showLogicOperator={index > 0}
                    onUpdate={(updates) => updateCondition(condition.id, updates)}
                    onRemove={() => removeCondition(condition.id)}
                  />
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Empty State */}
            {localConditions.length === 0 && (
              <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center bg-gray-50">
                <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2 text-gray-900">No exclusion conditions set</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Create conditions to automatically exclude submissions based on specific answers.
                </p>
                <Button
                  variant="outline"
                  onClick={addCondition}
                  className="h-12 px-8 border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Condition
                </Button>
              </div>
            )}

            {/* Logic Explanation */}
            {localConditions.length > 1 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Submissions will be excluded if <strong>ALL</strong> conditions are met (AND logic). 
                  For example, if you have conditions for "Industry = Healthcare" AND "Stage = Pre-Seed", 
                  only submissions that match both criteria will be excluded.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <div className="border-t border-gray-200 pt-6">
          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {hasChanges && (
                <Badge variant="secondary" className="px-3 py-1 bg-gray-100 text-gray-700 border border-gray-200">
                  Unsaved changes
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-3">
              {localConditions.length > 0 && (
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                  disabled={saving}
                  className="h-10 border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              )}

              <Button
                onClick={saveFilters}
                disabled={saving || !hasChanges || localConditions.filter(c => c.isValid).length === 0}
                className="h-10 px-6 bg-gray-900 hover:bg-gray-800 text-white"
              >
                {saving ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </div>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Filters
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Summary */}
        {currentFilter && currentFilter.is_active && localConditions.filter(c => c.isValid).length > 0 && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-6">
            <h4 className="font-semibold text-gray-900 mb-2">Active Exclusion Summary</h4>
            <p className="text-sm text-gray-600">
              Submissions will be automatically excluded when all {localConditions.filter(c => c.isValid).length} condition{localConditions.filter(c => c.isValid).length !== 1 ? 's are' : ' is'} met.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
