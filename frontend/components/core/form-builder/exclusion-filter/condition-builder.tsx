"use client"

import React, { useState, useEffect } from 'react';
import { Trash2, AlertCircle } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { OperatorSelector } from './operator-selector';
import { ValueInput } from './value-input';
import { Question } from '@/lib/types/form';
import {
  ConditionBuilderState,
  ExclusionOperator,
  getSupportedOperators
} from '@/lib/types/exclusion-filter';

interface ConditionBuilderProps {
  condition: ConditionBuilderState;
  questions: Question[];
  onUpdate: (condition: ConditionBuilderState) => void;
  onRemove: () => void;
  showRemove?: boolean;
}

export function ConditionBuilder({
  condition,
  questions,
  onUpdate,
  onRemove,
  showRemove = true
}: ConditionBuilderProps) {
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Find the selected question
  useEffect(() => {
    if (condition.question_id) {
      const question = questions.find(q => 
        (q._id === condition.question_id) || (q.id === condition.question_id)
      );
      setSelectedQuestion(question || null);
    } else {
      setSelectedQuestion(null);
    }
  }, [condition.question_id, questions]);

  // Validate the condition
  useEffect(() => {
    let isValid = true;
    let error = '';

    if (!condition.question_id) {
      isValid = false;
      error = 'Please select a question';
    } else if (!selectedQuestion) {
      isValid = false;
      error = 'Selected question not found';
    } else if (!condition.operator) {
      isValid = false;
      error = 'Please select an operator';
    } else if (condition.value === '' || condition.value === null || condition.value === undefined) {
      isValid = false;
      error = 'Please enter a value';
    } else {
      // Check if operator is supported for question type
      const supportedOperators = getSupportedOperators(selectedQuestion.type);
      if (!supportedOperators.some(op => op.value === condition.operator)) {
        isValid = false;
        error = `Operator "${condition.operator}" is not supported for ${selectedQuestion.type} questions`;
      }
    }

    setValidationError(error);
    
    // Update parent with validation status
    onUpdate({
      ...condition,
      isValid,
      error
    });
  }, [condition, selectedQuestion, onUpdate]);

  // Handle question selection
  const handleQuestionChange = (questionId: string) => {
    const question = questions.find(q => (q._id === questionId) || (q.id === questionId));
    
    onUpdate({
      ...condition,
      question_id: questionId,
      operator: 'eq' as ExclusionOperator, // Reset operator when question changes
      value: '', // Reset value when question changes
      isValid: false
    });
  };

  // Handle operator change
  const handleOperatorChange = (operator: ExclusionOperator) => {
    onUpdate({
      ...condition,
      operator,
      value: '', // Reset value when operator changes
      isValid: false
    });
  };

  // Handle value change
  const handleValueChange = (value: any) => {
    onUpdate({
      ...condition,
      value,
      isValid: false // Will be recalculated in useEffect
    });
  };

  return (
    <div className={`flex items-center gap-3 p-3 bg-white rounded-lg border mb-2 ${condition.isValid ? 'border-green-300' : 'border-red-300'}`}>
          {/* Question Selector */}
      <div className="flex-1 min-w-0">
            <Select value={condition.question_id} onValueChange={handleQuestionChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select question..." />
              </SelectTrigger>
              <SelectContent>
                {questions.map((question) => {
                  const questionId = question._id || question.id;
                  if (!questionId) return null;
                  
                  return (
                    <SelectItem key={questionId} value={questionId}>
                  <div className="flex flex-col items-start">
                    <span className="font-medium text-sm">{question.label}</span>
                        <span className="text-xs text-muted-foreground capitalize">
                          {question.type.replace('_', ' ')}
                        </span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Operator Selector */}
      <div className="flex-shrink-0">
            <OperatorSelector
              questionType={selectedQuestion?.type}
              value={condition.operator}
              onChange={handleOperatorChange}
              disabled={!selectedQuestion}
            />
          </div>

          {/* Value Input */}
      <div className="flex-shrink-0">
            <ValueInput
              question={selectedQuestion}
              operator={condition.operator}
              value={condition.value}
              onChange={handleValueChange}
              disabled={!selectedQuestion || !condition.operator}
            />
          </div>

          {/* Remove Button */}
            {showRemove && (
        <div className="flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
        </div>
      )}

      {/* Validation Error - Show as tooltip or small indicator */}
        {validationError && (
        <div className="absolute top-full left-0 right-0 mt-1 z-10">
          <Alert variant="destructive" className="text-xs">
            <AlertCircle className="h-3 w-3" />
            <AlertDescription className="text-xs">{validationError}</AlertDescription>
          </Alert>
          </div>
        )}
    </div>
  );
}
