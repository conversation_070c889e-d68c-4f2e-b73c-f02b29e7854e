"use client"

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

import { Question } from '@/lib/types/form';
import { ExclusionOperator } from '@/lib/types/exclusion-filter';

interface ValueInputProps {
  question?: Question | null;
  operator: ExclusionOperator;
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
}

export function ValueInput({
  question,
  operator,
  value,
  onChange,
  disabled = false
}: ValueInputProps) {
  // If no question is selected, show placeholder
  if (!question) {
    return (
      <Input
        placeholder="Select question and operator first..."
        disabled={true}
        className="w-[120px]"
      />
    );
  }

  // Handle different question types and operators
  const renderInput = () => {
    switch (question.type) {
      case 'boolean':
        return (
          <Select
            value={value?.toString()}
            onValueChange={(newValue) => onChange(newValue === 'true')}
            disabled={disabled}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select value..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">True</SelectItem>
              <SelectItem value="false">False</SelectItem>
            </SelectContent>
          </Select>
        );

      case 'single_select':
        if (operator === 'in' || operator === 'not_in') {
          // Multi-select for IN/NOT_IN operators
          return renderMultiSelectInput();
        } else {
          // Single select for other operators
          return (
            <Select
              value={value}
              onValueChange={onChange}
              disabled={disabled}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Select value..." />
              </SelectTrigger>
              <SelectContent>
                {question.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }

      case 'multi_select':
        if (operator === 'contains' || operator === 'not_contains') {
          // Single select for CONTAINS operators
          return (
            <Select
              value={value}
              onValueChange={onChange}
              disabled={disabled}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Select value..." />
              </SelectTrigger>
              <SelectContent>
                {question.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        } else {
          // Multi-select for other operators
          return renderMultiSelectInput();
        }

      case 'number':
      case 'range':
        return (
          <Input
            type="number"
            value={value || ''}
            onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
            placeholder="Enter number..."
            disabled={disabled}
            className="w-[120px]"
          />
        );

      case 'date':
        return (
          <Input
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            className="w-[120px]"
          />
        );

      case 'long_text':
        return (
          <Textarea
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Enter text..."
            disabled={disabled}
            rows={3}
          />
        );

      case 'short_text':
      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Enter text..."
            disabled={disabled}
            className="w-[120px]"
          />
        );
    }
  };

  // Render multi-select input for IN/NOT_IN operators
  const renderMultiSelectInput = () => {
    const selectedValues = Array.isArray(value) ? value : [];

    const toggleValue = (optionValue: string) => {
      if (selectedValues.includes(optionValue)) {
        onChange(selectedValues.filter(v => v !== optionValue));
      } else {
        onChange([...selectedValues, optionValue]);
      }
    };

    const removeValue = (optionValue: string) => {
      onChange(selectedValues.filter(v => v !== optionValue));
    };

    return (
      <div className="space-y-2">
        {/* Selected values */}
        {selectedValues.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {selectedValues.map((selectedValue) => {
              const option = question.options?.find(opt => opt.value === selectedValue);
              return (
                <Badge key={selectedValue} variant="secondary" className="text-xs">
                  {option?.label || selectedValue}
                  <button
                    type="button"
                    onClick={() => removeValue(selectedValue)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
        )}

        {/* Option selector */}
        <Select
          value=""
          onValueChange={toggleValue}
          disabled={disabled}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Add values..." />
          </SelectTrigger>
          <SelectContent>
            {question.options?.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                disabled={selectedValues.includes(option.value)}
              >
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedValues.includes(option.value)}
                    disabled
                  />
                  {option.label}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return renderInput();
}
