"use client"

import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface CompactBlockProps {
  type: 'section' | 'question';
  title: string;
  label?: string;
  required?: boolean;
  repeatable?: boolean;
  isCollapsed: boolean;
  onToggle: () => void;
  onExpand: () => void;
  children: React.ReactNode;
  className?: string;
}

export function CompactBlock({
  type,
  title,
  label,
  required,
  repeatable,
  isCollapsed,
  onToggle,
  onExpand,
  children,
  className,
}: CompactBlockProps) {
  const displayTitle = label || title;
  const typeLabel = type === 'section' ? 'Section' : 'Question';

  return (
    <div className={cn("relative group", className)}>
      {isCollapsed ? (
        <div
          className="flex items-center justify-between px-4 py-3 bg-card border rounded-md shadow-sm cursor-pointer hover:bg-accent/50 transition-all duration-300 ease-in-out"
          onClick={onExpand}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onExpand();
            }
          }}
          role="button"
          tabIndex={0}
          aria-expanded={!isCollapsed}
        >
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <span className="text-xs font-semibold text-muted-foreground whitespace-nowrap">
              {typeLabel}
            </span>
            <span 
              className="truncate font-medium" 
              title={displayTitle}
            >
              {displayTitle}
            </span>
          </div>
          <div className="flex items-center gap-2 ml-4">
            {typeof required !== "undefined" && (
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs",
                  required ? "bg-green-100 text-green-800 border-green-200" : "bg-muted"
                )}
              >
                {required ? "Required" : "Optional"}
              </Badge>
            )}
            {typeof repeatable !== "undefined" && (
              <Badge 
                variant="outline"
                className={cn(
                  "text-xs",
                  repeatable ? "bg-blue-100 text-blue-800 border-blue-200" : "bg-muted"
                )}
              >
                {repeatable ? "Repeatable" : "Single"}
              </Badge>
            )}
            <button
              className="ml-2 p-1 rounded-full hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={(e) => {
                e.stopPropagation();
                onToggle();
              }}
              aria-label={isCollapsed ? "Expand" : "Collapse"}
            >
              <ChevronDown 
                className={cn(
                  "h-4 w-4 text-muted-foreground transition-transform duration-300",
                  isCollapsed ? "" : "rotate-180"
                )} 
              />
            </button>
          </div>
        </div>
      ) : (
        <div 
          className={cn(
            "transition-all duration-300 ease-in-out",
            "opacity-100 scale-100 origin-top",
            "max-h-[2000px] overflow-hidden"
          )}
        >
          {children}
        </div>
      )}
    </div>
  );
} 