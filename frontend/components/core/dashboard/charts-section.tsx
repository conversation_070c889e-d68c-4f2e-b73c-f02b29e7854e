"use client"

import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Cell, 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveC<PERSON>r,
  <PERSON>
} from 'recharts';
import { cn } from '@/lib/utils';
import { DashboardSummary } from '@/lib/api/dashboard-api';

// Types for chart data
interface SectorData {
  name: string;
  value: number;
  color: string;
}

interface DealStageData {
  stage: string;
  count: number;
  color: string;
}

interface ChartsSectionProps {
  dashboardData: DashboardSummary;
  className?: string;
}

export function ChartsSection({
  dashboardData,
  className
}: ChartsSectionProps) {
  // Monochrome color palette with different shades of gray
  const sectorColors = [
    '#1f2937', '#374151', '#4b5563', '#6b7280', '#9ca3af',
    '#d1d5db', '#e5e7eb', '#f3f4f6', '#f9fafb', '#ffffff'
  ];

  const stageColors = [
    '#1f2937', '#374151', '#4b5563', '#6b7280', '#9ca3af', '#d1d5db'
  ];

  // Transform dashboard data for charts with patterns
  const sectorData: SectorData[] = dashboardData.sector_distribution.map((item, index) => ({
    name: item.sector,
    value: item.count,
    color: sectorColors[index % sectorColors.length]
  }));

  const dealStagesData: DealStageData[] = dashboardData.deal_stages.map((item, index) => ({
    stage: item.stage,
    count: item.count,
    color: stageColors[index % stageColors.length]
  }));
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const chartVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-muted-foreground">
            {`${payload[0].name}: ${payload[0].value}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-sm text-muted-foreground">
            {`${payload[0].value}% of deals`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        // Mobile-first responsive grid
        "grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8",
        className
      )}
    >
      {/* Sector Distribution Chart */}
      <motion.div variants={chartVariants} className="col-span-1">
        <Card className={cn(
          "border border-gray-200 shadow-sm bg-white",
          // Mobile-first height
          "h-[400px] md:h-[450px]"
        )}>
          <CardHeader className={cn(
            // Mobile-first header padding
            "pb-3 md:pb-4"
          )}>
            <CardTitle className={cn(
              "font-bold text-gray-900",
              // Mobile-first title sizing
              "text-lg md:text-xl"
            )}>
              Sector Distribution
            </CardTitle>
            <p className={cn(
              "text-gray-600",
              // Mobile-first description sizing
              "text-sm md:text-base"
            )}>
              Portfolio breakdown by industry sector
            </p>
          </CardHeader>
          <CardContent className={cn(
            // Mobile-first chart height
            "h-[280px] md:h-[320px]"
          )}>
            {sectorData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={sectorData}
                    cx="50%"
                    cy="50%"
                    innerRadius="40%"
                    outerRadius="70%"
                    paddingAngle={2}
                    dataKey="value"
                    animationBegin={0}
                    animationDuration={1000}
                  >
                  {sectorData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                  </Pie>
                  <Tooltip content={<CustomPieTooltip />} />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    iconType="circle"
                    wrapperStyle={{ fontSize: '11px' }}
                    layout="horizontal"
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <p className="text-muted-foreground text-sm">No sector data yet</p>
                  <p className="text-muted-foreground text-xs mt-1">Data will appear as deals are created</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Deal Stages Chart */}
      <motion.div variants={chartVariants} className="col-span-1">
        <Card className="h-[450px] border border-gray-200 shadow-sm bg-white">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-900">Deal Stages</CardTitle>
            <p className="text-base text-gray-600">
              Active deals by funding stage
            </p>
          </CardHeader>
          <CardContent className="h-[320px]">
            {dealStagesData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={dealStagesData}
                  margin={{
                    top: 10,
                    right: 10,
                    left: 10,
                    bottom: 20
                  }}
                >
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis
                  dataKey="stage"
                  tick={{ fontSize: 11, fill: '#6b7280' }}
                  stroke="#9ca3af"
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  tick={{ fontSize: 11, fill: '#6b7280' }}
                  stroke="#9ca3af"
                  width={30}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="count" 
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                  animationBegin={200}
                >
                  {dealStagesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <p className="text-muted-foreground text-sm">No stage data yet</p>
                  <p className="text-muted-foreground text-xs mt-1">Data will appear as deals are created</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
