"use client"

import React from 'react';
import { motion, type Variants } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, FileText, Target, Cpu, ArrowUpRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { visualRetreat } from '@/lib/utils/responsive';
import { DashboardSummary } from '@/lib/api/dashboard-api';
import Link from 'next/link';

interface SummaryTilesProps {
  dashboardData: DashboardSummary;
  className?: string;
}

interface TileData {
  title: string;
  value: string | number;
  trend?: {
    value: number;
    label: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  href: string;
}

// Safe motion wrapper to handle potential parsing issues
const MotionDiv = motion.div;

export function SummaryTiles({ dashboardData, className }: SummaryTilesProps) {
  const tiles: TileData[] = [
    {
      title: 'Active Deals',
      value: dashboardData.active_deals,
      trend: {
        value: dashboardData.active_deals_change_pct,
        label: 'this month'
      },
      icon: TrendingUp,
      href: '/deals'
    },
    {
      title: 'Forms',
      value: dashboardData.forms,
      icon: FileText,
      href: '/forms'
    },
    {
      title: 'Theses',
      value: dashboardData.theses,
      icon: Target,
      href: '/theses'
    },
    {
      title: dashboardData.ai_activity.active ? 'AI Activity' : 'AI Activity: Coming Soon',
      value: dashboardData.ai_activity.active ? 'Active' : 'Inactive',
      trend: dashboardData.ai_activity.last_sync ? {
        value: 0,
        label: `Last sync: ${new Date(dashboardData.ai_activity.last_sync).toLocaleDateString()}`
      } : {
        value: 0,
        label: 'No sync yet'
      },
      icon: Cpu,
      href: '/dashboard'
    }
  ];

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const tileVariants: Variants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const CountUpNumber = ({ value, duration = 1000 }: { value: number; duration?: number }) => {
    const [displayValue, setDisplayValue] = React.useState(0);
    
    React.useEffect(() => {
      let startTime: number;
      let animationFrame: number;

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / duration, 1);

        setDisplayValue(Math.floor(progress * value));

        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);

      return () => {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }
      };
    }, [value, duration]);

    return <span>{displayValue}</span>;
  };

  return (
    <MotionDiv
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        // Mobile-first responsive grid
        "grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8",
        className
      )}
    >
      {tiles.map((tile, index) => (
        <MotionDiv key={tile.title} variants={tileVariants} className="h-full">
          <Link href={tile.href} className="block h-full">
            <div className="flex flex-col h-full">
              <Card className={cn(
                visualRetreat.card.base,
                visualRetreat.card.interactive,
                "group cursor-pointer h-full flex flex-col"
              )}>
                <CardHeader className={cn(
                  "flex flex-row items-center justify-between space-y-0 flex-shrink-0",
                  // Mobile-first padding
                  "p-4 pb-3 md:p-6 md:pb-4"
                )}>
                  <CardTitle className={cn(
                    "font-semibold text-gray-700 truncate pr-2",
                    // Mobile-first text sizing
                    "text-sm md:text-base"
                  )}>
                    {tile.title}
                  </CardTitle>
                  <div className={cn(
                    "rounded-xl flex-shrink-0 bg-gray-100",
                    // Mobile-first icon sizing
                    "p-2 md:p-3"
                  )}>
                    <tile.icon className="h-4 w-4 md:h-5 md:w-5 text-gray-700" />
                  </div>
                </CardHeader>
                <CardContent className={cn(
                  "flex-1 flex flex-col justify-between",
                  // Mobile-first content padding
                  "p-4 pt-0 md:p-6 md:pt-0"
                )}>
                  <div className="flex items-start justify-between h-full">
                    <div className="flex-1 min-w-0">
                      <div className={cn(
                        "font-bold text-gray-900",
                        // Mobile-first value sizing
                        "text-2xl mb-2 md:text-3xl md:mb-3"
                      )}>
                        {typeof tile.value === 'number' ? (
                          <CountUpNumber value={tile.value} />
                        ) : (
                          <span className="truncate block">{tile.value}</span>
                        )}
                      </div>
                      {tile.trend && (
                        <div className="flex flex-col gap-1 md:gap-2">
                          {tile.trend.value > 0 && (
                            <Badge variant="secondary" className={cn(
                              "bg-gray-100 text-gray-700 hover:bg-gray-100 w-fit border border-gray-200",
                              // Mobile-first badge sizing
                              "text-xs px-2 py-1 md:text-sm md:px-3"
                            )}>
                              +{tile.trend.value}%
                            </Badge>
                          )}
                          <span className={cn(
                            "text-gray-600 line-clamp-2",
                            // Mobile-first text sizing
                            "text-xs md:text-sm"
                          )}>
                            {tile.trend.label}
                          </span>
                        </div>
                      )}
                    </div>
                    <ArrowUpRight className="h-4 w-4 text-gray-400 group-hover:text-gray-700 transition-colors flex-shrink-0 ml-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </Link>
        </MotionDiv>
      ))}
    </MotionDiv>
  );
}
