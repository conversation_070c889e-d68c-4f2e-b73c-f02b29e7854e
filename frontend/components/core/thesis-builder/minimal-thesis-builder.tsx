"use client"

import React, { useState } from 'react';
import { Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/components/ui/use-toast';

interface MinimalThesisBuilderProps {
  onSaveSuccess?: () => void;
}

export function MinimalThesisBuilder({ onSaveSuccess }: MinimalThesisBuilderProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  console.log('🎯 MinimalThesisBuilder loaded');

  const handleSave = async () => {
    try {
      if (!name.trim() || !description.trim()) {
        toast({
          title: "Missing information",
          description: "Please provide both name and description.",
          variant: "destructive",
        });
        return;
      }

      setIsSaving(true);
      
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success",
        description: "Thesis saved successfully!",
      });

      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error('Error saving:', error);
      toast({
        title: "Error",
        description: "Failed to save thesis.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Create New Thesis (Minimal)
          </h1>
          <p className="text-muted-foreground">
            Minimal thesis builder for testing
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={!name.trim() || !description.trim() || isSaving}
          size="lg"
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save Thesis'}
        </Button>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Provide basic details about your investment thesis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Thesis Name *</label>
            <Input
              placeholder="e.g., Early Stage SaaS Thesis"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Description *</label>
            <Textarea
              placeholder="Describe your investment thesis..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Status */}
      <Alert>
        <AlertDescription>
          This is a minimal version of the thesis builder for testing purposes.
          Name: {name || 'Not set'} | Description: {description ? 'Set' : 'Not set'}
        </AlertDescription>
      </Alert>
    </div>
  );
}
