"use client"

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Filter, X, ChevronDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FilterCondition, CompoundFilter, ConditionOperator, LogicalOperator } from '@/lib/types/thesis';

// Use thesis types for consistency
type ConditionItem = FilterCondition | CompoundFilter;

interface Question {
  _id?: string;
  id?: string;
  label: string;
  type: string;
  section_title: string;
  options?: Array<{ label: string; value: string }>;
}

interface ConditionBuilderProps {
  conditions: ConditionItem[];
  onConditionsChange: (conditions: ConditionItem[]) => void;
  allQuestions: Question[];
  className?: string;
  readOnly?: boolean;
}

// Only allow MCQ, Boolean, and Number question types for matching rules
const ALLOWED_QUESTION_TYPES = ['single_select', 'multi_select', 'boolean', 'number'];

// Operator mappings for allowed question types only
const OPERATORS_BY_TYPE = {
  single_select: [
    { value: ConditionOperator.EQUALS, label: 'Equals' },
    { value: ConditionOperator.NOT_EQUALS, label: 'Not Equals' },
    { value: ConditionOperator.IN, label: 'Is One Of' },
    { value: ConditionOperator.NOT_IN, label: 'Is Not One Of' }
  ],
  multi_select: [
    { value: ConditionOperator.CONTAINS, label: 'Contains' },
    { value: ConditionOperator.NOT_CONTAINS, label: 'Does Not Contain' },
    { value: ConditionOperator.IN, label: 'Includes Any Of' },
    { value: ConditionOperator.NOT_IN, label: 'Excludes All Of' }
  ],
  boolean: [
    { value: ConditionOperator.EQUALS, label: 'Equals' }
  ],
  number: [
    { value: ConditionOperator.EQUALS, label: 'Equals' },
    { value: ConditionOperator.NOT_EQUALS, label: 'Not Equals' },
    { value: ConditionOperator.GREATER_THAN, label: 'Greater Than' },
    { value: ConditionOperator.LESS_THAN, label: 'Less Than' },
    { value: ConditionOperator.GREATER_THAN_EQUALS, label: 'Greater Than or Equal' },
    { value: ConditionOperator.LESS_THAN_EQUALS, label: 'Less Than or Equal' }
  ]
};

// Notion-style Tags Picker Component
interface TagsPickerProps {
  question: Question;
  selectedValues: any[];
  onValuesChange: (values: any[]) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Value Input Component for different question types
interface ValueInputProps {
  question: Question;
  value: any;
  onValueChange: (value: any) => void;
  placeholder?: string;
  disabled?: boolean;
}

function ValueInput({ question, value, onValueChange, placeholder = "Enter value...", disabled = false }: ValueInputProps) {
  switch (question.type) {
    case 'number':
      return (
        <Input
          type="number"
          value={value ?? ''}
          onChange={(e) => onValueChange(e.target.value ? Number(e.target.value) : '')}
          placeholder={placeholder}
          disabled={disabled}
          className="h-9 rounded-md px-3 text-base border border-border focus:ring-2 focus:ring-primary"
        />
      );
    case 'boolean':
      return (
        <Select
          value={value === true ? 'true' : value === false ? 'false' : ''}
          onValueChange={(v) => onValueChange(v === 'true')}
          disabled={disabled}
        >
          <SelectTrigger className="w-full h-9 rounded-md px-3 text-base border border-border focus:ring-2 focus:ring-primary">
            <SelectValue placeholder="Select Yes or No" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">Yes</SelectItem>
            <SelectItem value="false">No</SelectItem>
          </SelectContent>
        </Select>
      );
    case 'single_select':
    case 'multi_select':
      // Use TagsPicker for MCQ questions
      return (
        <TagsPicker
          question={question}
          selectedValues={Array.isArray(value) ? value : (value ? [value] : [])}
          onValuesChange={onValueChange}
          placeholder={placeholder}
          disabled={disabled}
        />
      );
    
    default:
      return (
        <Input
          value={value || ''}
          onChange={(e) => onValueChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="h-9 rounded-md px-3 text-base border border-border focus:ring-2 focus:ring-primary"
        />
      );
  }
}

function TagsPicker({ question, selectedValues, onValuesChange, placeholder = "Select options...", disabled = false }: TagsPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, direction: 'down' as 'down' | 'up' });
  const [isMounted, setIsMounted] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Ensure component is mounted (client-side)
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Get available options based on question type
  const availableOptions = React.useMemo(() => {
    if (question.type === 'boolean') {
      return [
        { label: 'Yes', value: 'true' },
        { label: 'No', value: 'false' }
      ];
    }
    return question.options || [];
  }, [question]);

  // Filter options based on search term
  const filteredOptions = React.useMemo(() => {
    if (!searchTerm) return availableOptions;
    return availableOptions.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [availableOptions, searchTerm]);

  // Convert backend values to display values
  const displayValues = React.useMemo(() => {
    return selectedValues.map(value => {
      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
      }
      return String(value);
    });
  }, [selectedValues]);

  // Convert display values back to backend format
  const convertToBackendFormat = (values: string[]) => {
    if (question.type === 'boolean') {
      return values.map(value => value === 'Yes');
    }
    return values;
  };

  // Calculate dropdown position with smart upward/downward flipping
  const updatePosition = () => {
    if (!triggerRef.current) {
      console.log('❌ updatePosition: triggerRef.current is null');
      return;
    }

    const rect = triggerRef.current.getBoundingClientRect();
    const scrollY = window.pageYOffset || window.scrollY || 0;
    const scrollX = window.pageXOffset || window.scrollX || 0;
    const dropdownHeight = 320;
    const spaceBelow = window.innerHeight - rect.bottom;
    const spaceAbove = rect.top;

    let top = rect.bottom + scrollY + 4;
    let direction: 'down' | 'up' = 'down';

    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
      top = rect.top + scrollY - dropdownHeight - 4;
      direction = 'up';
    }

    // Calculate final position with viewport bounds checking
    let finalTop = Math.max(10, isNaN(top) ? 100 : top);
    let finalLeft = Math.max(10, isNaN(rect.left + scrollX) ? 100 : rect.left + scrollX);
    let finalWidth = Math.max(200, isNaN(rect.width) ? 300 : rect.width);

    // Ensure dropdown stays within viewport bounds
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const dropdownWidth = Math.min(finalWidth, 400); // Max 400px wide
    const maxDropdownHeight = 320;

    // Horizontal bounds check
    if (finalLeft + dropdownWidth > viewportWidth - 20) {
      finalLeft = Math.max(20, viewportWidth - dropdownWidth - 20);
    }
    if (finalLeft < 20) {
      finalLeft = 20;
    }

    // Vertical bounds check
    if (finalTop + maxDropdownHeight > viewportHeight - 20) {
      finalTop = Math.max(20, viewportHeight - maxDropdownHeight - 20);
    }
    if (finalTop < 20) {
      finalTop = 20;
    }

    // If still off-screen, center it
    if (finalLeft > viewportWidth - 100 || finalTop > viewportHeight - 100) {
      finalLeft = Math.max(20, (viewportWidth - dropdownWidth) / 2);
      finalTop = Math.max(20, (viewportHeight - maxDropdownHeight) / 2);
    }

    const finalPosition = {
      top: finalTop,
      left: finalLeft,
      width: dropdownWidth,
      direction,
    };

    console.log('🎯 Dropdown position calculated:', {
      triggerRect: rect,
      scrollY,
      scrollX,
      spaceBelow,
      spaceAbove,
      finalPosition,
      windowSize: { width: window.innerWidth, height: window.innerHeight }
    });

    setPosition(finalPosition);
  };

  // Update position when opening
  useEffect(() => {
    if (isOpen && isMounted) {
      updatePosition();
      
      // Update position on scroll/resize
      const handleUpdate = () => updatePosition();
      window.addEventListener('scroll', handleUpdate, true);
      window.addEventListener('resize', handleUpdate);
      
      return () => {
        window.removeEventListener('scroll', handleUpdate, true);
        window.removeEventListener('resize', handleUpdate);
      };
    }
  }, [isOpen, isMounted]);

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        triggerRef.current && 
        !triggerRef.current.contains(event.target as Node) &&
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    function handleEscapeKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isOpen]);

  const toggleOption = (value: string) => {
    const newDisplayValues = displayValues.includes(value)
      ? displayValues.filter(v => v !== value)
      : [...displayValues, value];
    
    const newBackendValues = convertToBackendFormat(newDisplayValues);
    onValuesChange(newBackendValues);
  };

  const removeValue = (value: string) => {
    const newDisplayValues = displayValues.filter(v => v !== value);
    const newBackendValues = convertToBackendFormat(newDisplayValues);
    onValuesChange(newBackendValues);
  };

  const getOptionLabel = (value: string) => {
    const option = availableOptions.find(opt => opt.value === value);
    return option?.label || value;
  };

  return (
    <>
      {/* Selected Tags Display */}
      <div
        ref={triggerRef}
        className={cn(
          "min-h-[32px] w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background",
          "flex flex-wrap gap-1 items-center cursor-pointer",
          disabled && "opacity-50 cursor-not-allowed",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            console.log('🖱️ TagsPicker clicked, current state:', { isOpen, disabled, hasRef: !!triggerRef.current });
            const newIsOpen = !isOpen;
            setIsOpen(newIsOpen);
            
            // Immediately update position when opening
            if (newIsOpen) {
              setTimeout(() => {
                console.log('⏰ Triggering position update after state change');
                updatePosition();
              }, 0);
            }
          }
        }}
      >
        {selectedValues.length > 0 ? (
          selectedValues.map((value) => (
            <Badge
              key={value}
              variant="secondary"
              className="text-xs h-6 px-2 gap-1"
            >
              {getOptionLabel(value)}
              {!disabled && (
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeValue(value);
                  }}
                />
              )}
            </Badge>
          ))
        ) : (
          <span className="text-muted-foreground">{placeholder}</span>
        )}
        <ChevronDown className={cn("h-4 w-4 ml-auto transition-transform", isOpen && "rotate-180")} />
      </div>

      {/* Portal Dropdown */}
      {isMounted && isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[99999] bg-popover text-popover-foreground border rounded-md shadow-lg max-h-80 overflow-hidden"
          style={{
            top: position.top,
            left: position.left,
            width: position.width,
            minWidth: '200px',
            boxShadow: "0 4px 24px rgba(0,0,0,0.16)"
          }}
        >

          {/* Search Input */}
          <div className="p-2 border-b">
            <Input
              ref={inputRef}
              placeholder="Search options..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-8"
              autoFocus
            />
          </div>

          {/* Options List */}
          <div className="max-h-40 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => {
                const isSelected = selectedValues.includes(option.value);
                return (
                  <div
                    key={option.value}
                    className={cn(
                      "px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",
                      "flex items-center justify-between",
                      isSelected && "bg-accent text-accent-foreground"
                    )}
                    onClick={() => {
                      toggleOption(option.value);
                    }}
                  >
                    <span className="flex-1 truncate">{option.label}</span>
                    {isSelected && (
                      <Check className="h-4 w-4 ml-2" />
                    )}
                  </div>
                );
              })
            ) : (
              <div className="px-3 py-2 text-sm text-muted-foreground">
                No options found
              </div>
            )}
          </div>
        </div>,
        document.body
      )}
    </>
  );
}

export function ConditionBuilder({
  conditions,
  onConditionsChange,
  allQuestions,
  className,
  readOnly
}: ConditionBuilderProps) {
  // Filter questions to only include MCQ and Boolean types
  const allowedQuestions = React.useMemo(() => {
    return allQuestions.filter(question =>
      ALLOWED_QUESTION_TYPES.includes(question.type)
    );
  }, [allQuestions]);

  const addSimpleCondition = () => {
    const newCondition: FilterCondition = {
      question_id: '',
      operator: ConditionOperator.EQUALS,
      value: []
    };

    onConditionsChange([...conditions, newCondition]);
  };

  const addCompoundCondition = () => {
    const newCondition: CompoundFilter = {
      operator: LogicalOperator.AND,
      conditions: []
    };

    onConditionsChange([...conditions, newCondition]);
  };

  const removeCondition = (index: number) => {
    const updatedConditions = conditions.filter((_, i) => i !== index);
    onConditionsChange(updatedConditions);
  };

  const updateCondition = (index: number, updates: Partial<ConditionItem>) => {
    const updatedConditions = conditions.map((condition, i) =>
      i === index ? { ...condition, ...updates } as ConditionItem : condition
    );
    onConditionsChange(updatedConditions);
  };

  const isSimpleCondition = (condition: ConditionItem): condition is FilterCondition => {
    return 'question_id' in condition;
  };

  return (
    <div className={cn("space-y-3", className)}>
      {/* Add Condition Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addSimpleCondition}
          disabled={allowedQuestions.length === 0 || readOnly}
        >
          <Plus className="h-4 w-4" />
          Add Condition
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={addCompoundCondition}
          disabled={allowedQuestions.length === 0 || readOnly}
        >
          <Plus className="h-4 w-4" />
          Add Group
        </Button>
      </div>

      {/* Conditions List */}
      <div className="space-y-3">
        <AnimatePresence mode="popLayout">
          {conditions.map((condition, index) => (
            <motion.div
              key={`condition-${index}`}
              layout
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {isSimpleCondition(condition) ? (
                <SimpleConditionCard
                  condition={condition}
                  index={index}
                  onUpdate={(updates) => updateCondition(index, updates)}
                  onRemove={() => removeCondition(index)}
                  allQuestions={allowedQuestions}
                  readOnly={readOnly}
                />
              ) : (
                <CompoundConditionCard
                  condition={condition}
                  index={index}
                  onUpdate={(updates) => updateCondition(index, updates)}
                  onRemove={() => removeCondition(index)}
                  allQuestions={allowedQuestions}
                  readOnly={readOnly}
                />
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {conditions.length === 0 && allowedQuestions.length > 0 && (
        <div className="text-center py-6 text-muted-foreground bg-muted/30 rounded-lg">
          <Filter className="h-6 w-6 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No conditions defined yet</p>
          <p className="text-xs">Add conditions to filter deals based on form responses</p>
        </div>
      )}

      {/* No Allowed Questions Warning */}
      {allowedQuestions.length === 0 && allQuestions.length > 0 && (
        <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg border border-amber-200">
          <p className="font-medium">No MCQ or Boolean questions available</p>
          <p>Matching rules can only use Multiple Choice (single/multi-select) or Boolean questions. Please add these question types to your form.</p>
        </div>
      )}

      {/* No Questions Warning */}
      {allQuestions.length === 0 && (
        <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg border border-amber-200">
          <p className="font-medium">No questions available</p>
          <p>The selected form has no questions. Please ensure the form has questions defined.</p>
        </div>
      )}
    </div>
  );
}

interface SimpleConditionCardProps {
  condition: FilterCondition;
  index: number;
  onUpdate: (updates: Partial<FilterCondition>) => void;
  onRemove: () => void;
  allQuestions: Question[];
  readOnly?: boolean;
}

function SimpleConditionCard({
  condition,
  index,
  onUpdate,
  onRemove,
  allQuestions,
  readOnly
}: SimpleConditionCardProps) {
  const selectedQuestion = allQuestions.find(q =>
    (q._id || q.id) === condition.question_id
  );

  const availableOperators = selectedQuestion
    ? OPERATORS_BY_TYPE[selectedQuestion.type as keyof typeof OPERATORS_BY_TYPE] || []
    : [];

  return (
    <Card className="relative px-4 py-4 bg-background rounded-xl border border-border/60 flex items-center gap-x-6 shadow-sm overflow-visible">
      {/* Colored left accent */}
      <div className="absolute left-0 top-0 bottom-0 w-1 bg-green-500 rounded-tl-xl rounded-bl-xl" />

      {/* Badge */}
      <div className="flex flex-col items-center justify-center min-w-[80px] mr-2">
        <Badge variant="secondary" className="text-xs px-3 py-1 rounded-full bg-muted">{'Condition'}</Badge>
      </div>

      {/* Inputs: Question / Operator / Value */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 items-end">
        {/* Question */}
        <div className="flex flex-col gap-1">
          <Label className="text-xs font-medium text-muted-foreground">Question</Label>
          <Select
            value={condition.question_id}
            onValueChange={(value) => onUpdate({ question_id: value })}
            disabled={readOnly}
          >
            <SelectTrigger className="h-9 rounded-md px-3 text-base bg-background border border-border focus:ring-2 focus:ring-primary">
              <SelectValue placeholder="Select question..." />
            </SelectTrigger>
            <SelectContent className="rounded-md py-1">
              {allQuestions.map((question) => {
                const questionId = question._id || question.id;
                if (!questionId) return null;
                return (
                  <SelectItem key={questionId} value={questionId} className="py-2 px-3">
                    <div className="flex flex-col items-start w-full gap-1">
                      <span className="font-medium text-base truncate max-w-[220px] sm:max-w-[320px]">
                        {question.label}
                      </span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Operator */}
        <div className="flex flex-col gap-1">
          <Label className="text-xs font-medium text-muted-foreground">Operator</Label>
          <Select
            value={condition.operator}
            onValueChange={(value) => onUpdate({ operator: value as ConditionOperator })}
            disabled={!selectedQuestion || readOnly}
          >
            <SelectTrigger className="h-9 rounded-md px-3 text-base bg-background border border-border focus:ring-2 focus:ring-primary">
              <SelectValue placeholder="Select operator..." />
            </SelectTrigger>
            <SelectContent className="rounded-md py-1">
              {availableOperators.map((op) => (
                <SelectItem key={op.value} value={op.value} className="py-2 px-3 text-sm">
                  {op.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Options */}
        <div className="flex flex-col gap-1">
          <Label className="text-xs font-medium text-muted-foreground">Options</Label>
          {selectedQuestion ? (
            (selectedQuestion.type === 'boolean' || selectedQuestion.type === 'number') ? (
              <ValueInput
                question={selectedQuestion}
                value={condition.value}
                onValueChange={(value) => onUpdate({ value })}
                placeholder={selectedQuestion.type === 'boolean' ? 'Select Yes or No' : 'Enter numeric value'}
                disabled={readOnly}
              />
            ) : (
            <TagsPicker
              question={selectedQuestion}
              selectedValues={Array.isArray(condition.value) ? condition.value : (condition.value ? [condition.value] : [])}
              onValuesChange={(values) => onUpdate({ value: values })}
              placeholder="Select options..."
                disabled={readOnly}
            />
            )
          ) : (
            <div className="min-h-[32px] w-full rounded-md border border-input bg-muted px-3 py-1 text-sm text-muted-foreground flex items-center">
              Select question first
            </div>
          )}
          {selectedQuestion && (
            (selectedQuestion.type === 'number' && (!condition.value && condition.value !== 0)) && (
              <p className="text-xs text-amber-600">Enter a numeric value to save this condition.</p>
            )
          )}
          {selectedQuestion && selectedQuestion.type !== 'number' && selectedQuestion.type !== 'boolean' && Array.isArray(condition.value) && condition.value.length === 0 && (
            <p className="text-xs text-amber-600">Select at least one option to save this condition.</p>
          )}
        </div>
      </div>

      {/* Delete Button */}
      <Button
      variant="ghost"
      size="sm"
      onClick={onRemove}
      className="text-destructive hover:text-destructive rounded-full flex items-center justify-center"
      title="Delete condition"
      type="button"
      disabled={readOnly}
    >
      <Trash2 className="h-5 w-5" />
    </Button>
    </Card>
  );
}

interface CompoundConditionCardProps {
  condition: CompoundFilter;
  index: number;
  onUpdate: (updates: Partial<CompoundFilter>) => void;
  onRemove: () => void;
  allQuestions: Question[];
  readOnly?: boolean;
}

function CompoundConditionCard({
  condition,
  index,
  onUpdate,
  onRemove,
  allQuestions,
  readOnly
}: CompoundConditionCardProps) {
  return (
    <Card className="p-3 bg-muted/50 border-l-4 border-l-purple-500">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="text-xs">
              Group
            </Badge>
            <Select
              value={condition.operator}
              onValueChange={(value: 'and' | 'or') => onUpdate({ operator: value as LogicalOperator })}
              disabled={readOnly}
            >
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="and">AND</SelectItem>
                <SelectItem value="or">OR</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="ghost"
            size="default"
            onClick={onRemove}
            className="text-destructive hover:text-destructive h-8 w-8 p-0"
            disabled={readOnly}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Nested Conditions */}
        <div className="ml-4 border-l-2 border-muted-foreground/20 pl-4">
          <ConditionBuilder
            conditions={condition.conditions}
            onConditionsChange={(conditions) => onUpdate({ conditions })}
            allQuestions={allQuestions}
            readOnly={readOnly}
          />
        </div>
      </div>
    </Card>
  );
}


