"use client"

import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, FileText } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { Form } from '@/lib/types/form';
import { FormAPI } from '@/lib/api/form-api';
import { toast } from '@/components/ui/use-toast';

interface FormSelectorProps {
  selectedFormId?: string;
  onFormSelect: (formId: string) => void;
  disabled?: boolean;
}

export function FormSelector({ selectedFormId, onFormSelect, disabled }: FormSelectorProps) {
  const [open, setOpen] = useState(false);
  const [forms, setForms] = useState<Form[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedForm, setSelectedForm] = useState<Form | undefined>();

  console.log('📋 FormSelector props:', { selectedFormId, disabled });

  // Load forms on mount
  useEffect(() => {
    loadForms();
  }, []);

  // Update selected form when selectedFormId changes
  useEffect(() => {
    if (selectedFormId && forms.length > 0) {
      const form = forms.find(f => (f._id || f.id) === selectedFormId);
      setSelectedForm(form);
      console.log('📋 Selected form updated:', form);
    }
  }, [selectedFormId, forms]);

  const loadForms = async () => {
    try {
      console.log('📋 Loading available forms...');
      setIsLoading(true);

      const formsList = await FormAPI.listForms();
      console.log('📋 Forms loaded:', formsList);

      setForms(formsList);
    } catch (error) {
      console.error('❌ Error loading forms:', error);
      toast({
        title: "Error loading forms",
        description: "Failed to load available forms. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSelect = (form: Form) => {
    const formId = form._id || form.id;
    if (!formId) {
      console.error('❌ Form missing ID:', form);
      return;
    }

    console.log('📋 Form selected:', form);
    setSelectedForm(form);
    onFormSelect(formId);
    setOpen(false);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Select Form
          </CardTitle>
          <CardDescription>
            Choose the form this thesis will apply to
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Select Form
        </CardTitle>
        <CardDescription>
          Choose the form this thesis will apply to. This determines which questions will be available for scoring and matching.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between"
              disabled={disabled}
            >
              {selectedForm ? (
                <div className="flex items-center gap-2">
                  <span className="truncate">{selectedForm.name}</span>
                  <Badge variant="secondary" className="ml-auto">
                    {selectedForm.sections?.length || 0} sections
                  </Badge>
                </div>
              ) : (
                "Select a form..."
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandInput placeholder="Search forms..." />
              <CommandEmpty>No forms found.</CommandEmpty>
              <CommandGroup>
                {forms.map((form) => {
                  const formId = form._id || form.id;
                  const isSelected = selectedFormId === formId;

                  return (
                    <CommandItem
                      key={formId}
                      value={form.name}
                      onSelect={() => handleFormSelect(form)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <Check
                          className={cn(
                            "h-4 w-4",
                            isSelected ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium">{form.name}</span>
                          {form.description && (
                            <span className="text-sm text-muted-foreground truncate">
                              {form.description}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {form.sections?.length || 0} sections
                        </Badge>
                        {form.is_active && (
                          <Badge variant="default" className="text-xs">
                            Active
                          </Badge>
                        )}
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>

        {selectedForm && (
          <div className="rounded-lg border bg-muted/50 p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h4 className="font-medium">{selectedForm.name}</h4>
                {selectedForm.description && (
                  <p className="text-sm text-muted-foreground">
                    {selectedForm.description}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {selectedForm.sections?.length || 0} sections
                </Badge>
                <Badge variant="outline">
                  {selectedForm.sections?.reduce((total, section) =>
                    total + (section.questions?.length || 0), 0
                  ) || 0} questions
                </Badge>
              </div>
            </div>

            {selectedForm.sections && selectedForm.sections.length > 0 && (
              <div className="mt-3 space-y-2">
                <h5 className="text-sm font-medium text-muted-foreground">Sections:</h5>
                <div className="flex flex-wrap gap-2">
                  {selectedForm.sections.map((section, index) => (
                    <Badge key={section._id || section.id || index} variant="secondary" className="text-xs">
                      {section.title}
                      {section.repeatable && " (repeatable)"}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
