"use client"

import React, { useState } from 'react';
import { Save, Target, Calculator, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/components/ui/use-toast';
import { ThesisBuilderErrorBoundary } from './error-boundary';

interface SafeThesisBuilderProps {
  onSaveSuccess?: () => void;
}

export function SafeThesisBuilder({ onSaveSuccess }: SafeThesisBuilderProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  console.log('🎯 SafeThesisBuilder loaded');

  const handleSave = async () => {
    try {
      if (!name.trim() || !description.trim()) {
        toast({
          title: "Missing information",
          description: "Please provide both name and description.",
          variant: "destructive",
        });
        return;
      }

      setIsSaving(true);
      
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success",
        description: "Thesis saved successfully!",
      });

      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error('Error saving:', error);
      toast({
        title: "Error",
        description: "Failed to save thesis.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const SafeTabContent = ({ children, tabName }: { children: React.ReactNode; tabName: string }) => (
    <ThesisBuilderErrorBoundary>
      <div className="space-y-4">
        <Alert>
          <AlertDescription>
            {tabName} tab loaded successfully! No crashes detected.
          </AlertDescription>
        </Alert>
        {children}
      </div>
    </ThesisBuilderErrorBoundary>
  );

  return (
    <ThesisBuilderErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Safe Thesis Builder
            </h1>
            <p className="text-muted-foreground">
              Crash-proof thesis builder for testing
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={!name.trim() || !description.trim() || isSaving}
            size="lg"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Thesis'}
          </Button>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Provide basic details about your investment thesis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Thesis Name *</label>
              <Input
                placeholder="e.g., Early Stage SaaS Thesis"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Description *</label>
              <Textarea
                placeholder="Describe your investment thesis..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Configuration Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Basic
            </TabsTrigger>
            <TabsTrigger value="matching" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Matching
            </TabsTrigger>
            <TabsTrigger value="scoring" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Scoring
            </TabsTrigger>
            <TabsTrigger value="bonus" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Bonus
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <SafeTabContent tabName="Basic">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Configuration</CardTitle>
                  <CardDescription>
                    Basic thesis settings and information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    This tab is working correctly. The basic information form above is functional.
                  </p>
                </CardContent>
              </Card>
            </SafeTabContent>
          </TabsContent>

          <TabsContent value="matching">
            <SafeTabContent tabName="Matching Rules">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Deal Matching Rules (Safe Mode)
                  </CardTitle>
                  <CardDescription>
                    This is a safe version of the matching rules tab
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertDescription>
                      Matching rules functionality is disabled in safe mode to prevent crashes.
                      The original component had issues with the ConditionBuilder.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </SafeTabContent>
          </TabsContent>

          <TabsContent value="scoring">
            <SafeTabContent tabName="Scoring Rules">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Scoring Configuration (Safe Mode)
                  </CardTitle>
                  <CardDescription>
                    This is a safe version of the scoring configuration tab
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertDescription>
                      Scoring configuration is disabled in safe mode to prevent crashes.
                      The original component had issues with state management.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </SafeTabContent>
          </TabsContent>

          <TabsContent value="bonus">
            <SafeTabContent tabName="Bonus Rules">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Bonus Scoring Rules (Safe Mode)
                  </CardTitle>
                  <CardDescription>
                    This is a safe version of the bonus rules tab
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertDescription>
                      Bonus rules functionality is disabled in safe mode to prevent crashes.
                      The original component had issues with the ConditionBuilder recursion.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </SafeTabContent>
          </TabsContent>
        </Tabs>

        {/* Status */}
        <Alert>
          <AlertDescription>
            Safe mode active. Current tab: {activeTab} | Name: {name || 'Not set'} | Description: {description ? 'Set' : 'Not set'}
          </AlertDescription>
        </Alert>
      </div>
    </ThesisBuilderErrorBoundary>
  );
}
