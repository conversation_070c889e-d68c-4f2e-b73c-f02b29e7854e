"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Play } from 'lucide-react';
import { 
  ScoringRule, 
  RuleType, 
  ConditionOperator, 
  FilterCondition,
  QuestionScoringConfig 
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { toast } from '@/components/ui/use-toast';

interface ScoringConfigValidationProps {
  questionConfigs: QuestionScoringConfig[];
  existingScoringRules: ScoringRule[];
  onCreateScoringRule?: (questionId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onUpdateScoringRule?: (ruleId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onDeleteScoringRule?: (ruleId: string) => Promise<void>;
}

interface ValidationResult {
  type: 'success' | 'warning' | 'error';
  message: string;
  details?: string;
}

export function ScoringConfigValidation({
  questionConfigs,
  existingScoringRules,
  onCreateScoringRule,
  onUpdateScoringRule,
  onDeleteScoringRule
}: ScoringConfigValidationProps) {
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Validation functions
  const validateQuestionConfigs = (): ValidationResult[] => {
    const results: ValidationResult[] = [];

    // Check for included questions without rules
    const includedQuestions = questionConfigs.filter(q => q.included);
    const questionsWithRules = existingScoringRules.map(r => r.question_id);
    
    const includedWithoutRules = includedQuestions.filter(q => 
      !questionsWithRules.includes(q.question_id)
    );

    if (includedWithoutRules.length > 0) {
      results.push({
        type: 'warning',
        message: `${includedWithoutRules.length} included questions have no scoring rules`,
        details: includedWithoutRules.map(q => q.question_label).join(', ')
      });
    }

    // Check for rules without included questions
    const rulesWithoutInclusion = existingScoringRules.filter(rule => {
      const config = questionConfigs.find(q => q.question_id === rule.question_id);
      return config && !config.included;
    });

    if (rulesWithoutInclusion.length > 0) {
      results.push({
        type: 'warning',
        message: `${rulesWithoutInclusion.length} scoring rules exist for excluded questions`,
        details: 'These rules will not be used in scoring'
      });
    }

    // Check weight consistency
    const weightMismatches = questionConfigs.filter(config => {
      const rule = existingScoringRules.find(r => r.question_id === config.question_id);
      return rule && config.weight !== rule.weight;
    });

    if (weightMismatches.length > 0) {
      results.push({
        type: 'error',
        message: `${weightMismatches.length} questions have weight mismatches between config and rule`,
        details: 'Weights in the table should match the rule weights'
      });
    }

    return results;
  };

  const validateScoringRules = (): ValidationResult[] => {
    const results: ValidationResult[] = [];

    // Check for rules with missing required fields
    const invalidRules = existingScoringRules.filter(rule => {
      if (rule.rule_type === RuleType.SCORING) {
        return !rule.question_id || !rule.weight || rule.weight <= 0 || !rule.condition;
      }
      if (rule.rule_type === RuleType.BONUS) {
        return !rule.bonus_points || rule.bonus_points <= 0 || !rule.condition;
      }
      return false;
    });

    if (invalidRules.length > 0) {
      results.push({
        type: 'error',
        message: `${invalidRules.length} scoring rules have missing required fields`,
        details: 'All rules must have valid conditions and appropriate points/weights'
      });
    }

    // Check for duplicate question rules
    const questionIds = existingScoringRules
      .filter(rule => rule.rule_type === RuleType.SCORING && rule.question_id)
      .map(rule => rule.question_id);
    
    const duplicates = questionIds.filter((id, index) => questionIds.indexOf(id) !== index);
    
    if (duplicates.length > 0) {
      results.push({
        type: 'error',
        message: `${duplicates.length} questions have duplicate scoring rules`,
        details: 'Each question should have only one scoring rule'
      });
    }

    // Check condition validity
    const rulesWithInvalidConditions = existingScoringRules.filter(rule => {
      if (!rule.condition) return true;
      
      if ('operator' in rule.condition) {
        const condition = rule.condition as FilterCondition;
        return !condition.question_id || !condition.operator || condition.value === undefined;
      }
      
      return false;
    });

    if (rulesWithInvalidConditions.length > 0) {
      results.push({
        type: 'error',
        message: `${rulesWithInvalidConditions.length} rules have invalid conditions`,
        details: 'All conditions must have question_id, operator, and value'
      });
    }

    return results;
  };

  const validateAPIFunctions = (): ValidationResult[] => {
    const results: ValidationResult[] = [];

    if (!onCreateScoringRule) {
      results.push({
        type: 'error',
        message: 'Create scoring rule function not provided',
        details: 'Cannot create new scoring rules'
      });
    }

    if (!onUpdateScoringRule) {
      results.push({
        type: 'error',
        message: 'Update scoring rule function not provided',
        details: 'Cannot update existing scoring rules'
      });
    }

    if (!onDeleteScoringRule) {
      results.push({
        type: 'error',
        message: 'Delete scoring rule function not provided',
        details: 'Cannot delete scoring rules'
      });
    }

    return results;
  };

  const runValidation = () => {
    const results = [
      ...validateQuestionConfigs(),
      ...validateScoringRules(),
      ...validateAPIFunctions()
    ];

    setValidationResults(results);

    if (results.length === 0) {
      setValidationResults([{
        type: 'success',
        message: 'All validations passed',
        details: 'Scoring configuration is properly set up'
      }]);
    }
  };

  const testAPIFunctions = async () => {
    if (!onCreateScoringRule || !onUpdateScoringRule || !onDeleteScoringRule) {
      toast({
        title: 'Cannot run API tests',
        description: 'API functions are not properly wired',
        variant: 'destructive'
      });
      return;
    }

    setIsRunningTests(true);
    const testResults: ValidationResult[] = [];

    try {
      // Find a question without a rule for testing
      const testQuestion = questionConfigs.find(q => 
        !existingScoringRules.some(r => r.question_id === q.question_id)
      );

      if (!testQuestion) {
        testResults.push({
          type: 'warning',
          message: 'No available questions for API testing',
          details: 'All questions already have rules'
        });
      } else {
        console.log('🧪 Starting API test with question:', testQuestion);

        // Test create rule
        const testRule: Partial<ScoringRule> = {
          rule_type: RuleType.SCORING,
          question_id: testQuestion.question_id,
          weight: 1.0,
          condition: {
            question_id: testQuestion.question_id,
            operator: ConditionOperator.EQUALS,
            value: 'test_value'
          } as FilterCondition
        };

        console.log('🧪 Creating test rule:', testRule);

        try {
          await onCreateScoringRule(testQuestion.question_id, testRule);
          testResults.push({
            type: 'success',
            message: 'Create scoring rule API test passed',
            details: `Successfully created test rule for ${testQuestion.question_label}`
          });

          // Wait a moment for the rule to be created and state to update
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Find the created rule in the existing rules
          const createdRule = existingScoringRules.find(r => 
            r.question_id === testQuestion.question_id
          );

          console.log('🧪 Found created rule:', createdRule);

          if (createdRule && (createdRule.id || createdRule._id)) {
            const ruleId = createdRule.id || createdRule._id as string;
            
            // Test update rule
            console.log('🧪 Updating test rule:', ruleId);
            try {
              await onUpdateScoringRule(ruleId, { weight: 2.0 });
              testResults.push({
                type: 'success',
                message: 'Update scoring rule API test passed',
                details: 'Successfully updated test rule weight'
              });
            } catch (error) {
              console.error('🧪 Update test failed:', error);
              testResults.push({
                type: 'error',
                message: 'Update scoring rule API test failed',
                details: error instanceof Error ? error.message : 'Unknown error'
              });
            }

            // Test delete rule
            console.log('🧪 Deleting test rule:', ruleId);
            try {
              await onDeleteScoringRule(ruleId);
              testResults.push({
                type: 'success',
                message: 'Delete scoring rule API test passed',
                details: 'Successfully deleted test rule'
              });
            } catch (error) {
              console.error('🧪 Delete test failed:', error);
              testResults.push({
                type: 'error',
                message: 'Delete scoring rule API test failed',
                details: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          } else {
            testResults.push({
              type: 'warning',
              message: 'Could not find created rule for further testing',
              details: 'Rule may have been created but not visible in current state'
            });
          }
        } catch (error) {
          console.error('🧪 Create test failed:', error);
          testResults.push({
            type: 'error',
            message: 'Create scoring rule API test failed',
            details: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } catch (error) {
      console.error('🧪 API test suite failed:', error);
      testResults.push({
        type: 'error',
        message: 'API test suite failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsRunningTests(false);
      setValidationResults(prev => [...prev, ...testResults]);
      
      toast({
        title: 'API Tests Complete',
        description: `Completed ${testResults.length} tests. Check results below.`,
      });
    }
  };

  const getResultIcon = (type: ValidationResult['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getResultVariant = (type: ValidationResult['type']) => {
    switch (type) {
      case 'success':
        return 'default';
      case 'warning':
        return 'default';
      case 'error':
        return 'destructive';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Scoring Configuration Validation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button onClick={runValidation} variant="outline">
            Run Validation
          </Button>
          <Button 
            onClick={testAPIFunctions} 
            variant="outline"
            disabled={isRunningTests}
          >
            <Play className="h-4 w-4 mr-2" />
            {isRunningTests ? 'Testing APIs...' : 'Test API Functions'}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium">Question Configs:</span>
            <Badge variant="outline" className="ml-2">
              {questionConfigs.length}
            </Badge>
          </div>
          <div>
            <span className="font-medium">Included Questions:</span>
            <Badge variant="outline" className="ml-2">
              {questionConfigs.filter(q => q.included).length}
            </Badge>
          </div>
          <div>
            <span className="font-medium">Existing Rules:</span>
            <Badge variant="outline" className="ml-2">
              {existingScoringRules.length}
            </Badge>
          </div>
        </div>

        {validationResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Validation Results:</h4>
            {validationResults.map((result, index) => (
              <Alert key={index} variant={getResultVariant(result.type)}>
                <div className="flex items-start gap-2">
                  {getResultIcon(result.type)}
                  <div className="flex-1">
                    <AlertDescription>
                      <div className="font-medium">{result.message}</div>
                      {result.details && (
                        <div className="text-sm text-muted-foreground mt-1">
                          {result.details}
                        </div>
                      )}
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 