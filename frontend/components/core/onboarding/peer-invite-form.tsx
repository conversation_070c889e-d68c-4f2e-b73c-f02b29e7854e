"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Plus, Mail, X, Send } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import OnboardingAPI from "@/lib/api/onboarding-api"

interface PeerInviteFormProps {
  onSuccess?: () => void
}

export function PeerInviteForm({ onSuccess }: PeerInviteFormProps) {
  const [emails, setEmails] = useState<string[]>([''])
  const [message, setMessage] = useState('')
  const [isInviting, setIsInviting] = useState(false)
  const [results, setResults] = useState<any>(null)

  const handleAddEmail = () => {
    setEmails([...emails, ''])
  }

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails]
    newEmails[index] = value
    setEmails(newEmails)
  }

  const handleRemoveEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index)
      setEmails(newEmails)
    }
  }

  const handleSendInvites = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const validEmails = emails.filter(email => email.trim() && email.includes('@'))
    
    if (validEmails.length === 0) {
      toast.error("Please enter at least one valid email address")
      return
    }

    try {
      setIsInviting(true)
      
      const response = await OnboardingAPI.sendPeerInvites({
        emails: validEmails,
        message: message.trim() || undefined,
      })

      setResults(response)
      
      if (response.total_sent > 0) {
        toast.success(`Invitations sent to ${response.total_sent} people!`)
        
        // Reset form
        setEmails([''])
        setMessage('')
        
        if (onSuccess) {
          onSuccess()
        }
      }
      
      if (response.total_failed > 0) {
        toast.warning(`${response.total_failed} invitations failed to send`)
      }
    } catch (error: any) {
      console.error("Send invites error:", error)
      toast.error(error.message || "Failed to send invitations")
    } finally {
      setIsInviting(false)
    }
  }

  const handleReset = () => {
    setResults(null)
    setEmails([''])
    setMessage('')
  }

  if (results) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <Mail className="h-5 w-5" />
            Invitations Sent!
          </CardTitle>
          <CardDescription>
            Your team invitations have been processed.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {results.total_sent}
              </div>
              <div className="text-sm text-muted-foreground">Sent Successfully</div>
            </div>
            <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {results.total_failed}
              </div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
          </div>

          {/* Results Details */}
          {results.results && results.results.length > 0 && (
            <div className="space-y-2">
              <Label>Invitation Results</Label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {results.results.map((result: any, index: number) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                  >
                    <span className="text-sm">{result.email}</span>
                    <Badge
                      variant={result.status === 'sent' ? 'default' : 'destructive'}
                    >
                      {result.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <Button onClick={handleReset} variant="outline" className="flex-1">
              Send More Invites
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Invite Team Members
        </CardTitle>
        <CardDescription>
          Send invitations to colleagues to join your organization.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSendInvites} className="space-y-6">
          {/* Email Inputs */}
          <div className="space-y-3">
            <Label>Email Addresses</Label>
            {emails.map((email, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => handleEmailChange(index, e.target.value)}
                  className="h-10 rounded-lg"
                />
                {emails.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveEmail(index)}
                    className="h-10 px-3"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddEmail}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Another Email
            </Button>
          </div>

          {/* Optional Message */}
          <div className="space-y-2">
            <Label>Personal Message (Optional)</Label>
            <Textarea
              placeholder="Join our team on TractionX! We're excited to have you on board."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="rounded-lg"
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isInviting}
            className="w-full h-12 rounded-xl font-semibold"
          >
            {isInviting ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"
              />
            ) : (
              <Send className="h-4 w-4 mr-2" />
            )}
            {isInviting ? "Sending Invitations..." : "Send Invitations"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
