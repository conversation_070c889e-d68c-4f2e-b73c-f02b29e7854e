"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, CheckCircle, Star, Building, User, Rocket } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { AuthCard } from "@/components/auth/auth-card"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { OrganizationSetupForm } from "./organization-setup-form"
import { ProfileSetupForm } from "./profile-setup-form"
import { WelcomeScreen } from "./welcome-screen"
import { ValidateInviteCodeResponse } from "@/lib/api/onboarding-api"

interface OnboardingWizardProps {
  inviteCode: string
  inviteData: ValidateInviteCodeResponse
  onComplete: (result: any) => void
}

const STEPS = [
  {
    id: 1,
    title: "Organization Setup",
    description: "Configure your investment firm's workspace",
    icon: Building,
    color: "from-blue-500 to-blue-600"
  },
  {
    id: 2,
    title: "Profile Creation",
    description: "Set up your investor profile and preferences",
    icon: User,
    color: "from-purple-500 to-purple-600"
  },
  {
    id: 3,
    title: "Ready to Launch",
    description: "Your premium TractionX experience awaits",
    icon: Rocket,
    color: "from-emerald-500 to-emerald-600"
  },
]

export function OnboardingWizard({ inviteCode, inviteData, onComplete }: OnboardingWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [stepData, setStepData] = useState<Record<number, any>>({})

  const progress = (currentStep / STEPS.length) * 100

  const handleStepComplete = (stepNumber: number, data: any) => {
    setStepData(prev => ({ ...prev, [stepNumber]: data }))
    
    if (stepNumber < STEPS.length) {
      setCurrentStep(stepNumber + 1)
    } else {
      // Final step completed
      onComplete(data)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const currentStepData = STEPS.find(step => step.id === currentStep)

  return (
    <div className="w-full max-w-4xl mx-auto space-y-8">
      {/* Premium Progress Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Premium Step Indicators */}
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-4">
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isActive = step.id === currentStep
              const isCompleted = step.id < currentStep

              return (
                <div key={step.id} className="flex items-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className={cn(
                      "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300",
                      isCompleted
                        ? "bg-emerald-500 border-emerald-500 text-white"
                        : isActive
                          ? `bg-gradient-to-r ${step.color} border-transparent text-white shadow-lg`
                          : "bg-white border-gray-200 text-gray-400"
                    )}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}

                    {isActive && (
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20"
                      />
                    )}
                  </motion.div>

                  {index < STEPS.length - 1 && (
                    <div className={cn(
                      "w-16 h-0.5 mx-4 transition-colors duration-300",
                      isCompleted ? "bg-emerald-500" : "bg-gray-200"
                    )} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Premium Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium text-gray-600">
              Step {currentStep} of {STEPS.length}
            </span>
            <span className="font-medium text-gray-600">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <Progress
            value={progress}
            className="h-3 bg-gray-100 rounded-full overflow-hidden"
          />
        </div>

        {/* Premium Step Title */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-3"
        >
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">
            {currentStepData?.title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {currentStepData?.description}
          </p>
        </motion.div>
      </motion.div>

      {/* Premium Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          <div className={cn(visualRetreat.card.base, visualRetreat.card.floating, "p-8 md:p-12")}>
            {currentStep === 1 && (
              <OrganizationSetupForm
                inviteCode={inviteCode}
                inviteData={inviteData}
                onComplete={(data) => handleStepComplete(1, data)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}

            {currentStep === 2 && (
              <ProfileSetupForm
                inviteCode={inviteCode}
                inviteData={inviteData}
                organizationData={stepData[1]}
                onComplete={(data) => handleStepComplete(2, data)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}

            {currentStep === 3 && (
              <WelcomeScreen
                userData={stepData[2]}
                organizationData={stepData[1]}
                onComplete={(data) => handleStepComplete(3, data)}
              />
            )}
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Premium Navigation */}
      {currentStep < 3 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center"
        >
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentStep === 1 || isLoading}
            className={cn(
              "flex items-center gap-2 transition-all duration-200",
              "bg-white/80 border-gray-200/60 hover:bg-gray-50/80 backdrop-blur-sm touch-target",
              currentStep === 1 || isLoading ? "opacity-50 cursor-not-allowed" : ""
            )}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Previous Step</span>
            <span className="sm:hidden">Back</span>
          </Button>
        </motion.div>
      )}
    </div>
  )
}
