"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Star, Users, ArrowRight, Mail, Plus } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import OnboardingAPI from "@/lib/api/onboarding-api"

interface WelcomeScreenProps {
  userData: any
  organizationData: any
  onComplete: (data: any) => void
}

export function WelcomeScreen({ userData, organizationData, onComplete }: WelcomeScreenProps) {
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [emails, setEmails] = useState<string[]>([''])
  const [message, setMessage] = useState('')
  const [isInviting, setIsInviting] = useState(false)

  const handleAddEmail = () => {
    setEmails([...emails, ''])
  }

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails]
    newEmails[index] = value
    setEmails(newEmails)
  }

  const handleRemoveEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index)
      setEmails(newEmails)
    }
  }

  const handleSendInvites = async () => {
    try {
      setIsInviting(true)
      
      const validEmails = emails.filter(email => email.trim() && email.includes('@'))
      
      if (validEmails.length === 0) {
        toast.error("Please enter at least one valid email address")
        return
      }

      const response = await OnboardingAPI.sendPeerInvites({
        emails: validEmails,
        message: message.trim() || undefined,
      })

      toast.success(`Invitations sent to ${response.total_sent} people!`)
      
      // Continue to dashboard
      onComplete(userData)
    } catch (error: any) {
      console.error("Send invites error:", error)
      toast.error(error.message || "Failed to send invitations")
    } finally {
      setIsInviting(false)
    }
  }

  const handleSkipInvites = () => {
    onComplete(userData)
  }

  return (
    <div className="text-center space-y-8">
      {/* Success Animation */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", duration: 0.6 }}
        className="relative"
      >
        <div className="h-24 w-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto flex items-center justify-center">
          <Star className="h-12 w-12 text-white" />
        </div>
        
        {/* Confetti Effect */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="absolute inset-0 pointer-events-none"
        >
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, rotate: 0 }}
              animate={{ 
                scale: [0, 1, 0],
                rotate: [0, 180, 360],
                x: [0, (i % 2 ? 1 : -1) * (50 + i * 10)],
                y: [0, -30 - i * 5, 20]
              }}
              transition={{ 
                duration: 2,
                delay: 0.5 + i * 0.1,
                ease: "easeOut"
              }}
              className="absolute top-1/2 left-1/2 h-2 w-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"
            />
          ))}
        </motion.div>
      </motion.div>

      {/* Welcome Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <h2 className="text-3xl font-bold text-foreground">
          🎉 Welcome to TractionX!
        </h2>
        <p className="text-lg text-muted-foreground max-w-md mx-auto">
          Your organization has been set up successfully. You're ready to start building the future of investing.
        </p>
      </motion.div>

      {/* Invite Team Section */}
      {!showInviteForm ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          <Separator />
          
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Users className="h-5 w-5" />
              <span className="font-medium">Invite Your Team</span>
            </div>
            <p className="text-sm text-muted-foreground max-w-sm mx-auto">
              Get your colleagues on board and start collaborating immediately.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => setShowInviteForm(true)}
              className="flex items-center gap-2 h-12 px-6 rounded-xl font-semibold"
            >
              <Mail className="h-4 w-4" />
              Invite Team Members
            </Button>
            <Button
              variant="outline"
              onClick={handleSkipInvites}
              className="flex items-center gap-2 h-12 px-6 rounded-xl"
            >
              Skip for Now
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 text-left"
        >
          <Separator />
          
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">Invite Your Team</h3>
            
            {/* Email Inputs */}
            <div className="space-y-3">
              <Label>Email Addresses</Label>
              {emails.map((email, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => handleEmailChange(index, e.target.value)}
                    className="h-10 rounded-lg"
                  />
                  {emails.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveEmail(index)}
                      className="h-10 px-3"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddEmail}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Another Email
              </Button>
            </div>

            {/* Optional Message */}
            <div className="space-y-2">
              <Label>Personal Message (Optional)</Label>
              <Textarea
                placeholder="Join our team on TractionX! We're excited to have you on board."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="rounded-lg"
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={handleSendInvites}
                disabled={isInviting}
                className="flex-1 h-12 rounded-xl font-semibold"
              >
                {isInviting ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"
                  />
                ) : (
                  <Mail className="h-4 w-4 mr-2" />
                )}
                {isInviting ? "Sending..." : "Send Invites"}
              </Button>
              <Button
                variant="outline"
                onClick={handleSkipInvites}
                className="h-12 px-6 rounded-xl"
              >
                Skip
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
