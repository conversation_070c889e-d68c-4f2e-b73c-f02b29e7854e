"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { motion } from "framer-motion"
import { User, Building2, Users, Settings } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"

interface SettingsLayoutProps {
  children?: React.ReactNode
  activeTab?: string
  className?: string
}

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  adminOnly?: boolean
}

const tabs: TabConfig[] = [
  {
    id: "profile",
    label: "Profile",
    icon: User,
    description: "Manage your personal account settings and preferences"
  },
  {
    id: "organization", 
    label: "Organization",
    icon: Building2,
    description: "Configure your organization's branding and settings",
    adminOnly: true
  },
  {
    id: "members",
    label: "Members", 
    icon: Users,
    description: "Manage team members and user permissions",
    adminOnly: true
  }
]

export function SettingsLayout({
  children,
  activeTab: propActiveTab,
  className
}: SettingsLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useAuth()

  // Determine active tab from URL
  const getActiveTabFromPath = () => {
    if (pathname?.includes('/settings/profile')) return 'profile'
    if (pathname?.includes('/settings/organization')) return 'organization'
    if (pathname?.includes('/settings/members')) return 'members'
    return 'profile'
  }

  const [activeTab, setActiveTab] = useState(propActiveTab || getActiveTabFromPath())

  useEffect(() => {
    const tabFromPath = getActiveTabFromPath()
    setActiveTab(tabFromPath)
  }, [pathname])

  // Check if user is admin (you can adjust this logic based on your auth system)
  const isAdmin = true // TODO: Replace with actual role check based on user data

  const availableTabs = tabs.filter(tab =>
    !tab.adminOnly || isAdmin
  )

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    router.push(`/settings/${tabId}`)
  }

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      <div className="relative">
        {/* Header */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-muted rounded-lg flex items-center justify-center">
                <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-muted-foreground" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">
                  Settings
                </h1>
                <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                  Manage your account, organization, and team preferences
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="container max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6 sm:space-y-8">
            {/* Tab Navigation */}
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 bg-muted p-1 rounded-lg">
              {availableTabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center justify-center space-x-2 px-3 py-2 rounded-md transition-all duration-200 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium text-sm">{tab.label}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {/* Tab Content */}
            <div className="space-y-6 sm:space-y-8">
              {availableTabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-0">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
                          <tab.icon className="w-4 h-4 text-muted-foreground" />
                        </div>
                        <div>
                          <h2 className="text-xl sm:text-2xl font-semibold text-foreground">
                            {tab.label}
                          </h2>
                          <p className="text-sm text-muted-foreground">
                            {tab.description}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-6">
                      {children}
                    </div>
                  </motion.div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Export individual tab content components for use in pages
export function ProfileTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrganizationTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function MembersTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}
