"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import { UserPlus, Mail, Crown, User, Loader2, Send } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"

const inviteSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["admin", "member"], {
    required_error: "Please select a role",
  }),
})

type InviteFormValues = z.infer<typeof inviteSchema>

interface InviteUserFormProps {
  onInvite: (data: { emails: string[], role_id?: string, message?: string }) => Promise<any>
  className?: string
}

export function InviteUserForm({ onInvite, className }: InviteUserFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<InviteFormValues>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: "",
      role: "member",
    },
  })

  const handleSubmit = async (data: InviteFormValues) => {
    setIsLoading(true)
    try {
      await onInvite({ 
        emails: [data.email],
        role_id: data.role
      })
      
      toast({
        title: "Invitation sent!",
        description: `An invitation has been sent to ${data.email}.`,
      })
      
      form.reset()
    } catch (error) {
      console.error('Invite error:', error)
      toast({
        title: "Invitation failed",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className={`border bg-card ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
            <UserPlus className="w-4 h-4 text-muted-foreground" />
          </div>
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">
              Invite Team Member
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Send an invitation to join your organization on TractionX
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Email Field */}
            <div className="md:col-span-2 space-y-2">
              <Label htmlFor="email" className="text-sm font-medium flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="h-11"
                {...form.register("email")}
              />
              {form.formState.errors.email && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {form.formState.errors.email.message}
                </motion.p>
              )}
            </div>

            {/* Role Field */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                Role
              </Label>
              <Select
                value={form.watch("role")}
                onValueChange={(value: "admin" | "member") => form.setValue("role", value)}
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="member" className="cursor-pointer">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      Member
                    </div>
                  </SelectItem>
                  <SelectItem value="admin" className="cursor-pointer">
                    <div className="flex items-center">
                      <Crown className="w-4 h-4 mr-2" />
                      Admin
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.role && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {form.formState.errors.role.message}
                </motion.p>
              )}
            </div>
          </div>

          {/* Role Descriptions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-muted rounded-lg border">
              <div className="flex items-center space-x-2 mb-2">
                <User className="w-4 h-4 text-muted-foreground" />
                <h4 className="font-medium text-foreground">Member</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Can view and manage deals, forms, and submissions. Cannot manage organization settings or invite users.
              </p>
            </div>

            <div className="p-4 bg-muted rounded-lg border">
              <div className="flex items-center space-x-2 mb-2">
                <Crown className="w-4 h-4 text-muted-foreground" />
                <h4 className="font-medium text-foreground">Admin</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Full access to all features including organization settings, user management, and billing.
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isLoading}
              className="font-medium"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Help Text */}
        <div className="mt-6 p-4 bg-muted rounded-lg border">
          <h4 className="font-medium text-foreground mb-2">
            What happens next?
          </h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• The invitee will receive a branded email with an invitation link</li>
            <li>• They can accept the invitation and set up their account</li>
            <li>• Once accepted, they'll appear in your members list</li>
            <li>• You can manage their role and permissions anytime</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
