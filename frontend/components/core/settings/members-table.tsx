"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { MoreHorizontal, Mail, Trash2, UserCheck, UserX, Crown, User, Clock, Users } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

export interface Member {
  id: string
  name: string
  email: string
  profile_picture?: string
  status: string
  role_name?: string
  last_login?: number
  created_at: number
}

interface MembersTableProps {
  members: Member[]
  currentUserId: string
  isAdmin?: boolean
  onResendInvite: (email: string) => Promise<void>
  onRemoveMember: (memberId: string) => Promise<void>
  onUpdateRole?: (memberId: string, role: string) => Promise<void>
  className?: string
}

export function MembersTable({
  members,
  currentUserId,
  isAdmin = true,
  onResendInvite,
  onRemoveMember,
  onUpdateRole,
  className
}: MembersTableProps) {
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({})

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "ACTIVE":
        return (
          <Badge variant="secondary" className="bg-muted text-muted-foreground border-0">
            <UserCheck className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case "INVITED":
      case "PENDING":
        return (
          <Badge variant="outline" className="text-muted-foreground border-border">
            <Clock className="w-3 h-3 mr-1" />
            Invited
          </Badge>
        )
      case "SUSPENDED":
      case "INACTIVE":
        return (
          <Badge variant="outline" className="text-muted-foreground border-border opacity-60">
            <UserX className="w-3 h-3 mr-1" />
            Suspended
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="text-muted-foreground border-border">
            <User className="w-3 h-3 mr-1" />
            {status}
          </Badge>
        )
    }
  }

  const getRoleBadge = (role_name?: string) => {
    if (role_name?.toLowerCase().includes("admin") || role_name?.toLowerCase() === "gp") {
      return (
        <Badge variant="secondary" className="bg-muted text-foreground border-0">
          <Crown className="w-3 h-3 mr-1" />
          Admin
        </Badge>
      )
    }
    return (
      <Badge variant="outline" className="text-muted-foreground border-border">
        <User className="w-3 h-3 mr-1" />
        {role_name || "Member"}
      </Badge>
    )
  }

  const handleAction = async (action: () => Promise<void>, memberId: string, actionName: string) => {
    setLoadingActions(prev => ({ ...prev, [memberId]: true }))
    try {
      await action()
      toast({
        title: "Success",
        description: `${actionName} completed successfully.`,
      })
    } catch (error) {
      console.error(`${actionName} error:`, error)
      toast({
        title: "Error",
        description: `Failed to ${actionName.toLowerCase()}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setLoadingActions(prev => ({ ...prev, [memberId]: false }))
    }
  }

  const formatLastLogin = (timestamp?: number) => {
    if (!timestamp) return "Never"
    return formatDistanceToNow(new Date(timestamp * 1000), { addSuffix: true })
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="rounded-lg border bg-card overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent border-border">
              <TableHead className="font-medium text-foreground">Member</TableHead>
              <TableHead className="font-medium text-foreground">Status</TableHead>
              <TableHead className="font-medium text-foreground">Role</TableHead>
              <TableHead className="font-medium text-foreground hidden sm:table-cell">Last Login</TableHead>
              {isAdmin && (
                <TableHead className="font-medium text-foreground text-right">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member, index) => (
              <motion.tr
                key={member.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="border-border hover:bg-muted/50 transition-colors"
              >
                <TableCell className="py-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8 sm:w-10 sm:h-10">
                      <AvatarImage src={member.profile_picture} alt={member.name} />
                      <AvatarFallback className="bg-muted text-muted-foreground font-medium text-sm">
                        {getInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-foreground truncate">
                          {member.name}
                        </p>
                        {member.id === currentUserId && (
                          <Badge variant="outline" className="text-xs shrink-0">You</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground truncate">{member.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="py-4">
                  {getStatusBadge(member.status)}
                </TableCell>
                <TableCell className="py-4">
                  {getRoleBadge(member.role_name)}
                </TableCell>
                <TableCell className="py-4 hidden sm:table-cell">
                  <span className="text-sm text-muted-foreground">
                    {member.status.toUpperCase() === "INVITED" || member.status.toUpperCase() === "PENDING"
                      ? `Invited ${formatLastLogin(member.created_at)}`
                      : formatLastLogin(member.last_login)
                    }
                  </span>
                </TableCell>
                {isAdmin && (
                  <TableCell className="py-4 text-right">
                    {member.id !== currentUserId && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            disabled={loadingActions[member.id]}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                          {(member.status.toUpperCase() === "INVITED" || member.status.toUpperCase() === "PENDING") && (
                            <DropdownMenuItem
                              onClick={() => handleAction(
                                () => onResendInvite(member.email),
                                member.id,
                                "Resend invite"
                              )}
                              className="cursor-pointer"
                            >
                              <Mail className="w-4 h-4 mr-2" />
                              Resend Invite
                            </DropdownMenuItem>
                          )}
                          
                          {onUpdateRole && member.status.toUpperCase() === "ACTIVE" && (
                            <>
                              <DropdownMenuItem
                                onClick={() => handleAction(
                                  () => onUpdateRole(member.id, member.role_name?.toLowerCase().includes("admin") ? "member" : "admin"),
                                  member.id,
                                  member.role_name?.toLowerCase().includes("admin") ? "Remove admin" : "Make admin"
                                )}
                                className="cursor-pointer"
                              >
                                {member.role_name?.toLowerCase().includes("admin") ? (
                                  <>
                                    <User className="w-4 h-4 mr-2" />
                                    Remove Admin
                                  </>
                                ) : (
                                  <>
                                    <Crown className="w-4 h-4 mr-2" />
                                    Make Admin
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                            </>
                          )}
                          
                          <DropdownMenuItem
                            onClick={() => handleAction(
                              () => onRemoveMember(member.id),
                              member.id,
                              "Remove member"
                            )}
                            className="cursor-pointer text-destructive focus:text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                )}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {members.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No members yet
          </h3>
          <p className="text-muted-foreground">
            Invite team members to start collaborating on TractionX.
          </p>
        </div>
      )}
    </div>
  )
}
