"use client"
import Image from "next/image"
import Link from "next/link"
import { motion } from 'framer-motion'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils";

interface PlaceholderScreenProps {
  title: string
  description: string
  imagePath: string
  buttonText?: string
  buttonLink?: string
}

import dynamic from "next/dynamic"
const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), { ssr: false })

// Keeping ParticleOrbitField for future use
// const ParticleOrbitField = dynamic(() => import("@/components/ui/particle-fields"), {
//   ssr: false,
// })

// PremiumWave component available for future use
// const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), {
//   ssr: false,
// })

const ClientOrbitPulseGrid = dynamic(() => import("@/components/core/orbit-ai/orbit-icon").then(mod => ({ default: mod.OrbitPulseGrid })), {
  ssr: false,
})

export default function OrbitHero() {
  return (
    <section className="relative min-h-screen w-full overflow-hidden flex flex-col">
      {/* Gradient background (bulletproof, always fills viewport) */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 via-white to-neutral-100" />
        <div 
          className="absolute inset-0 opacity-[0.015]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(0 0 0) 1px, transparent 0)`,
            backgroundSize: '60px 60px'
          }}
        />
      </div>

      {/* Premium Wave - positioned at top, above gradient */}
      <div className="absolute top-0 left-0 right-0 h-[140px] sm:h-[180px] lg:h-[200px] z-10 pointer-events-none">
        <PremiumWave />
        {/* Fade-out overlay for seamless blend */}
        <div className="absolute bottom-0 left-0 right-0 h-8 sm:h-12 bg-gradient-to-b from-transparent to-[rgba(245,245,245,0.95)] z-20" />
      </div>

      {/* Main Content Container */}
      <div className="relative z-20 flex-1 flex flex-col">
        {/* Header Section - Fixed at top */}
        <header className="flex items-center justify-between px-4 sm:px-6 lg:px-8 pt-6 sm:pt-8 lg:pt-10">
          {/* TractionX Logo */}
          <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <Image
              src="/images/traction.svg"
              alt="TractionX"
              width={150}
              height={150}
              className="w-24 sm:w-28 lg:w-32 h-auto drop-shadow-sm"
            />
          </Link>

          {/* Login Button */}
          <Button 
            asChild
            variant="outline"
            size="sm"
            className="border-neutral-200 hover:border-neutral-300 bg-white/80 backdrop-blur-sm text-sm"
          >
            <Link href="/login">Login</Link>
          </Button>
        </header>

        {/* Main Content - Perfectly Centered and fills screen */}
        <main className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 pb-8 sm:pb-12 lg:pb-16 pt-16 sm:pt-20 lg:pt-24">
          {/* Content Wrapper with max-width for better readability */}
          <div className="w-full max-w-6xl mx-auto text-center">
            {/* Main Headline */}
            <div className="mb-12 sm:mb-16 lg:mb-20">
              <h1
                className="text-5xl sm:text-6xl md:text-7xl lg:text-7.5xl xl:text-[6rem] font-bold leading-tight tracking-tight text-neutral-900"
                style={{ fontFamily: "'Playfair Display', 'Inter', serif" }}
              >
                <span className="block mb-3">Access to Private Markets,</span>
                <span className="bg-gradient-to-r from-neutral-900 via-neutral-700 to-neutral-900 bg-clip-text text-transparent">
                  the only place you need to be!
                </span>
              </h1>
            </div>
            {/* Orbit & Status Section */}
            <div className="flex flex-col lg:flex-row items-center justify-center gap-8 sm:gap-10 lg:gap-16 mb-16 sm:mb-20 lg:mb-24">
              {/* OrbitPulseGrid */}
              <div className="relative order-2 lg:order-1">
                <div className="relative z-10">
                  <ClientOrbitPulseGrid
                    size={100}
                    dotCount={16}
                    duration={1500}
                    className="drop-shadow-lg sm:w-20 sm:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28"
                  />
                </div>
                {/* Subtle glow effect */}
                <div className="absolute inset-0 rounded-full bg-neutral-900/5 blur-2xl sm:blur-3xl z-0" />
              </div>
              {/* Status Text */}
              <div className="text-center md:text-left max-w-md">
                <p className="text-lg tracking-widest text-neutral-700 font-semibold mb-1 font-mono">
                  Orbit is live.
                </p>
                <p className="text-neutral-600 text-lg leading-relaxed font-light">
                  The edge investors need to excel in all private market lifecycles
                </p>
              </div>
            </div>
            {/* CTA Button */}
            <div className="relative">
              <Link href="/login">
                <motion.button
                  className={cn(
                    "group relative rounded-full px-8 py-4 sm:px-10 sm:py-5 lg:px-12 lg:py-6",
                    "bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-900",
                    "border border-amber-400/30 hover:border-amber-400/60",
                    "font-mono text-sm sm:text-base lg:text-lg font-medium tracking-wide",
                    "text-amber-50",
                    "transition-all duration-500 ease-out",
                    "shadow-2xl shadow-neutral-900/25 hover:shadow-3xl hover:shadow-neutral-900/40",
                    "overflow-hidden",
                    "hover:scale-105 active:scale-95",
                    "w-full sm:w-auto max-w-sm sm:max-w-none"
                  )}
                  whileHover={{ 
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  {/* Animated shimmer effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/20 to-transparent"
                    animate={{
                      x: ["-100%", "200%"]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      ease: "easeInOut"
                    }}
                  />

                  {/* Button content */}
                  <span className="relative z-10 flex items-center justify-center space-x-3">
                    <span className="tracking-wider relative text-center">
                      {/* Glow effect for the main text */}
                      <span className="absolute inset-0 text-amber-400/30 blur-sm hidden sm:block">
                        WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="absolute inset-0 text-amber-400/30 blur-sm sm:hidden">
                        AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="relative z-10 hidden sm:block">
                        WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="relative z-10 sm:hidden">
                        AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                    </span>
                    <motion.span 
                      className="text-amber-400 text-lg sm:text-xl"
                      animate={{
                        x: [0, 4, 0]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      →
                    </motion.span>
                  </span>

                  {/* Subtle inner glow */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-400/5 via-transparent to-amber-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                </motion.button>
              </Link>
            </div>
          </div>
        </main>
      </div>
    </section>
  )
}

export function PlaceholderScreen({
  title,
  description,
  imagePath,
  buttonText = "Go to Dashboard",
  buttonLink = "/dashboard",
}: PlaceholderScreenProps) {
  return (
    <div className="flex flex-1 min-h-0 flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-center">
      <Card className="mx-auto max-w-3xl w-full">
        <CardHeader className="space-y-4">
          <CardTitle className="text-2xl sm:text-3xl lg:text-4xl font-bold">{title}</CardTitle>
          <CardDescription className="text-base sm:text-lg lg:text-xl">{description}</CardDescription>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 lg:px-8">
          <div className="relative mx-auto aspect-video w-full max-w-2xl overflow-hidden rounded-lg">
            <Image
              src={imagePath}
              alt="Coming Soon"
              fill
              className="object-cover"
              priority
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-center px-4 sm:px-6 lg:px-8 pb-6">
          <Button asChild size="lg" className="w-full sm:w-auto">
            <Link href={buttonLink}>{buttonText}</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}