"use client"

/**
 * SimpleGlow - Ultra-simple, guaranteed visible premium animation
 * 
 * Features:
 * - Simple animated gradient background
 * - Floating geometric elements
 * - Guaranteed visibility
 * - Zero performance impact
 */
export default function SimpleGlow() {
  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Animated gradient background */}
      <div 
        className="absolute inset-0 bg-gradient-to-br from-amber-100/40 via-orange-50/30 to-amber-50/20 animate-pulse"
        style={{ animationDuration: '4s' }}
      />
      
      {/* Floating dots */}
      <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-amber-400/60 rounded-full animate-bounce shadow-lg" style={{ animationDelay: '0s', animationDuration: '3s' }} />
      <div className="absolute top-1/3 right-1/3 w-3 h-3 bg-orange-400/50 rounded-full animate-bounce shadow-md" style={{ animationDelay: '1s', animationDuration: '4s' }} />
      <div className="absolute bottom-1/3 left-1/2 w-5 h-5 bg-amber-500/40 rounded-full animate-bounce shadow-lg" style={{ animationDelay: '2s', animationDuration: '5s' }} />
      
      {/* Animated border lines */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-amber-400/50 to-transparent animate-pulse" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-orange-400/40 to-transparent animate-pulse" style={{ animationDelay: '2s' }} />
      
      {/* Central glow effect */}
      <div 
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-radial from-amber-200/30 via-orange-100/20 to-transparent rounded-full animate-ping"
        style={{ animationDuration: '6s' }}
      />
    </div>
  )
}
