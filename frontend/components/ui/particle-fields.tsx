// components/ui/particle-fields.tsx
"use client"

import { useEffect, useRef } from "react"

const COUNT = 2500
const { random, cos, sin, sqrt, PI } = Math

/**
 * ParticleOrbitField - Hydration-safe gravitational particle orbit animation
 *
 * Features:
 * - 2500 particles in gravitational spiral orbit
 * - Premium color palette (hsl(15-25, 60%, 10-18%))
 * - Responsive field sizing (85% width, 40% height)
 * - Client-only rendering to prevent hydration mismatches
 * - Infinite loop animation with vanilla JS (anime.js alternative)
 */
export default function ParticleOrbitField() {
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>()

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Clear any existing content from previous renders
    container.innerHTML = ""

    // Get container dimensions
    const fieldWidth = container.offsetWidth || 800
    const fieldHeight = container.offsetHeight || 320

    const centerX = fieldWidth / 2
    const centerY = fieldHeight / 2
    const baseRadius = Math.min(fieldWidth, fieldHeight) * 0.3

    const particles: Array<{
      element: HTMLElement
      angle: number
      radius: number
      speed: number
    }> = []

    // Create particles
    console.log(`Creating ${COUNT} particles in container:`, fieldWidth, 'x', fieldHeight)
    for (let i = 0; i < COUNT; i++) {
      const particle = document.createElement("div")

      // Premium color palette as specified in PRD
      const hue = 15 + random() * 10 // 15-25 range
      const saturation = 60
      const lightness = 20 + random() * 15 // More visible: 20-35% range
      const size = 2 + random() * 2 // More visible: 2px - 4px

      particle.className = "absolute rounded-full pointer-events-none"
      particle.style.width = particle.style.height = `${size}px`
      particle.style.background = `hsl(${hue}, ${saturation}%, ${lightness}%)`
      particle.style.opacity = "0.9"
      particle.style.filter = "blur(0.1px)"
      particle.style.boxShadow = `0 0 2px hsl(${hue}, ${saturation}%, ${lightness + 10}%)`

      // Initial random position in spiral
      const angle = random() * 2 * PI
      const radius = baseRadius * sqrt(random())
      const speed = 0.5 + random() * 1.5 // Varying orbital speeds

      particles.push({
        element: particle,
        angle,
        radius,
        speed
      })

      container.appendChild(particle)
    }

    let startTime = Date.now()

    // Animation loop
    const animate = () => {
      const currentTime = Date.now()
      const elapsed = (currentTime - startTime) / 1000 // Convert to seconds

      particles.forEach((particle, index) => {
        // Create spiral motion with varying speeds based on distance
        const radiusMultiplier = 1 + 0.3 * sin(elapsed * 0.5 + index * 0.1)
        const currentRadius = particle.radius * radiusMultiplier

        // Orbital rotation - closer particles move faster (gravitational effect)
        const orbitSpeed = particle.speed * (1 + (1 - particle.radius / (particle.radius + 100)))
        const currentAngle = particle.angle + elapsed * orbitSpeed

        // Calculate new position
        const x = centerX + currentRadius * cos(currentAngle)
        const y = centerY + currentRadius * sin(currentAngle)

        particle.element.style.left = `${x}px`
        particle.element.style.top = `${y}px`

        // Subtle opacity variation for depth effect
        const opacity = 0.6 + 0.4 * (0.5 + 0.5 * sin(elapsed * 2 + index * 0.2))
        particle.element.style.opacity = String(opacity)
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    // Start animation
    animate()

    // Handle window resize
    const handleResize = () => {
      const newFieldWidth = window.innerWidth * 0.85
      const newFieldHeight = window.innerHeight * 0.4
      container.style.width = `${newFieldWidth}px`
      container.style.height = `${newFieldHeight}px`
    }

    window.addEventListener('resize', handleResize)

    // Cleanup function
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      window.removeEventListener('resize', handleResize)
      container.innerHTML = ""
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="relative w-full h-full overflow-hidden pointer-events-none"
      style={{
        position: "relative",
        width: "100%",
        height: "100%",
      }}
    />
  )
}
