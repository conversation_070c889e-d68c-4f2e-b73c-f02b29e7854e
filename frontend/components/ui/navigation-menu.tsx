import * as React from "react"
import {
  Root as NavigationMenuRoot,
  List as NavigationMenuList,
  Item as NavigationMenuItem,
  Trigger as NavigationMenuTrigger,
  Content as NavigationMenuContent,
  Link as NavigationMenuLink,
  Viewport as NavigationMenuViewport,
  Indicator as NavigationMenuIndicator,
} from "@radix-ui/react-navigation-menu"
import { cva } from "class-variance-authority"
import { ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"

const NavigationMenu = React.forwardRef<
  React.ElementRef<typeof NavigationMenuRoot>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuRoot>
>(({ className, children, ...props }, ref) => (
  <NavigationMenuRoot
    ref={ref}
    className={cn(
      "relative z-10 flex flex-1 items-center justify-center",
      className
    )}
    {...props}
  >
    {children}
    <NavigationMenuViewportComponent />
  </NavigationMenuRoot>
))
NavigationMenu.displayName = "NavigationMenu"

const NavigationMenuListComponent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuList>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuList>
>(({ className, ...props }, ref) => (
  <NavigationMenuList
    ref={ref}
    className={cn(
      "group flex flex-1 list-none items-center justify-center space-x-1",
      className
    )}
    {...props}
  />
))
NavigationMenuListComponent.displayName = "NavigationMenuList"

const navigationMenuTriggerStyle = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none bg-background hover:bg-accent hover:text-accent-foreground data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 h-10 py-2 px-4 group w-max"
)

const NavigationMenuTriggerComponent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuTrigger>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuTrigger>
>(({ className, children, ...props }, ref) => (
  <NavigationMenuTrigger
    ref={ref}
    className={cn(navigationMenuTriggerStyle(), "group", className)}
    {...props}
  >
    {children}{" "}
    <ChevronDown
      className="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180"
      aria-hidden="true"
    />
  </NavigationMenuTrigger>
))
NavigationMenuTriggerComponent.displayName = "NavigationMenuTrigger"

const NavigationMenuContentComponent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuContent>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuContent>
>(({ className, ...props }, ref) => (
  <NavigationMenuContent
    ref={ref}
    className={cn(
      "left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",
      className
    )}
    {...props}
  />
))
NavigationMenuContentComponent.displayName = "NavigationMenuContent"

const NavigationMenuViewportComponent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuViewport>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuViewport>
>(({ className, ...props }, ref) => (
  <div className={cn("absolute left-0 top-full flex justify-center")}>
    <NavigationMenuViewport
      className={cn(
        "origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",
        className
      )}
      ref={ref}
      {...props}
    />
  </div>
))
NavigationMenuViewportComponent.displayName = "NavigationMenuViewport"

const NavigationMenuIndicatorComponent = React.forwardRef<
  React.ElementRef<typeof NavigationMenuIndicator>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuIndicator>
>(({ className, ...props }, ref) => (
  <NavigationMenuIndicator
    ref={ref}
    className={cn(
      "top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",
      className
    )}
    {...props}
  >
    <div className="relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md" />
  </NavigationMenuIndicator>
))
NavigationMenuIndicatorComponent.displayName = "NavigationMenuIndicator"

export {
  navigationMenuTriggerStyle,
  NavigationMenu,
  NavigationMenuListComponent as NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuContentComponent as NavigationMenuContent,
  NavigationMenuTriggerComponent as NavigationMenuTrigger,
  NavigationMenuLink,
  NavigationMenuIndicatorComponent as NavigationMenuIndicator,
  NavigationMenuViewportComponent as NavigationMenuViewport,
}
