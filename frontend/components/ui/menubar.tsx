"use client"

import * as React from "react"
import {
  Root as MenubarRoot,
  Menu as MenubarMenu,
  Group as MenubarGroup,
  Portal as MenubarPortal,
  Sub as MenubarSub,
  RadioGroup as MenubarRadioGroup,
  <PERSON>gger as MenubarTrigger,
  SubTrigger as MenubarSubTrigger,
  SubContent as <PERSON>ubar<PERSON>ubContent,
  Content as <PERSON>ubar<PERSON>ontent,
  Item as MenubarItem,
  CheckboxItem as MenubarCheckboxItem,
  RadioItem as MenubarRadioItem,
  Label as MenubarLabel,
  Separator as MenubarSeparator,
  ItemIndicator as MenubarItemIndicator,
} from "@radix-ui/react-menubar"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const Menubar = React.forwardRef<
  React.ElementRef<typeof MenubarRoot>,
  React.ComponentPropsWithoutRef<typeof MenubarRoot>
>(({ className, ...props }, ref) => (
  <MenubarRoot
    ref={ref}
    className={cn(
      "flex h-10 items-center space-x-1 rounded-md border bg-background p-1",
      className
    )}
    {...props}
  />
))
Menubar.displayName = "Menubar"

const MenubarTriggerComponent = React.forwardRef<
  React.ElementRef<typeof MenubarTrigger>,
  React.ComponentPropsWithoutRef<typeof MenubarTrigger>
>(({ className, ...props }, ref) => (
  <MenubarTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",
      className
    )}
    {...props}
  />
))
MenubarTriggerComponent.displayName = "MenubarTrigger"

const MenubarSubTriggerComponent = React.forwardRef<
  React.ElementRef<typeof MenubarSubTrigger>,
  React.ComponentPropsWithoutRef<typeof MenubarSubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <MenubarSubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",
      inset && "pl-8",
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className="ml-auto h-4 w-4" />
  </MenubarSubTrigger>
))
MenubarSubTriggerComponent.displayName = "MenubarSubTrigger"

const MenubarSubContentComponent = React.forwardRef<
  React.ElementRef<typeof MenubarSubContent>,
  React.ComponentPropsWithoutRef<typeof MenubarSubContent>
>(({ className, ...props }, ref) => (
  <MenubarSubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",
      className
    )}
    {...props}
  />
))
MenubarSubContentComponent.displayName = "MenubarSubContent"

const MenubarContentComponent = React.forwardRef<
  React.ElementRef<typeof MenubarContent>,
  React.ComponentPropsWithoutRef<typeof MenubarContent>
>(
  (
    { className, align = "start", alignOffset = -4, sideOffset = 8, ...props },
    ref
  ) => (
    <MenubarPortal>
      <MenubarContent
        ref={ref}
        align={align}
        alignOffset={alignOffset}
        sideOffset={sideOffset}
        className={cn(
          "z-50 min-w-[12rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in slide-in-from-top-1",
          className
        )}
        {...props}
      />
    </MenubarPortal>
  )
)
MenubarContentComponent.displayName = "MenubarContent"

const MenubarItemComponent = React.forwardRef<
  React.ElementRef<typeof MenubarItem>,
  React.ComponentPropsWithoutRef<typeof MenubarItem> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <MenubarItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
MenubarItemComponent.displayName = "MenubarItem"

const MenubarCheckboxItemComponent = React.forwardRef<
  React.ElementRef<typeof MenubarCheckboxItem>,
  React.ComponentPropsWithoutRef<typeof MenubarCheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <MenubarCheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <MenubarItemIndicator>
        <Check className="h-4 w-4" />
      </MenubarItemIndicator>
    </span>
    {children}
  </MenubarCheckboxItem>
))
MenubarCheckboxItemComponent.displayName = "MenubarCheckboxItem"

const MenubarRadioItemComponent = React.forwardRef<
  React.ElementRef<typeof MenubarRadioItem>,
  React.ComponentPropsWithoutRef<typeof MenubarRadioItem>
>(({ className, children, ...props }, ref) => (
  <MenubarRadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <MenubarItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </MenubarItemIndicator>
    </span>
    {children}
  </MenubarRadioItem>
))
MenubarRadioItemComponent.displayName = "MenubarRadioItem"

const MenubarLabelComponent = React.forwardRef<
  React.ElementRef<typeof MenubarLabel>,
  React.ComponentPropsWithoutRef<typeof MenubarLabel> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <MenubarLabel
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
MenubarLabelComponent.displayName = "MenubarLabel"

const MenubarSeparatorComponent = React.forwardRef<
  React.ElementRef<typeof MenubarSeparator>,
  React.ComponentPropsWithoutRef<typeof MenubarSeparator>
>(({ className, ...props }, ref) => (
  <MenubarSeparator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
MenubarSeparatorComponent.displayName = "MenubarSeparator"

const MenubarShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest opacity-60", className)}
      {...props}
    />
  )
}
MenubarShortcut.displayName = "MenubarShortcut"

export {
  Menubar,
  MenubarMenu,
  MenubarTriggerComponent as MenubarTrigger,
  MenubarContentComponent as MenubarContent,
  MenubarItemComponent as MenubarItem,
  MenubarSeparatorComponent as MenubarSeparator,
  MenubarLabelComponent as MenubarLabel,
  MenubarCheckboxItemComponent as MenubarCheckboxItem,
  MenubarRadioItemComponent as MenubarRadioItem,
  MenubarPortal,
  MenubarSubContentComponent as MenubarSubContent,
  MenubarSubTriggerComponent as MenubarSubTrigger,
  MenubarGroup,
  MenubarSub,
  MenubarRadioGroup,
  MenubarShortcut,
}
