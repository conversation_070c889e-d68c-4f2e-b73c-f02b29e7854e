"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface ShimmerSkeletonProps {
  className?: string
  children?: React.ReactNode
}

export function ShimmerSkeleton({ className, children }: ShimmerSkeletonProps) {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Base skeleton */}
      <div className="bg-gray-200 rounded animate-pulse">
        {children}
      </div>
      
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/60 to-transparent"
        animate={{
          translateX: ["100%", "100%"]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}

interface ShimmerCardProps {
  className?: string
}

export function ShimmerCard({ className }: ShimmerCardProps) {
  return (
    <div className={cn(
      "bg-white/80 backdrop-blur-md border-0 shadow-xl rounded-xl p-6 space-y-4",
      className
    )}>
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2 flex-1">
          <ShimmerSkeleton className="h-6 w-32 rounded" />
          <ShimmerSkeleton className="h-4 w-24 rounded" />
        </div>
        <ShimmerSkeleton className="h-8 w-8 rounded" />
      </div>
      
      {/* Content */}
      <div className="space-y-3">
        <ShimmerSkeleton className="h-4 w-full rounded" />
        <ShimmerSkeleton className="h-4 w-full rounded" />
        <ShimmerSkeleton className="h-4 w-3/4 rounded" />
      </div>
      
      {/* Highlight box */}
      <ShimmerSkeleton className="h-16 w-full rounded-lg" />
      
      {/* Footer */}
      <div className="flex gap-2 pt-2">
        <ShimmerSkeleton className="h-6 w-16 rounded-full" />
        <ShimmerSkeleton className="h-6 w-20 rounded-full" />
      </div>
    </div>
  )
}

interface ShimmerGridProps {
  count?: number
  className?: string
}

export function ShimmerGrid({ count = 6, className }: ShimmerGridProps) {
  return (
    <div className={cn("grid gap-6 md:grid-cols-2 lg:grid-cols-3", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
        >
          <ShimmerCard />
        </motion.div>
      ))}
    </div>
  )
}
