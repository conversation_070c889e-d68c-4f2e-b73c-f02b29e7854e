"use client"

import * as React from "react"
import {
  Root as ContextMenu,
  <PERSON><PERSON> as ContextMenuTrigger,
  Content as ContextMenuContent,
  Item as ContextMenuItem,
  CheckboxItem as ContextMenuCheckboxItem,
  RadioItem as ContextMenuRadioItem,
  Label as ContextMenuLabel,
  Separator as ContextMenuSeparator,
  Group as ContextMenuGroup,
  Portal as ContextMenuPortal,
  Sub as ContextMenuSub,
  SubContent as ContextMenuSubContent,
  SubTrigger as ContextMenuSubTrigger,
  RadioGroup as ContextMenuRadioGroup,
  ItemIndicator as ContextMenuItemIndicator,
} from "@radix-ui/react-context-menu"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const ContextMenuSubTriggerComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuSubTrigger>,
  React.ComponentPropsWithoutRef<typeof ContextMenuSubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <ContextMenuSubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",
      inset && "pl-8",
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className="ml-auto h-4 w-4" />
  </ContextMenuSubTrigger>
))
ContextMenuSubTriggerComponent.displayName = "ContextMenuSubTrigger"

const ContextMenuSubContentComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuSubContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuSubContent>
>(({ className, ...props }, ref) => (
  <ContextMenuSubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in slide-in-from-left-1",
      className
    )}
    {...props}
  />
))
ContextMenuSubContentComponent.displayName = "ContextMenuSubContent"

const ContextMenuContentComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuContent>
>(({ className, ...props }, ref) => (
  <ContextMenuPortal>
    <ContextMenuContent
      ref={ref}
      className={cn(
        "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-80",
        className
      )}
      {...props}
    />
  </ContextMenuPortal>
))
ContextMenuContentComponent.displayName = "ContextMenuContent"

const ContextMenuItemComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuItem> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <ContextMenuItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
ContextMenuItemComponent.displayName = "ContextMenuItem"

const ContextMenuCheckboxItemComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuCheckboxItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuCheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <ContextMenuCheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuItemIndicator>
        <Check className="h-4 w-4" />
      </ContextMenuItemIndicator>
    </span>
    {children}
  </ContextMenuCheckboxItem>
))
ContextMenuCheckboxItemComponent.displayName = "ContextMenuCheckboxItem"

const ContextMenuRadioItemComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuRadioItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuRadioItem>
>(({ className, children, ...props }, ref) => (
  <ContextMenuRadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </ContextMenuItemIndicator>
    </span>
    {children}
  </ContextMenuRadioItem>
))
ContextMenuRadioItemComponent.displayName = "ContextMenuRadioItem"

const ContextMenuLabelComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuLabel>,
  React.ComponentPropsWithoutRef<typeof ContextMenuLabel> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <ContextMenuLabel
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold text-foreground",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
ContextMenuLabelComponent.displayName = "ContextMenuLabel"

const ContextMenuSeparatorComponent = React.forwardRef<
  React.ElementRef<typeof ContextMenuSeparator>,
  React.ComponentPropsWithoutRef<typeof ContextMenuSeparator>
>(({ className, ...props }, ref) => (
  <ContextMenuSeparator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-border", className)}
    {...props}
  />
))
ContextMenuSeparatorComponent.displayName = "ContextMenuSeparator"

const ContextMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest text-muted-foreground",
        className
      )}
      {...props}
    />
  )
}
ContextMenuShortcut.displayName = "ContextMenuShortcut"

export {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContentComponent as ContextMenuContent,
  ContextMenuItemComponent as ContextMenuItem,
  ContextMenuCheckboxItemComponent as ContextMenuCheckboxItem,
  ContextMenuRadioItemComponent as ContextMenuRadioItem,
  ContextMenuLabelComponent as ContextMenuLabel,
  ContextMenuSeparatorComponent as ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuGroup,
  ContextMenuPortal,
  ContextMenuSub,
  ContextMenuSubContentComponent as ContextMenuSubContent,
  ContextMenuSubTriggerComponent as ContextMenuSubTrigger,
  ContextMenuRadioGroup,
}
