"use client"

import * as React from "react"
import {
  Root as AvatarRoot,
  Image as AvatarImage,
  Fallback as AvatarFallback,
} from "@radix-ui/react-avatar"

import { cn } from "@/lib/utils"

const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarRoot>,
  React.ComponentPropsWithoutRef<typeof AvatarRoot>
>(({ className, ...props }, ref) => (
  <AvatarRoot
    ref={ref}
    className={cn(
      "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",
      className
    )}
    {...props}
  />
))
Avatar.displayName = "Avatar"

const AvatarImageComponent = React.forwardRef<
  React.ElementRef<typeof AvatarImage>,
  React.ComponentPropsWithoutRef<typeof AvatarImage>
>(({ className, ...props }, ref) => (
  <AvatarImage
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
))
AvatarImageComponent.displayName = "AvatarImage"

const AvatarFallbackComponent = React.forwardRef<
  React.ElementRef<typeof AvatarFallback>,
  React.ComponentPropsWithoutRef<typeof AvatarFallback>
>(({ className, ...props }, ref) => (
  <AvatarFallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
))
AvatarFallbackComponent.displayName = "AvatarFallback"

export { 
  Avatar, 
  AvatarImageComponent as AvatarImage, 
  AvatarFallbackComponent as AvatarFallback 
}
