import * as React from "react"

import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  hasLeftIcon?: boolean
}
// export interface InputProps
//   extends React.InputHTMLAttributes<HTMLInputElement> {}

// const Input = React.forwardRef<HTMLInputElement, InputProps>(
//   ({ className, type, ...props }, ref) => {
//     return (
//       <input
//         type={type}
//         className={cn(
//           // Mobile-first input design with touch-friendly sizing
//           "flex w-full rounded-xl border border-input bg-transparent ring-offset-background",
//           "file:border-0 file:bg-transparent file:font-medium",
//           "placeholder:text-muted-foreground",
//           "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
//           "disabled:cursor-not-allowed disabled:opacity-50",
//           "shadow-sm focus-visible:shadow-md transition-all duration-200",
//           // Mobile: larger touch targets and text
//           "h-12 px-4 py-3 text-base",
//           // Tablet and up: refined sizing
//           "md:h-11 md:px-3 md:py-2 md:text-sm",
//           // Enhanced mobile interactions
//           "touch-manipulation", // Optimizes touch interactions
//           "active:scale-[0.99]", // Subtle press feedback
//           className
//         )}
//         ref={ref}
//         {...props}
//       />
//     )
//   }
// )
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, hasLeftIcon, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex w-full rounded-xl border border-input bg-transparent ring-offset-background",
          "file:border-0 file:bg-transparent file:font-medium",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "shadow-sm focus-visible:shadow-md transition-all duration-200",
          "h-12 px-4 py-3 text-base",
          "md:h-11 md:px-3 md:py-2 md:text-sm",
          "touch-manipulation",
          "active:scale-[0.99]",
          "pl-10", // ← key fix
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
