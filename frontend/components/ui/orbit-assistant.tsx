"use client"

import React, { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence, useDragControls, PanInfo } from "framer-motion"
import { MessageCircle, X, Minimize2, Maximize2, G<PERSON>Horizontal } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"
import { visualRetreat } from "@/lib/utils/responsive"

// Types for Orbit Assistant
interface OrbitPosition {
  x: number
  y: number
}

interface OrbitSize {
  width: number
  height: number
  mode: 'compact' | 'normal' | 'expanded'
}

interface OrbitState {
  isOpen: boolean
  position: OrbitPosition
  size: OrbitSize
  activeTab: 'chat' | 'research' | 'agent'
}

// Default positions and sizes
const DEFAULT_POSITION: OrbitPosition = {
  x: typeof window !== 'undefined' ? window.innerWidth - 80 : 80,
  y: typeof window !== 'undefined' ? window.innerHeight - 80 : 80
}

const DEFAULT_MOBILE_POSITION: OrbitPosition = {
  x: typeof window !== 'undefined' ? window.innerWidth / 2 - 30 : 30,
  y: typeof window !== 'undefined' ? window.innerHeight - 80 : 80
}

const SIZES: Record<OrbitSize['mode'], Omit<OrbitSize, 'mode'>> = {
  compact: { width: 320, height: 400 },
  normal: { width: 480, height: 600 },
  expanded: { width: 640, height: 800 }
}

// Storage keys
const STORAGE_KEYS = {
  position: 'orbit-position',
  size: 'orbit-size',
  isOpen: 'orbit-is-open',
  activeTab: 'orbit-active-tab'
}

// Utility functions
const isMobile = () => typeof window !== 'undefined' && window.innerWidth < 768
const getStorageKey = (key: string, orgId?: string) => 
  orgId ? `${key}-${orgId}` : key

const getSafePosition = (position: OrbitPosition, size: OrbitSize): OrbitPosition => {
  if (typeof window === 'undefined') return position

  const padding = 16
  const navHeight = 64 // Account for navigation bars
  const maxX = window.innerWidth - size.width - padding
  const maxY = window.innerHeight - size.height - padding

  return {
    x: Math.max(padding, Math.min(position.x, maxX)),
    y: Math.max(navHeight, Math.min(position.y, maxY))
  }
}

// Enhanced safe position with snap zones
const getSafePositionWithSnap = (position: OrbitPosition, size: OrbitSize): OrbitPosition => {
  if (typeof window === 'undefined') return position

  const padding = 16
  const snapThreshold = 30
  const navHeight = 64
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let { x, y } = position

  // Snap to edges
  if (x < snapThreshold) x = padding
  if (x > viewportWidth - size.width - snapThreshold) x = viewportWidth - size.width - padding
  if (y < navHeight + snapThreshold) y = navHeight
  if (y > viewportHeight - size.height - snapThreshold) y = viewportHeight - size.height - padding

  // Ensure within bounds
  x = Math.max(padding, Math.min(x, viewportWidth - size.width - padding))
  y = Math.max(navHeight, Math.min(y, viewportHeight - size.height - padding))

  return { x, y }
}

const getDefaultPosition = (): OrbitPosition => {
  if (typeof window === 'undefined') return { x: 80, y: 80 }
  
  return isMobile() ? {
    x: window.innerWidth / 2 - 30,
    y: window.innerHeight - 80
  } : {
    x: window.innerWidth - 80,
    y: window.innerHeight - 80
  }
}

// Orbit Icon Component - SIMPLIFIED SINGLE SOURCE OF TRUTH
interface OrbitIconProps {
  onClick: () => void
  initialPosition: OrbitPosition
  onPositionChange: (position: OrbitPosition) => void
}

function OrbitIcon({ onClick, initialPosition, onPositionChange }: OrbitIconProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [hasClicked, setHasClicked] = useState(false)

  const handleClick = () => {
    if (!hasClicked) {
      onClick()
    }
    setHasClicked(false)
  }

  const handleDragStart = () => {
    console.log('🎯 DRAG START')
    setIsDragging(true)
    setHasClicked(false)
  }

  const handleDrag = (event: any, info: PanInfo) => {
    console.log('🎯 DRAGGING:', info.point)
    setHasClicked(true) // Mark as dragged to prevent click
  }

  const handleDragEnd = (event: any, info: PanInfo) => {
    console.log('🎯 DRAG END:', info.point)

    // Calculate final position
    const newPosition = {
      x: info.point.x - 28, // Center the icon (56/2 = 28)
      y: info.point.y - 28
    }

    // Apply constraints
    const padding = 16
    const iconSize = 56
    const safePosition = {
      x: Math.max(padding, Math.min(newPosition.x, window.innerWidth - iconSize - padding)),
      y: Math.max(64, Math.min(newPosition.y, window.innerHeight - iconSize - padding))
    }

    console.log('🎯 FINAL POSITION:', safePosition)

    setIsDragging(false)
    onPositionChange(safePosition)
  }

  return (
    <motion.div
      drag
      dragMomentum={false}
      dragElastic={0}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
      initial={{
        x: initialPosition.x,
        y: initialPosition.y
      }}
      style={{
        position: 'fixed',
        zIndex: 9998,
        width: 56,
        height: 56
      }}
      className="cursor-grab active:cursor-grabbing"
      whileDrag={{
        scale: 1.1,
        zIndex: 9999,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.2)"
      }}
    >
      <button
        onClick={handleClick}
        className={cn(
          "relative rounded-full",
          "bg-primary/90 hover:bg-primary text-white",
          "backdrop-blur-md border-0 shadow-lg hover:shadow-xl",
          "transition-all duration-200",
          "flex items-center justify-center group",
          // Mobile: larger touch target
          isMobile() ? "h-16 w-16 touch-target-lg" : "h-14 w-14 touch-target"
        )}
        aria-label="Open Orbit AI Assistant"
        aria-describedby="orbit-tooltip"
        role="button"
      >
        {/* Pulse animation */}
        <motion.div
          className="absolute inset-0 rounded-full bg-primary/30"
          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        />

        {/* Tooltip */}
        <div
          id="orbit-tooltip"
          className={cn(
            "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1",
            "bg-gray-900 text-white text-xs rounded whitespace-nowrap",
            "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
            "pointer-events-none z-50"
          )}
        >
          Orbit AI Assistant
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900" />
        </div>

        <MessageCircle className="h-6 w-6" />
      </button>
    </motion.div>
  )
}

// Main Orbit Assistant Component
interface OrbitAssistantProps {
  className?: string
}

export function OrbitAssistant({ className }: OrbitAssistantProps) {
  const { user, orgId } = useAuth()
  const [mounted, setMounted] = useState(false)

  // SINGLE SOURCE OF TRUTH - No conflicting states
  const [iconPosition, setIconPosition] = useState<OrbitPosition>(getDefaultPosition())
  const [isOpen, setIsOpen] = useState(false)
  const [size, setSize] = useState<OrbitSize>({ ...SIZES.normal, mode: 'normal' })
  const [activeTab, setActiveTab] = useState<OrbitState['activeTab']>('chat')

  // SIMPLIFIED SAVE - No debouncing, just save immediately
  const savePosition = useCallback((position: OrbitPosition) => {
    if (!orgId) return
    try {
      const key = getStorageKey(STORAGE_KEYS.position, orgId)
      localStorage.setItem(key, JSON.stringify(position))
      console.log('💾 Position saved:', position)
    } catch (error) {
      console.warn('Failed to save position:', error)
    }
  }, [orgId])

  const saveIsOpen = useCallback((open: boolean) => {
    if (!orgId) return
    try {
      const key = getStorageKey(STORAGE_KEYS.isOpen, orgId)
      localStorage.setItem(key, JSON.stringify(open))
    } catch (error) {
      console.warn('Failed to save isOpen:', error)
    }
  }, [orgId])

  // SIMPLIFIED LOAD - Just load position
  const loadPosition = useCallback(() => {
    if (!orgId) return
    try {
      const key = getStorageKey(STORAGE_KEYS.position, orgId)
      const saved = localStorage.getItem(key)
      if (saved) {
        const position = JSON.parse(saved)
        console.log('📂 Position loaded:', position)
        setIconPosition(position)
      }
    } catch (error) {
      console.warn('Failed to load position:', error)
    }
  }, [orgId])

  // SIMPLIFIED HANDLERS
  const handlePositionChange = useCallback((newPosition: OrbitPosition) => {
    console.log('🔄 Position changed to:', newPosition)
    setIconPosition(newPosition)
    savePosition(newPosition)
  }, [savePosition])

  const handleToggleOpen = useCallback(() => {
    const newIsOpen = !isOpen
    setIsOpen(newIsOpen)
    saveIsOpen(newIsOpen)
  }, [isOpen, saveIsOpen])

  // Handle mounting - SIMPLIFIED
  useEffect(() => {
    setMounted(true)
    loadPosition()
  }, [loadPosition])

  // Don't render until mounted to avoid hydration issues
  if (!mounted) return null

  return (
    <div className={cn("orbit-assistant", className)}>
      {/* SIMPLIFIED FLOATING ICON */}
      <OrbitIcon
        onClick={handleToggleOpen}
        initialPosition={iconPosition}
        onPositionChange={handlePositionChange}
      />

      {/* SIMPLIFIED MODAL */}
      <AnimatePresence>
        {isOpen && (
          <div>Modal coming soon...</div>
        )}
      </AnimatePresence>
    </div>
  )
}

export { OrbitIcon }
