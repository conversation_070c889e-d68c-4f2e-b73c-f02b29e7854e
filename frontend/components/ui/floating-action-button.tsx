"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FloatingActionButtonProps {
  icon: React.ReactNode
  label: string
  onClick: () => void
  className?: string
  variant?: "default" | "secondary" | "outline"
  size?: "sm" | "md" | "lg"
  disabled?: boolean
}

export function FloatingActionButton({
  icon,
  label,
  onClick,
  className,
  variant = "default",
  size = "md",
  disabled = false
}: FloatingActionButtonProps) {
  const sizeClasses = {
    sm: "h-10 w-10",
    md: "h-12 w-12", 
    lg: "h-14 w-14"
  }

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className="group relative"
    >
      <Button
        onClick={onClick}
        disabled={disabled}
        variant={variant}
        size="icon"
        className={cn(
          "rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
          "backdrop-blur-md border-0",
          sizeClasses[size],
          variant === "default" && "bg-primary/90 hover:bg-primary text-white",
          variant === "secondary" && "bg-white/90 hover:bg-white text-gray-900",
          variant === "outline" && "bg-white/80 hover:bg-white border border-gray-200",
          className
        )}
      >
        {icon}
      </Button>
      
      {/* Tooltip */}
      <motion.div
        initial={{ opacity: 0, x: 10 }}
        whileHover={{ opacity: 1, x: 0 }}
        className="absolute right-full mr-3 top-1/2 -translate-y-1/2 pointer-events-none"
      >
        <div className="bg-gray-900 text-white text-sm px-3 py-1.5 rounded-lg shadow-lg whitespace-nowrap">
          {label}
          <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900" />
        </div>
      </motion.div>
    </motion.div>
  )
}
