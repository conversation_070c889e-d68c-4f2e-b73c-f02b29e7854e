"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface LoadingProps {
  className?: string
  size?: "sm" | "md" | "lg"
  fullScreen?: boolean
  text?: string
}

export function Loading({ 
  className, 
  size = "md", 
  fullScreen = false, 
  text = "Loading..." 
}: LoadingProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8"
  }

  const containerSizeClasses = {
    sm: "gap-1",
    md: "gap-2",
    lg: "gap-3"
  }

  const LoadingSquares = () => (
    <div className={cn("flex items-center justify-center", containerSizeClasses[size])}>
      {[0, 1, 2, 3].map((index) => (
        <motion.div
          key={index}
          className={cn("bg-foreground rounded-sm", sizeClasses[size])}
          style={{ opacity: 0.2 + (index * 0.2) }}
          animate={{
            rotate: [0, 90, 180, 270, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: index * 0.1
          }}
        />
      ))}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col items-center gap-4">
          <LoadingSquares />
          {text && (
            <p className="text-sm text-muted-foreground font-medium">{text}</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <LoadingSquares />
    </div>
  )
}

// Minimal inline loading component
export function LoadingDots({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center gap-1", className)}>
      {[0, 1, 2, 3].map((index) => (
        <motion.div
          key={index}
          className="w-2 h-2 bg-current rounded-sm"
          style={{ opacity: 0.6 + (index * 0.1) }}
          animate={{
            rotate: [0, 90, 180, 270, 360],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: index * 0.1
          }}
        />
      ))}
    </div>
  )
}

// Page loading component
export function PageLoading({ text = "Loading page..." }: { text?: string }) {
  return <Loading fullScreen={true} size="lg" text={text} />
} 