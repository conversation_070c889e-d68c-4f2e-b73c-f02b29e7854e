"use client"

import * as React from "react"
import { useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { useAuth } from "@/lib/auth-context"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

// Form validation schema
const loginSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address",
  }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters",
  }),
})

type LoginFormValues = z.infer<typeof loginSchema>

interface CustomAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CustomAuthForm({ className, ...props }: CustomAuthFormProps) {
  const { login } = useAuth()
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string | null>(null)
  const [loginSuccess, setLoginSuccess] = React.useState<boolean>(false)
  const searchParams = useSearchParams()

  // Check if user is already logged in and redirect if needed
  React.useEffect(() => {
    // Check if there's a valid token in localStorage
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    // Only consider the user logged in if both token and user data exist
    if (token && user) {
      try {
        // Try to parse the user data to verify it's valid
        const userData = JSON.parse(user);

        if (userData && userData.email) {
          console.log('User already logged in:', userData.email);
          setLoginSuccess(true);

          // If we're on the login page but already have a valid token and user data, redirect to dashboard
          console.log('Redirecting to dashboard');
          window.location.replace('/dashboard');
        } else {
          // Invalid user data, clear localStorage
          console.warn('Invalid user data found in localStorage');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('orgId');
        }
      } catch (e) {
        // Error parsing user data, clear localStorage
        console.error('Error parsing user data from localStorage:', e);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('orgId');
      }
    } else {
      console.log('No valid auth data found, user needs to log in');
    }
  }, [])

  // Initialize form with react-hook-form
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  // Handle form submission
  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true)
    setError(null)

    try {
      // Validate form data
      if (!data.email || !data.password) {
        throw new Error("Email and password are required");
      }

      console.log('Submitting login form with email:', data.email)

      // Call the login function from auth context with the user-provided credentials
      const result = await login(data.email, data.password)

      // Set login success state
      setLoginSuccess(true)

      // Show success toast
      toast({
        title: "Login successful",
        description: "You have been logged in successfully. Redirecting to dashboard...",
      })

      console.log('Login successful, redirecting to:', result.redirectUrl)

      // Perform the redirect immediately with a shorter timeout
      setTimeout(() => {
        console.log('Executing redirect now')
        // Use window.location.replace for a more reliable redirect
        window.location.replace(result.redirectUrl)
      }, 100)

    } catch (error: any) {
      console.error("Login error:", error)

      // Set error message with more detailed information
      if (error.response?.data?.detail) {
        setError(error.response.data.detail)
      } else if (error.message) {
        setError(error.message)
      } else {
        setError("An error occurred during login. Please try again.")
      }

      // Show error toast with more detailed information
      toast({
        title: "Login failed",
        description: error.response?.data?.detail || error.message || "Invalid email or password",
        variant: "destructive",
      })

      setIsLoading(false)
    }
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle>Login</CardTitle>
          <CardDescription>
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...form.register("email")}
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  placeholder="••••••••"
                  type="password"
                  autoCapitalize="none"
                  autoComplete="current-password"
                  disabled={isLoading}
                  {...form.register("password")}
                />
                {form.formState.errors.password && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>

              {error && (
                <div className="bg-red-50 p-3 rounded-md text-red-800 text-sm">
                  {error}
                </div>
              )}

              <Button disabled={isLoading} type="submit" className="w-full">
                {isLoading && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Sign In
              </Button>

              {/* Success message and manual navigation button */}
              {loginSuccess && (
                <>
                  <div className="bg-green-50 p-3 rounded-md text-green-800 text-sm mt-4 mb-2">
                    <p className="font-medium">Login successful!</p>
                    <p>If you are not redirected automatically, please click the button below.</p>
                  </div>
                  <Button
                    type="button"
                    className="w-full mt-2 bg-green-600 hover:bg-green-700"
                    onClick={() => {
                      console.log('Manual navigation to dashboard');
                      // Force a hard reload to the dashboard
                      window.location.replace('/dashboard');
                    }}
                  >
                    Go to Dashboard
                  </Button>
                </>
              )}
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col">
          <div className="text-sm text-muted-foreground mt-2">
            <a href="/forgot-password" className="text-primary hover:underline">
              Forgot password?
            </a>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
