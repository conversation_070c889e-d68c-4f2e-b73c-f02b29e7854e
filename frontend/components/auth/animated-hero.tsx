"use client"

import { useRef, useState, useEffect } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { Icons } from "@/components/icons"

export function AnimatedHero() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="relative z-10 max-w-2xl space-y-12 text-center">

      {/* Logo */}
      <motion.div 
        className="mb-8 flex justify-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Icons.logoFull className="h-14 text-foreground"/>
      </motion.div>

      {/* Hero Headline with Shimmer */}
      <div className="space-y-8">
        <motion.h1
          className={cn(
            "font-grotesk text-3xl font-bold leading-[1.2] pb-[0.1em] sm:text-4xl lg:text-5xl xl:text-6xl",
            "bg-clip-text text-transparent",
            "bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900",
            "dark:from-slate-100 dark:via-slate-300 dark:to-slate-100",
            "relative overflow-hidden",
            "px-4 sm:px-0"
          )}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            animate={{
              x: ["-100%", "200%"]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 4,
              ease: "easeInOut"
            }}
          />
          Access to Private Markets, the only place you need to be!
        </motion.h1>

        {/* Subtitle */}
        <motion.p 
          className="mx-auto max-w-lg px-4 text-lg leading-relaxed text-muted-foreground sm:px-0 sm:text-xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          TractionX empowers investors with AI-driven insights, automated deal flow, and intelligent thesis matching.
        </motion.p>
      </div>

      {/* Agentic CTA Button */}
      <motion.div 
        className="pt-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <motion.button
          className={cn(
            "group relative rounded-full px-4 py-3 sm:px-8 sm:py-4",
            "bg-gradient-to-r from-slate-900 to-slate-800",
            "dark:from-slate-100 dark:to-slate-200",
            "border border-amber-400/50 hover:border-amber-400/80",
            "font-mono text-xs font-medium tracking-wide sm:text-sm",
            "text-amber-100 dark:text-slate-900",
            "transition-all duration-300",
            "shadow-lg shadow-amber-400/30 hover:shadow-xl hover:shadow-amber-400/50",
            "overflow-hidden",
            "w-full max-w-sm sm:w-auto sm:max-w-none"
          )}
          onClick={() => {
            document.querySelector('form')?.scrollIntoView({behavior: 'smooth'})
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Button shimmer effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/25 to-transparent"
            animate={{
              x: ["-100%", "200%"]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 2,
              ease: "easeInOut"
            }}
          />

          <span className="relative z-10 flex items-center justify-center space-x-2">
            <span className="hidden sm:inline">WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS</span>
            <span className="sm:hidden">AGENTIC OS FOR PRIVATE MARKETS</span>
            <span className="text-amber-400">→</span>
          </span>
        </motion.button>
      </motion.div>
    </div>
  )
}
