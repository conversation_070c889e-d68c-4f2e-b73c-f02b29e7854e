"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive-safe"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { useAuth } from "@/lib/auth-context"
import { AuthCard } from "./auth-card"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

type LoginFormValues = z.infer<typeof loginSchema>

export function LoginForm() {
  const { login } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true)

    try {
      const result = await login(data.email, data.password)
      
      if (result.success) {
        toast({
          title: "Welcome back!",
          description: "You've been successfully logged in.",
        })
        
        // Redirect to intended page or dashboard
        const redirectUrl = searchParams?.get("from") || result.redirectUrl || "/dashboard"
        router.push(redirectUrl)
      }
    } catch (error) {
      console.error("Login error:", error)
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Please check your credentials and try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className={visualRetreat.form.container}>
        <div className="space-y-6">
          {/* Email Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="email" className="text-sm font-medium text-gray-700">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("email")}
            />
            {form.formState.errors.email && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.email.message}
              </motion.p>
            )}
          </div>

          {/* Password Field */}
          <div className={visualRetreat.form.field}>
            <div className="flex items-center justify-between">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                Password
              </Label>
              <Link
                href="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
              >
                Forgot password?
              </Link>
            </div>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              autoComplete="current-password"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.password.message}
              </motion.p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading}
          className={cn(
            visualRetreat.form.button,
            "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg shadow-blue-500/25"
          )}
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              Signing in...
            </>
          ) : (
            "Sign in"
          )}
        </Button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-white/20 dark:border-white/10" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            {/* <span className="bg-white/70 dark:bg-black/70 px-2 text-muted-foreground">
              Or continue with
            </span> */}
          </div>
        </div>


        {/* <Button
          type="button"
          variant="outline"
          disabled={isLoading}
          className="w-full h-11 bg-white/50 dark:bg-black/50 border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/70"
          onClick={() => {
            toast({
              title: "Coming soon",
              description: "GitHub OAuth will be available soon.",
            })
          }}
        >
          <Icons.gitHub className="mr-2 h-4 w-4" />
          GitHub
        </Button> */}

        {/* Sign Up Link */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Don't have an account?{" "}
            <Link
              href="/register"
              className="text-blue-600 hover:text-blue-500 font-medium transition-colors"
            >
              Sign up
            </Link>
          </p>
        </div>
      </form>
    </AuthCard>
  )
}
