"use client"

import Link from "next/link"
import { User } from "next-auth"
import { useAuth } from "@/lib/auth-context"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserAvatar } from "@/components/user-avatar"
import { cn } from "@/lib/utils"

interface UserAccountNavProps extends React.HTMLAttributes<HTMLDivElement> {
  user: Pick<User, "name" | "image" | "email">
}

export function UserAccountNav({ user }: UserAccountNavProps) {
  // Get the logout function from auth context
  const { logout } = useAuth();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={cn(
          "rounded-full transition-all duration-200",
          "hover:bg-accent active:scale-95 p-1",
          "touch-target focus:outline-none focus:ring-2 focus:ring-ring"
        )}>
          <UserAvatar
            user={{ name: user.name || null, image: user.image || null }}
            className={cn(
              // Mobile-first avatar sizing
              "h-9 w-9 md:h-8 md:w-8"
            )}
          />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className={cn(
        // Mobile-first dropdown sizing
        "w-64 md:w-56"
      )}>
        <div className={cn(
          "flex items-center justify-start gap-3",
          // Mobile-first padding
          "p-3 md:p-2"
        )}>
          <div className="flex flex-col space-y-1 leading-none">
            {user.name && <p className={cn(
              "font-medium",
              // Mobile-first text sizing
              "text-base md:text-sm"
            )}>{user.name}</p>}
            {user.email && (
              <p className={cn(
                "truncate text-muted-foreground",
                // Mobile-first text sizing and width
                "w-[180px] md:w-[200px] text-sm md:text-xs"
              )}>
                {user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/dashboard">Dashboard</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/dashboard/billing">Billing</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/settings/profile">Settings</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onSelect={(event) => {
            event.preventDefault()
            console.log('Logout triggered from user account nav');
            // Use our custom logout function instead of NextAuth's signOut
            logout();
          }}
        >
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
