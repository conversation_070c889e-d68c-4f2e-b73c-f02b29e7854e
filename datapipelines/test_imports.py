#!/usr/bin/env python3
"""
Test script to verify imports work correctly in the new structure.
"""

import sys
import asyncio
from typing import Optional

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")

    try:
        # Test Redis imports
        import redis
        print(f"✓ Redis import successful (version: {redis.__version__})")

        # Test RQ imports
        import rq
        print(f"✓ RQ import successful (version: {rq.__version__})")

        # Test FastAPI imports
        import fastapi
        print(f"✓ FastAPI import successful (version: {fastapi.__version__})")

        return True

    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_app_imports():
    """Test app module imports."""
    print("\nTesting app module imports...")

    try:
        # Test configs
        from app.configs import settings, get_logger
        print("✓ App configs import successful")

        # Test tasks
        from app.tasks import enrich_company_data
        print("✓ App tasks import successful")

        # Test main app
        from app.main import app
        print("✓ App main import successful")

        return True

    except ImportError as e:
        print(f"✗ App import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_redis_connection():
    """Test Redis connection."""
    print("\nTesting Redis connection...")

    try:
        import redis
        from app.configs import settings

        # Create connection
        redis_conn = redis.from_url(
            settings.REDIS_URL,
            decode_responses=True
        )

        # Test ping
        redis_conn.ping()
        print("✓ Redis connection successful")

        # Test basic operations
        redis_conn.set("test_key", "test_value", ex=10)
        value = redis_conn.get("test_key")

        if value == "test_value":
            print("✓ Redis read/write operations successful")
        else:
            print(f"✗ Redis read/write failed: expected 'test_value', got '{value}'")

        # Cleanup
        redis_conn.delete("test_key")
        redis_conn.close()

        return True

    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        return False

def test_rq_functionality():
    """Test RQ functionality."""
    print("\nTesting RQ functionality...")

    try:
        import rq
        from redis import Redis
        from app.configs import settings
        from app.tasks import enrich_company_data

        # Create Redis connection
        redis_conn = Redis.from_url(settings.REDIS_URL, decode_responses=True)

        # Create queue
        queue = rq.Queue('test', connection=redis_conn)

        # Enqueue a job
        job = queue.enqueue(enrich_company_data, {"company_id": "test123", "name": "Test Company"})
        print(f"✓ Job enqueued successfully: {job.id}")

        # Check job status
        print(f"✓ Job status: {job.get_status()}")

        redis_conn.close()
        return True

    except Exception as e:
        print(f"✗ RQ functionality test failed: {e}")
        return False

def main():
    """Main test function."""
    print("TractionX Data Pipeline - Import & Functionality Test")
    print("=" * 60)

    # Test imports
    basic_imports_ok = test_basic_imports()
    app_imports_ok = test_app_imports()

    # Test connections (only if imports work)
    redis_ok = False
    rq_ok = False

    if basic_imports_ok and app_imports_ok:
        redis_ok = test_redis_connection()
        rq_ok = test_rq_functionality()

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"Basic imports: {'✓ PASS' if basic_imports_ok else '✗ FAIL'}")
    print(f"App imports: {'✓ PASS' if app_imports_ok else '✗ FAIL'}")
    print(f"Redis connection: {'✓ PASS' if redis_ok else '✗ FAIL'}")
    print(f"RQ functionality: {'✓ PASS' if rq_ok else '✗ FAIL'}")

    if all([basic_imports_ok, app_imports_ok, redis_ok, rq_ok]):
        print("\n🎉 All tests passed! The restructured pipeline is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
