services:
  api:
    build: .
    command: uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
    ports:
      - "8001:8001"
    volumes:
      - .:/app          # only for dev/hot reload, not for prod
    environment:
      - PYTHONPATH=/app
      - ENV=dev
    env_file:
      - .env

  worker:
    build: .
    # Option 1: Use the improved watch script (auto-reload on relevant changes only)
    command: bash -c "chmod +x /app/watch_worker.sh && /app/watch_worker.sh"
    # Option 2: Run worker directly without auto-reload (uncomment to use)
    # command: python app/worker_runner.py --worker-id dev-worker-1 --concurrency 4 --poll-interval 1.0
    volumes:
      - .:/app          # For development to enable hot reload
    environment:
      - PYTHONPATH=/app
      - ENV=dev
      - WATCHDOG_ENABLED=1    # Set to 0 to disable file watching
    env_file:
      - .env



