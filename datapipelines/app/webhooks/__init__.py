"""
Webhook endpoints for TractionX Data Pipeline Service.
"""

from fastapi import APIRouter
from .clay_webhook import router as clay_router
from .generic_webhook import router as generic_router

# Create main webhook router
router = APIRouter()

# Include sub-routers
router.include_router(clay_router, prefix="/clay", tags=["clay"])
router.include_router(generic_router, prefix="/generic", tags=["generic"])

__all__ = ["router"]
