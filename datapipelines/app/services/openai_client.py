"""
OpenAI API Client for TractionX Data Pipeline Service.

Handles chat completions with OpenAI GPT models for founder signal generation.
Provides robust error handling, retries, and performance monitoring.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional

import httpx
from app.configs.logging import get_logger
from app.configs.settings import settings

logger = get_logger(__name__)


class OpenAIError(Exception):
    """Base exception for OpenAI API errors."""

    pass


class OpenAIRateLimitError(OpenAIError):
    """Raised when rate limit is exceeded."""

    pass


class OpenAITimeoutError(OpenAIError):
    """Raised when request times out."""

    pass


class OpenAIClient:
    """
    Client for OpenAI Chat Completions API.
    Handles founder signal generation with robust error handling and retries.
    """

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or settings.OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        self.base_url = "https://api.openai.com/v1"
        self.timeout = 60.0  # Longer timeout for signal generation
        self.max_retries = 3
        self.retry_delay = 1.0

        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a chat completion request with retries and error handling.

        Args:
            messages: List of message dictionaries with role and content
            model: OpenAI model to use
            max_tokens: Maximum tokens in response
            temperature: Response randomness (0.0-2.0)
            stream: Whether to stream the response

        Returns:
            completion_data: Full completion response with performance metadata

        Raises:
            OpenAIError: For API errors
            OpenAIRateLimitError: For rate limit issues
            OpenAITimeoutError: For timeout issues
        """
        start_time = time.time()

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            "presence_penalty": 0,
            "frequency_penalty": 0,
        }

        for attempt in range(self.max_retries + 1):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        f"{self.base_url}/chat/completions",
                        headers=self.headers,
                        json=payload,
                    )

                    if response.status_code == 429:
                        if attempt < self.max_retries:
                            wait_time = self.retry_delay * (2**attempt)
                            logger.warning(
                                f"Rate limit hit, retrying in {wait_time}s (attempt {attempt + 1})"
                            )
                            await asyncio.sleep(wait_time)
                            continue
                        raise OpenAIRateLimitError(
                            "Rate limit exceeded after all retries"
                        )

                    elif response.status_code == 400:
                        error_detail = response.text
                        raise OpenAIError(f"Bad request: {error_detail}")
                    elif response.status_code == 401:
                        raise OpenAIError("Invalid API key")
                    elif response.status_code == 403:
                        raise OpenAIError(
                            "Access forbidden. Check your API permissions."
                        )
                    elif response.status_code >= 500:
                        if attempt < self.max_retries:
                            wait_time = self.retry_delay * (2**attempt)
                            logger.warning(
                                f"Server error {response.status_code}, retrying in {wait_time}s (attempt {attempt + 1})"
                            )
                            await asyncio.sleep(wait_time)
                            continue
                        raise OpenAIError(
                            f"OpenAI server error ({response.status_code}) after all retries"
                        )
                    elif response.status_code >= 400:
                        error_detail = response.text
                        raise OpenAIError(
                            f"API error {response.status_code}: {error_detail}"
                        )

                    result = response.json()

                    if not result.get("choices"):
                        raise OpenAIError(
                            "No response choices returned from OpenAI API"
                        )

                    # Add performance metadata
                    response_time_ms = int((time.time() - start_time) * 1000)
                    result["_performance"] = {
                        "response_time_ms": response_time_ms,
                        "model": model,
                        "attempts": attempt + 1,
                    }

                    logger.info(
                        "Completed OpenAI chat completion",
                        model=model,
                        response_time_ms=response_time_ms,
                        attempts=attempt + 1,
                        tokens_used=result.get("usage", {}).get("total_tokens", 0),
                    )
                    return result

            except httpx.TimeoutException:
                if attempt < self.max_retries:
                    wait_time = self.retry_delay * (2**attempt)
                    logger.warning(
                        f"Request timeout, retrying in {wait_time}s (attempt {attempt + 1})"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                raise OpenAITimeoutError("Request timed out after all retries")

            except httpx.RequestError as e:
                if attempt < self.max_retries:
                    wait_time = self.retry_delay * (2**attempt)
                    logger.warning(
                        f"Network error, retrying in {wait_time}s (attempt {attempt + 1}): {e}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                logger.error(f"Network error during completion after all retries: {e}")
                raise OpenAIError("Network error after all retries")

            except Exception as e:
                if isinstance(
                    e, (OpenAIError, OpenAIRateLimitError, OpenAITimeoutError)
                ):
                    raise
                logger.error(f"Unexpected error during completion: {e}")
                raise OpenAIError(f"Failed to create completion: {str(e)}")

        # This should never be reached, but just in case
        raise OpenAIError("All retry attempts failed")

    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from OpenAI completion response.

        Args:
            completion_data: Response from create_chat_completion

        Returns:
            Extracted content string
        """
        try:
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content

            return "No response generated"

        except Exception as e:
            logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"

    def extract_performance_metadata(
        self, completion_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract performance metadata from completion response.

        Args:
            completion_data: Response from create_chat_completion

        Returns:
            Performance metadata dictionary
        """
        try:
            performance = completion_data.get("_performance", {})
            usage = completion_data.get("usage", {})

            return {
                "response_time_ms": performance.get("response_time_ms"),
                "token_count": usage.get("total_tokens"),
                "model": performance.get("model"),
                "attempts": performance.get("attempts", 1),
            }

        except Exception as e:
            logger.error(f"Error extracting performance metadata: {e}")
            return {}

    async def generate_founder_signals(
        self,
        system_prompt: str,
        user_prompt: str,
        model: str = "gpt-4o",
        temperature: float = 0.3,
    ) -> Optional[str]:
        """
        Generate founder signals using OpenAI.

        Args:
            system_prompt: System prompt for signal generation
            user_prompt: User prompt with founder data
            model: OpenAI model to use
            temperature: Response temperature

        Returns:
            Generated signal content or None if failed
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]

            completion = await self.create_chat_completion(
                messages=messages, model=model, max_tokens=2000, temperature=temperature
            )

            content = self.extract_content_from_completion(completion)
            performance = self.extract_performance_metadata(completion)

            logger.info(
                "Generated founder signals successfully",
                model=model,
                response_time_ms=performance.get("response_time_ms"),
                token_count=performance.get("token_count"),
            )

            return content

        except Exception as e:
            logger.error(f"Failed to generate founder signals: {e}")
            return None
