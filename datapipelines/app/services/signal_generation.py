"""
LLM-based founder signal generation service.
"""

import json
import time
from typing import Any, Dict, Optional

from app.configs.logging import get_logger
from app.models.founder import (
    FounderSignalInput,
    FounderSignalOutput,
    FounderSignalRecord,
    SkillProfile,
)
from app.services.openai_client import OpenAIClient, OpenAIError


class SignalGenerationService:
    """Service for generating founder signals using LLM analysis."""

    def __init__(self, llm_client: Optional[OpenAIClient] = None):
        self.logger = get_logger(__name__)
        self.llm_client = llm_client or OpenAIClient()
        self.model = "gpt-4o"  # Use latest model
        self.max_retries = 3

    async def generate_founder_signals(
        self, founder_data: Dict[str, Any], founder_id: str
    ) -> Optional[FounderSignalRecord]:
        """
        Generate founder signals using LLM analysis.

        Args:
            founder_data: Structured founder data from RDS
            founder_id: Founder identifier

        Returns:
            FounderSignalRecord if successful, None if failed
        """
        try:
            start_time = time.time()

            # Prepare input data
            signal_input = self._prepare_signal_input(founder_data)

            # Generate signals using LLM
            llm_output = await self._call_llm_for_signals(signal_input)

            if not llm_output:
                self.logger.error(
                    f"Failed to generate signals for founder {founder_id}"
                )
                return None

            # Calculate processing time
            processing_time = time.time() - start_time
            self.logger.info(
                f"Processing time: {llm_output.skill_profile.model_dump()}"
            )
            # Create signal record
            signal_record = FounderSignalRecord(
                founder_id=founder_id,
                score=llm_output.score,
                tags=llm_output.tags,
                strengths={"items": llm_output.strengths},
                risks={"items": llm_output.risks},
                skill_profile=llm_output.skill_profile.model_dump(),
                llm_model=self.model,
                prompt_version="v1",
                processing_time=processing_time,
            )  # type: ignore

            self.logger.info(
                f"Generated signals for founder {founder_id}",
                score=llm_output.score,
                processing_time=processing_time,
            )

            return signal_record

        except Exception as e:
            self.logger.error(f"Error generating signals for founder {founder_id}: {e}")
            return None

    def _prepare_signal_input(self, founder_data: Dict[str, Any]) -> FounderSignalInput:
        """Prepare structured input for LLM signal generation."""

        # Extract current job info
        current_job = None
        if founder_data.get("current_job_title") and founder_data.get(
            "current_job_company"
        ):
            current_job = f"{founder_data['current_job_title']} at {founder_data['current_job_company']}"

        # Format experiences
        experiences = []
        for exp in founder_data.get("experiences", []):
            experience = {
                "title": exp.get("title"),
                "company": exp.get("company_name"),
                "start_date": exp.get("start_date"),
                "end_date": exp.get("end_date"),
                "is_primary": exp.get("is_primary", False),
            }
            experiences.append(experience)

        # Format education
        education = []
        for edu in founder_data.get("education", []):
            edu_record = {
                "school_name": edu.get("school_name"),
                "degrees": edu.get("degrees", []),
                "majors": edu.get("majors", []),
                "start_date": edu.get("start_date"),
                "end_date": edu.get("end_date"),
            }
            education.append(edu_record)

        # Format profiles
        profiles = []
        for profile in founder_data.get("profiles", []):
            profile_record = {
                "network": profile.get("network"),
                "url": profile.get("url"),
            }
            profiles.append(profile_record)

        return FounderSignalInput(
            full_name=founder_data.get("full_name", "Unknown"),
            current_job=current_job,
            experiences=experiences,
            education=education,
            skills=founder_data.get("skills", []),
            profiles=profiles,
        )  # type: ignore

    async def _call_llm_for_signals(
        self, signal_input: FounderSignalInput
    ) -> Optional[FounderSignalOutput]:
        """Call LLM to generate founder signals."""

        # Create the system prompt
        system_prompt = self._get_system_prompt()

        # Create the user prompt
        user_prompt = self._get_user_prompt(signal_input)

        try:
            # Use the OpenAI client to generate signals
            content = await self.llm_client.generate_founder_signals(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                model=self.model,
                temperature=0.3,
            )

            if not content:
                self.logger.error("Failed to generate signals from OpenAI")
                return None

            # Parse and validate response
            signal_output = self._parse_llm_response(content)
            if signal_output:
                return signal_output
            else:
                self.logger.error("Failed to parse LLM response")
                return None

        except OpenAIError as e:
            self.logger.error(f"OpenAI API error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during LLM call: {e}")
            return None

    def _get_system_prompt(self) -> str:
        """Get the system prompt for signal generation."""
        return """You are Orbit — TractionX's AI investment analyst specializing in founder evaluation for early-stage venture capital.

Your mission is to assess founders with the precision and insight of a seasoned VC associate, providing nuanced analysis that helps investors make informed decisions about early-stage teams.

ANALYSIS FRAMEWORK:
- Evaluate each founder as a unique individual with distinct background, skills, and potential
- Consider the founder's specific career trajectory, education, and achievements
- Assess how their background aligns with their current venture and market opportunity
- Identify both strengths that indicate founder-market fit and risks that could impact success
- Provide balanced, evidence-based analysis without overhyping or over-penalizing

SCORING CRITERIA (0-100):
- 85-100: Exceptional founder with proven track record, strong domain expertise, and clear founder-market fit
- 70-84: Strong founder with relevant experience, good educational background, and promising indicators
- 55-69: Decent founder with some relevant experience but gaps in key areas
- 40-54: Risky founder with limited relevant experience or significant gaps
- 0-39: High-risk founder with minimal relevant background or major red flags

SKILL ASSESSMENT (0-10 scale):
- Business: Strategic thinking, market understanding, business model development
- Operations: Execution ability, team management, operational efficiency
- Fundraising: Investor relations, pitch skills, financial acumen
- Product: Product vision, user understanding, development process knowledge
- Tech: Technical expertise, engineering background, technology understanding

OUTPUT REQUIREMENTS:
- Provide a valid JSON object with all required fields
- Ensure each analysis is unique to the specific founder's background
- Base all assessments on the provided founder data
- Use specific, evidence-based language in strengths and risks
- Create punchy, memorable tags that capture key founder characteristics

Output format:
{
"score": 78,
"strengths": ["International education and business exposure", "Founder of multiple ventures before age 25"],
"risks": ["No deep technical background", "Short tenures in multiple roles"],
"tags": ["young founder", "sales-driven", "needs technical cofounder"],
"skill_profile": {
    "business": 8,
    "operations": 7,
    "fundraising": 6,
    "product": 5,
    "tech": 2
}
}"""

    def _get_user_prompt(self, signal_input: FounderSignalInput) -> str:
        """Get the user prompt with founder data."""
        return f"""Please provide a comprehensive, founder-specific analysis for venture capital evaluation.

FOUNDER DATA:
{signal_input.to_llm_prompt()}

ANALYSIS INSTRUCTIONS:
- Analyze this specific founder's background, experience, and potential
- Consider their unique career trajectory and how it relates to startup success
- Evaluate their skill set and identify both strengths and gaps
- Provide a nuanced score that reflects their founder potential
- Ensure your analysis is tailored to this individual's specific background

Provide your analysis in the exact JSON format specified in the system prompt, ensuring each assessment is unique to this founder's profile."""

    def _parse_llm_response(self, content: str) -> Optional[FounderSignalOutput]:
        """Parse and validate LLM response."""
        try:
            # Extract JSON from response (handle markdown code blocks)
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            # Parse JSON
            data = json.loads(content)

            # Validate and create output
            return FounderSignalOutput(
                score=data["score"],
                strengths=data["strengths"],
                risks=data["risks"],
                tags=data["tags"],
                skill_profile=SkillProfile(**data["skill_profile"]),
            )

        except Exception as e:
            self.logger.error(f"Failed to parse LLM response: {e}")
            self.logger.debug(f"Raw response: {content}")
            return None
