#!/usr/bin/env python3
"""
Worker Runner for TractionX Data Pipeline Service

This script starts the new round-robin queue worker with proper configuration,
monitoring, and graceful shutdown handling.
"""

import argparse
import asyncio
import os
import sys
from typing import Optional

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.configs import get_logger, settings
from app.queueing.backends.redis_backend import RedisQueueBackend
from app.queueing.service import TractionXQueueService

logger = get_logger(__name__)


class SimpleRoundRobinWorker:
    """
    Simplified round-robin worker that works with the current codebase.
    """

    def __init__(
        self,
        worker_id: str = "worker-1",
        concurrency: int = 4,
        poll_interval: float = 1.0,
    ):
        self.worker_id = worker_id
        self.concurrency = concurrency
        self.poll_interval = poll_interval
        self.running = False
        self.queue_service: Optional[TractionXQueueService] = None

        # Priority queues in order (high to low)
        self.queue_priorities = ["high", "normal", "low"]

        # Load job handlers
        from app.tasks import get_job_handlers

        self.handlers = get_job_handlers()

        logger.info(
            f"Worker {self.worker_id} initialized",
            handlers=list(self.handlers.keys()),
        )

    async def initialize(self) -> None:
        """Initialize the worker."""
        # Create Redis backend
        backend = RedisQueueBackend(
            redis_url=settings.redis_connection_string,
            key_prefix=settings.REDIS_KEY_PREFIX,
        )

        # Create queue service
        self.queue_service = TractionXQueueService(backend)
        await self.queue_service.initialize()

        logger.info(f"Worker {self.worker_id} queue service initialized")

    async def start(self) -> None:
        """Start the worker."""
        if not self.queue_service:
            await self.initialize()

        self.running = True
        logger.info(f"Starting worker {self.worker_id}")

        # Start worker tasks
        tasks = []
        for i in range(self.concurrency):
            task = asyncio.create_task(self._worker_loop(f"{self.worker_id}-{i}"))
            tasks.append(task)

        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.running = False
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _worker_loop(self, task_id: str) -> None:
        """Main worker loop."""
        logger.info(f"Worker task {task_id} started")

        while self.running:
            try:
                # Try to get a job using round-robin priority
                if self.queue_service and hasattr(self.queue_service, "backend"):
                    job_data = await self.queue_service.backend.dequeue(
                        queue_names=self.queue_priorities,
                        timeout=int(self.poll_interval),
                    )

                    if job_data:
                        await self._process_job(job_data, task_id)
                    else:
                        # No jobs available, brief sleep
                        await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info(f"Worker task {task_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker task {task_id} error: {e}", exc_info=True)
                await asyncio.sleep(1)

        logger.info(f"Worker task {task_id} stopped")

    async def _process_job(self, job_data: dict, task_id: str) -> None:
        """Process a single job."""
        job_id = job_data.get("job_id", "unknown")
        job_func = job_data.get("job_func")
        job_args = job_data.get("job_args", {})

        if not job_func:
            logger.error(f"Job {job_id} has no job_func specified")
            return

        logger.info(f"Processing job {job_id}: {job_func}")

        try:
            # Find handler
            handler = self.handlers.get(job_func)
            if not handler:
                raise ValueError(f"No handler found for: {job_func}")

            # Execute handler (now async)
            result = await handler(job_args)
            logger.info(f"Job {job_id} result: {result}")

            # Mark as completed
            if self.queue_service:
                await self.queue_service.backend.complete_job(job_id, result)

            logger.info(f"Job {job_id} completed successfully")

        except Exception as e:
            error_msg = f"Job {job_id} failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            if self.queue_service:
                await self.queue_service.backend.fail_job(job_id, error_msg, retry=True)


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="TractionX Data Pipeline Worker")
    parser.add_argument("--worker-id", default="worker-1", help="Worker ID")
    parser.add_argument(
        "--concurrency", type=int, default=4, help="Number of concurrent workers"
    )
    parser.add_argument(
        "--poll-interval", type=float, default=1.0, help="Poll interval in seconds"
    )

    args = parser.parse_args()

    logger.info("Starting TractionX Data Pipeline Worker")
    logger.info(f"Worker ID: {args.worker_id}")
    logger.info(f"Concurrency: {args.concurrency}")
    logger.info(f"Poll Interval: {args.poll_interval}s")

    worker = SimpleRoundRobinWorker(
        worker_id=args.worker_id,
        concurrency=args.concurrency,
        poll_interval=args.poll_interval,
    )

    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
