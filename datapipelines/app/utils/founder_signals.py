"""
Founder signal generation utilities for LLM-based scoring and team analysis.
"""

import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from app.configs import get_logger
from app.models.founder import FounderSignal, TeamSignal
from app.storage.rds_storage import RDSStorage

logger = get_logger(__name__)


class FounderSignalGenerator:
    """Utility class for generating LLM-based founder signals and team analysis."""
    
    def __init__(self, rds_storage: RDSStorage):
        self.rds = rds_storage
        
    async def generate_founder_signals(self, founder_id: str) -> Optional[FounderSignal]:
        """
        Generate LLM-based signals for a single founder.
        
        Args:
            founder_id: The founder UUID or founder_id
            
        Returns:
            FounderSignal object or None if generation fails
        """
        try:
            # Get founder data with all relations
            founder_data = await self.rds.get_founder_with_relations(founder_id)
            if not founder_data:
                logger.warning(f"Founder {founder_id} not found")
                return None
            
            # Analyze founder data
            analysis = await self._analyze_founder_profile(founder_data)
            
            # Create founder signal record
            signal = FounderSignal(
                id=str(uuid4()),
                founder_id=founder_data["id"],  # Use the UUID
                score=analysis["score"],
                tags=analysis["tags"],
                strengths=analysis["strengths"],
                risks=analysis["risks"],
                generated_at=datetime.now(timezone.utc)
            )
            
            # Store in database
            await self.rds.insert("founder_signals", signal.model_dump())
            
            logger.info(f"Generated signals for founder {founder_id}: score={analysis['score']}")
            return signal
            
        except Exception as e:
            logger.error(f"Failed to generate signals for founder {founder_id}: {str(e)}")
            return None
    
    async def generate_team_signals(self, company_id: str, org_id: str) -> Optional[TeamSignal]:
        """
        Generate team-level signals for all founders in a company.
        
        Args:
            company_id: Company identifier
            org_id: Organization identifier
            
        Returns:
            TeamSignal object or None if generation fails
        """
        try:
            # Get all founders for the company
            founders = await self.rds.get_by_fields("founders", {
                "company_id": company_id,
                "org_id": org_id
            })
            
            if not founders:
                logger.warning(f"No founders found for company {company_id}")
                return None
            
            # Get detailed data for each founder
            founder_profiles = []
            for founder in founders:
                profile = await self.rds.get_founder_with_relations(founder["id"])
                if profile:
                    founder_profiles.append(profile)
            
            if not founder_profiles:
                logger.warning(f"No founder profiles found for company {company_id}")
                return None
            
            # Analyze team composition
            team_analysis = await self._analyze_team_composition(founder_profiles)
            
            # Create team signal record
            signal = TeamSignal(
                id=str(uuid4()),
                company_id=company_id,
                org_id=org_id,
                complementarity_score=team_analysis["complementarity_score"],
                coverage=team_analysis["coverage"],
                narrative=team_analysis["narrative"],
                generated_at=datetime.now(timezone.utc)
            )
            
            # Store in database
            await self.rds.insert("team_signals", signal.model_dump())
            
            logger.info(f"Generated team signals for company {company_id}: complementarity={team_analysis['complementarity_score']}")
            return signal
            
        except Exception as e:
            logger.error(f"Failed to generate team signals for company {company_id}: {str(e)}")
            return None
    
    async def _analyze_founder_profile(self, founder_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a founder profile and generate scoring/tags.
        This is a placeholder for LLM-based analysis.
        """
        # Extract key metrics
        experiences = founder_data.get("experiences", [])
        education = founder_data.get("education", [])
        skills = founder_data.get("skills", [])
        
        # Calculate base score
        score = 50  # Base score
        
        # Experience scoring
        if experiences:
            # Add points for relevant experience
            for exp in experiences:
                if exp.get("is_primary"):
                    score += 10
                if exp.get("title") and any(keyword in exp["title"].lower() for keyword in ["ceo", "founder", "cto", "vp"]):
                    score += 15
                if exp.get("company_size") in ["1001-5000", "5001-10000", "10001+"]:
                    score += 10
        
        # Education scoring
        if education:
            for edu in education:
                if edu.get("degrees"):
                    if any("master" in degree.lower() for degree in edu["degrees"]):
                        score += 10
                    if any("phd" in degree.lower() or "doctorate" in degree.lower() for degree in edu["degrees"]):
                        score += 15
        
        # Skills scoring
        tech_skills = ["python", "javascript", "machine learning", "artificial intelligence", "data science"]
        business_skills = ["strategy", "marketing", "sales", "business development"]
        
        tech_count = sum(1 for skill in skills if any(tech in skill.lower() for tech in tech_skills))
        business_count = sum(1 for skill in skills if any(biz in skill.lower() for biz in business_skills))
        
        score += min(tech_count * 3, 15)  # Max 15 points for tech skills
        score += min(business_count * 3, 15)  # Max 15 points for business skills
        
        # Cap score at 100
        score = min(score, 100)
        
        # Generate tags
        tags = []
        if tech_count > 3:
            tags.append("technical")
        if business_count > 3:
            tags.append("business-oriented")
        if any(exp.get("is_primary") for exp in experiences):
            tags.append("experienced")
        if len(experiences) > 3:
            tags.append("diverse-background")
        
        # Generate strengths and risks
        strengths = {}
        risks = {}
        
        if experiences:
            strengths["experience"] = f"Has {len(experiences)} professional experiences"
        if education:
            strengths["education"] = f"Educational background from {len(education)} institutions"
        if skills:
            strengths["skills"] = f"Demonstrates {len(skills)} professional skills"
        
        if score < 60:
            risks["overall"] = "Below average founder profile score"
        if not experiences:
            risks["experience"] = "Limited professional experience"
        if not education:
            risks["education"] = "No formal education data available"
        
        return {
            "score": score,
            "tags": tags,
            "strengths": strengths,
            "risks": risks
        }
    
    async def _analyze_team_composition(self, founder_profiles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze team composition and generate complementarity metrics.
        This is a placeholder for LLM-based team analysis.
        """
        # Analyze skill coverage
        all_skills = set()
        tech_founders = 0
        business_founders = 0
        
        for profile in founder_profiles:
            skills = profile.get("skills", [])
            all_skills.update(skills)
            
            # Categorize founders
            tech_skills = sum(1 for skill in skills if any(tech in skill.lower() for tech in ["python", "javascript", "engineering", "technical"]))
            business_skills = sum(1 for skill in skills if any(biz in skill.lower() for biz in ["marketing", "sales", "business", "strategy"]))
            
            if tech_skills > business_skills:
                tech_founders += 1
            else:
                business_founders += 1
        
        # Calculate complementarity score
        complementarity_score = 50  # Base score
        
        # Team balance
        if tech_founders > 0 and business_founders > 0:
            complementarity_score += 30  # Good balance
        
        # Team size
        team_size = len(founder_profiles)
        if 2 <= team_size <= 4:
            complementarity_score += 20  # Optimal team size
        elif team_size > 4:
            complementarity_score -= 10  # Too large
        
        # Skill diversity
        if len(all_skills) > team_size * 3:
            complementarity_score += 20  # Good skill diversity
        
        complementarity_score = min(complementarity_score, 100)
        
        # Generate coverage analysis
        coverage = {
            "technical_coverage": tech_founders > 0,
            "business_coverage": business_founders > 0,
            "team_size": team_size,
            "total_skills": len(all_skills),
            "skill_diversity_ratio": len(all_skills) / max(team_size, 1)
        }
        
        # Generate narrative
        narrative_parts = []
        narrative_parts.append(f"Team of {team_size} founders")
        
        if tech_founders > 0 and business_founders > 0:
            narrative_parts.append(f"with balanced technical ({tech_founders}) and business ({business_founders}) expertise")
        elif tech_founders > business_founders:
            narrative_parts.append("with strong technical focus")
        else:
            narrative_parts.append("with strong business focus")
        
        narrative_parts.append(f"demonstrating {len(all_skills)} combined skills")
        
        narrative = ", ".join(narrative_parts) + "."
        
        return {
            "complementarity_score": complementarity_score,
            "coverage": coverage,
            "narrative": narrative
        }
    
    async def batch_generate_founder_signals(self, batch_size: int = 50) -> Dict[str, Any]:
        """
        Generate signals for all founders that don't have recent signals.
        
        Args:
            batch_size: Number of founders to process in each batch
            
        Returns:
            Processing statistics
        """
        stats = {
            "total_processed": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "errors": []
        }
        
        try:
            # Find founders without recent signals (older than 30 days)
            cutoff_date = datetime.now(timezone.utc).replace(day=1)  # Beginning of current month
            
            founders_sql = """
                SELECT f.id, f.founder_id 
                FROM founders f
                LEFT JOIN founder_signals fs ON f.id = fs.founder_id 
                WHERE fs.generated_at IS NULL OR fs.generated_at < %s
                ORDER BY f.created_at DESC
            """
            
            founders_needing_signals = await self.rds.execute_query(
                founders_sql, 
                {"cutoff_date": cutoff_date}
            )
            
            logger.info(f"Found {len(founders_needing_signals)} founders needing signal generation")
            
            # Process in batches
            for i in range(0, len(founders_needing_signals), batch_size):
                batch = founders_needing_signals[i:i + batch_size]
                
                for founder in batch:
                    try:
                        signal = await self.generate_founder_signals(founder["id"])
                        if signal:
                            stats["successful_generations"] += 1
                        else:
                            stats["failed_generations"] += 1
                    except Exception as e:
                        error_msg = f"Failed to generate signals for founder {founder['founder_id']}: {str(e)}"
                        logger.error(error_msg)
                        stats["errors"].append(error_msg)
                        stats["failed_generations"] += 1
                    
                    stats["total_processed"] += 1
                
                logger.info(f"Processed batch {i//batch_size + 1}, total: {stats['total_processed']}")
                
        except Exception as e:
            logger.error(f"Batch signal generation failed: {str(e)}")
            stats["errors"].append(f"Batch processing failed: {str(e)}")
        
        logger.info(f"Batch signal generation completed. Stats: {stats}")
        return stats


async def run_signal_generation():
    """Standalone function to run signal generation."""
    rds_storage = RDSStorage()
    await rds_storage.initialize()
    
    try:
        generator = FounderSignalGenerator(rds_storage)
        stats = await generator.batch_generate_founder_signals()
        print(f"Signal generation completed: {stats}")
    finally:
        await rds_storage.cleanup()


if __name__ == "__main__":
    asyncio.run(run_signal_generation())
