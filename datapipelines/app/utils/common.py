from typing import Annotated, Any

from bson import ObjectId
from pydantic import GetJsonSchemaHandler
from pydantic_core import CoreSchema, core_schema


class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        _source_type: Any,
        _handler: GetJsonSchemaHandler,
    ) -> CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ]),
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x),
                return_schema=core_schema.str_schema(),
                when_used="json",
            ),
        )

    def __str__(self):
        return super().__str__()

    def __repr__(self):
        return f"ObjectId('{str(self)}')"

    def __eq__(self, other):
        if isinstance(other, ObjectId):
            return str(self) == str(other)
        return False

    def __hash__(self):
        return hash(str(self))


ObjectIdField = Annotated[PyObjectId, PyObjectId]

# ObjectIdField = Annotated[PyObjectId, Field(default_factory=PyObjectId)]
