"""
API endpoints for TractionX Data Pipeline Service.
"""

from fastapi import APIRouter
from .pipelines import router as pipelines_router
from .status import router as status_router

# Create main API router
router = APIRouter()

# Include sub-routers
router.include_router(pipelines_router, prefix="/pipelines", tags=["pipelines"])
router.include_router(status_router, prefix="/status", tags=["status"])

__all__ = ["router"]
