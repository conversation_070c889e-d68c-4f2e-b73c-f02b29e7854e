"""
Company enrichment pipeline for TractionX Data Pipeline Service.
"""

import time
from typing import Any, Dict, Optional
import httpx

from app.configs import settings
from app.models.base import ProcessingResult, EnrichmentSource
from app.models.company import ClayCompanyData
from .base import BasePipeline


class CompanyEnrichmentPipeline(BasePipeline):
    """Pipeline for enriching company data using external APIs."""
    
    def __init__(self):
        super().__init__("company_enrichment")
        self.clay_client: Optional[httpx.AsyncClient] = None
    
    async def _initialize_pipeline(self) -> None:
        """Initialize the company enrichment pipeline."""
        # Initialize Clay API client if configured
        if settings.CLAY_API_KEY:
            self.clay_client = httpx.AsyncClient(
                base_url=settings.CLAY_BASE_URL,
                headers={
                    "Authorization": f"Bearer {settings.CLAY_API_KEY}",
                    "Content-Type": "application/json"
                },
                timeout=30.0
            )
            self.logger.info("Clay API client initialized")
        else:
            self.logger.warning("Clay API key not configured, Clay enrichment will be skipped")
    
    async def _cleanup_pipeline(self) -> None:
        """Clean up pipeline resources."""
        if self.clay_client:
            await self.clay_client.aclose()
            self.clay_client = None
    
    def _get_required_fields(self) -> list[str]:
        """Get required input fields."""
        return ["company_id", "org_id", "company_name"]
    
    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Validate input data for company enrichment."""
        # Check required fields
        validation_result = self._validate_required_fields(input_data)
        if not validation_result.success:
            return validation_result
        
        # Validate company name
        company_name = input_data.get("company_name", "").strip()
        if not company_name:
            return ProcessingResult.error_result(
                error_message="Company name cannot be empty",
                error_type="ValidationError"
            )
        
        return ProcessingResult.success_result({})
    
    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process company data through enrichment APIs."""
        company_id = input_data["company_id"]
        company_name = input_data["company_name"]
        domain = input_data.get("domain")
        
        self.logger.info(
            "Starting company enrichment",
            company_id=company_id,
            company_name=company_name,
            domain=domain
        )
        
        enrichment_data = {}
        
        # Clay enrichment
        if settings.ENABLE_COMPANY_ENRICHMENT and self.clay_client:
            try:
                clay_data = await self._enrich_with_clay(company_name, domain)
                if clay_data:
                    enrichment_data["clay_data"] = clay_data
                    self.logger.info("Clay enrichment completed successfully")
                else:
                    self.logger.warning("No data returned from Clay enrichment")
            except Exception as e:
                self.logger.error(f"Clay enrichment failed: {e}")
                # Continue with other enrichments even if Clay fails
        
        # Store raw enrichment data
        if enrichment_data:
            self._store_raw_data(
                enrichment_data,
                f"company/{company_id}/enrichment_{int(time.time())}.json"
            )
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(enrichment_data)
        
        return ProcessingResult.success_result(
            data=enrichment_data,
            metadata={
                "company_id": company_id,
                "company_name": company_name,
                "confidence_score": confidence_score,
                "enrichment_sources": list(enrichment_data.keys())
            },
            source=EnrichmentSource.CLAY
        )
    
    async def _enrich_with_clay(self, company_name: str, domain: Optional[str] = None) -> Optional[ClayCompanyData]:
        """Enrich company data using Clay API."""
        try:
            # Prepare search parameters
            search_params = {
                "name": company_name
            }
            
            if domain:
                search_params["domain"] = domain
            
            # Make API call to Clay
            response = await self.clay_client.post(
                "/companies/search",
                json=search_params
            )
            
            response.raise_for_status()
            clay_response = response.json()
            
            # Extract company data from Clay response
            if clay_response.get("companies"):
                company_data = clay_response["companies"][0]  # Take first result
                
                # Convert to ClayCompanyData model
                clay_data = ClayCompanyData(
                    clay_id=company_data.get("id"),
                    name=company_data.get("name"),
                    domain=company_data.get("domain"),
                    website=company_data.get("website"),
                    description=company_data.get("description"),
                    industry=company_data.get("industry"),
                    sub_industry=company_data.get("sub_industry"),
                    employee_count=company_data.get("employee_count"),
                    employee_count_range=company_data.get("employee_count_range"),
                    headquarters=company_data.get("headquarters"),
                    country=company_data.get("country"),
                    city=company_data.get("city"),
                    state=company_data.get("state"),
                    funding_total=company_data.get("funding_total"),
                    funding_rounds=company_data.get("funding_rounds"),
                    valuation=company_data.get("valuation"),
                    technologies=company_data.get("technologies", []),
                    tech_stack=company_data.get("tech_stack", {}),
                    linkedin_url=company_data.get("linkedin_url"),
                    twitter_url=company_data.get("twitter_url"),
                    facebook_url=company_data.get("facebook_url"),
                    email=company_data.get("email"),
                    phone=company_data.get("phone"),
                    clay_metadata={
                        "search_params": search_params,
                        "api_response": clay_response
                    }
                )
                
                return clay_data
            
            return None
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                self.logger.info(f"Company not found in Clay: {company_name}")
                return None
            else:
                self.logger.error(f"Clay API error: {e.response.status_code} - {e.response.text}")
                raise
        except Exception as e:
            self.logger.error(f"Clay enrichment error: {e}")
            raise
