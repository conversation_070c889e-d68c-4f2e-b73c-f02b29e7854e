"""
Data models for TractionX Data Pipeline Service.
"""

from .base import (
    BaseModel,
    TractionXModel,
    PipelineJobMetadata,
    PipelineStatus,
    EnrichmentSource,
    ProcessingResult
)

from .company import (
    CompanyData,
    CompanyEnrichmentData,
    ClayCompanyData
)

from .founder import (
    FounderData,
    FounderEnrichmentData,
    PDLFounderData
)

from .news import (
    NewsData,
    NewsArticle,
    BingNewsData
)

from .embedding import (
    EmbeddingData,
    TextEmbedding,
    EmbeddingMetadata
)

__all__ = [
    # Base models
    "BaseModel",
    "TractionXModel",
    "PipelineJobMetadata",
    "PipelineStatus", 
    "EnrichmentSource",
    "ProcessingResult",
    
    # Company models
    "CompanyData",
    "CompanyEnrichmentData",
    "ClayCompanyData",
    
    # Founder models
    "FounderData",
    "FounderEnrichmentData", 
    "PDLFounderData",
    
    # News models
    "NewsData",
    "NewsArticle",
    "BingNewsData",
    
    # Embedding models
    "EmbeddingData",
    "TextEmbedding",
    "EmbeddingMetadata"
]
