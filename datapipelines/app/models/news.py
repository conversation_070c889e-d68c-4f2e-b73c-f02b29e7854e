"""
News data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from pydantic import Field, HttpUrl

from .base import EnrichmentSource, TractionXModel


class NewsArticle(TractionXModel):
    """Individual news article data structure."""

    # Core identifiers
    article_id: str = Field(..., description="Unique article identifier")

    # Article content
    title: str = Field(..., description="Article title")
    description: Optional[str] = Field(None, description="Article description/summary")
    content: Optional[str] = Field(None, description="Full article content")
    url: HttpUrl = Field(..., description="Article URL")

    # Publication info
    source_name: str = Field(..., description="News source name")
    author: Optional[str] = Field(None, description="Article author")
    published_date: datetime = Field(..., description="Publication date")

    # Categorization
    category: Optional[str] = Field(None, description="Article category")
    tags: List[str] = Field(default_factory=list, description="Article tags")
    sentiment: Optional[str] = Field(
        None, description="Article sentiment (positive/negative/neutral)"
    )

    # Relevance
    relevance_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Relevance to company"
    )
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")

    # Metadata
    language: Optional[str] = Field(None, description="Article language")
    image_url: Optional[HttpUrl] = Field(None, description="Article image URL")

    # Additional data
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional article data"
    )


class BingNewsData(TractionXModel):
    """News data from Bing News API."""

    # Search metadata
    query: str = Field(..., description="Search query used")
    total_results: int = Field(..., description="Total number of results")

    # Articles
    articles: List[Dict[str, Any]] = Field(
        default_factory=list, description="Raw article data from Bing"
    )

    # API metadata
    search_date: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    api_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Bing API metadata"
    )

    def to_news_articles(self, company_id: str) -> List[NewsArticle]:
        """Convert Bing news data to NewsArticle format."""
        articles = []

        for i, article_data in enumerate(self.articles):
            try:
                # Generate article ID
                article_id = (
                    f"{company_id}_bing_{i}_{int(self.search_date.timestamp())}"
                )

                # Parse publication date
                published_date = datetime.now(timezone.utc)
                if "datePublished" in article_data:
                    try:
                        published_date = datetime.fromisoformat(
                            article_data["datePublished"].replace("Z", "+00:00")
                        )
                    except (ValueError, AttributeError):
                        pass

                # Extract provider name
                source_name = "Unknown"
                if "provider" in article_data and article_data["provider"]:
                    provider = (
                        article_data["provider"][0]
                        if isinstance(article_data["provider"], list)
                        else article_data["provider"]
                    )
                    source_name = provider.get("name", "Unknown")

                article = NewsArticle(
                    article_id=article_id,
                    title=article_data.get("name", ""),
                    description=article_data.get("description"),
                    url=article_data.get("url", ""),
                    source_name=source_name,
                    published_date=published_date,
                    category=article_data.get("category"),
                    image_url=article_data.get("image", {})
                    .get("thumbnail", {})
                    .get("contentUrl")
                    if article_data.get("image")
                    else None,
                    additional_data={
                        "bing_data": article_data,
                        "search_query": self.query,
                        "search_date": self.search_date.isoformat(),
                    },
                    content=None,
                    author=None,
                    sentiment=None,
                    relevance_score=None,
                    language=None,
                )
                articles.append(article)

            except Exception:
                # Skip invalid articles but log the error
                continue

        return articles


class NewsData(TractionXModel):
    """Aggregated news data for a company."""

    # Core identifiers
    company_id: str = Field(..., description="Associated company ID")
    org_id: str = Field(..., description="Organization ID")

    # News articles
    articles: List[NewsArticle] = Field(
        default_factory=list, description="News articles"
    )

    # Aggregation metadata
    search_queries: List[str] = Field(
        default_factory=list, description="Search queries used"
    )
    total_articles: int = Field(default=0, description="Total number of articles found")
    date_range: Dict[str, Optional[datetime]] = Field(
        default_factory=lambda: {"start": None, "end": None},
        description="Date range of articles",
    )

    # Analysis
    sentiment_summary: Dict[str, int] = Field(
        default_factory=lambda: {"positive": 0, "negative": 0, "neutral": 0},
        description="Sentiment analysis summary",
    )
    top_keywords: List[str] = Field(
        default_factory=list, description="Most frequent keywords"
    )

    # Processing metadata
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    source: EnrichmentSource = Field(
        default=EnrichmentSource.BING_NEWS, description="News source"
    )

    # Quality metrics
    relevance_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Overall relevance score"
    )

    def add_articles(self, new_articles: List[NewsArticle]) -> None:
        """Add new articles to the collection."""
        # Add articles and update metadata
        self.articles.extend(new_articles)
        self.total_articles = len(self.articles)

        # Update date range
        if self.articles:
            dates = [article.published_date for article in self.articles]
            self.date_range["start"] = min(dates)
            self.date_range["end"] = max(dates)

        # Update last_updated
        self.last_updated = datetime.now(timezone.utc)

    def get_recent_articles(self, days: int = 30) -> List[NewsArticle]:
        """Get articles from the last N days."""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        return [
            article
            for article in self.articles
            if article.published_date >= cutoff_date
        ]

    def get_articles_by_sentiment(self, sentiment: str) -> List[NewsArticle]:
        """Get articles with specific sentiment."""
        return [article for article in self.articles if article.sentiment == sentiment]


class NewsEnrichmentData(TractionXModel):
    """Enriched news data from multiple sources."""

    # Core identifiers
    company_id: str = Field(..., description="Associated company ID")
    org_id: str = Field(..., description="Organization ID")

    # News data from different sources
    bing_data: Optional[BingNewsData] = Field(None, description="Bing News data")

    # Aggregated news
    aggregated_news: Optional[NewsData] = Field(
        None, description="Aggregated news from all sources"
    )

    # Processing metadata
    enrichment_status: Dict[str, str] = Field(
        default_factory=dict, description="Status of each enrichment"
    )
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    def get_canonical_data(self) -> NewsData:
        """Get canonical news data from all sources."""
        if self.aggregated_news:
            return self.aggregated_news

        # Create new aggregated data
        canonical = NewsData(
            company_id=self.company_id,
            org_id=self.org_id,
            relevance_score=None,
        )

        # Add Bing articles
        if self.bing_data:
            bing_articles = self.bing_data.to_news_articles(self.company_id)
            canonical.add_articles(bing_articles)
            canonical.search_queries.append(self.bing_data.query)

        return canonical
