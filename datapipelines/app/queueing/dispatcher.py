"""
Queue Dispatcher

This module provides a simple interface for dispatching jobs to the appropriate queues
with automatic queue service management and job routing.
"""

from typing import Any, Dict, Optional

from app.configs import get_logger

from .factory import create_queue_service
from .interfaces import JobPriority

logger = get_logger(__name__)

# Global queue service instance
_queue_service = None


async def get_queue_service():
    """Get or create the global queue service instance."""
    global _queue_service

    if _queue_service is None:
        _queue_service = await create_queue_service()
        await _queue_service.initialize()
        logger.info("Global queue service initialized")

    return _queue_service


async def dispatch_job(
    job_func: str,
    job_args: Dict[str, Any],
    priority: str = "normal",
    job_id: Optional[str] = None,
    delay_seconds: int = 0,
    retry_config: Optional[Dict[str, Any]] = None,
) -> str:
    """
    Dispatch a job to the appropriate queue.

    Args:
        job_func: Function name to execute
        job_args: Arguments to pass to the function
        priority: Job priority ("high", "normal", "low")
        job_id: Optional custom job ID
        delay_seconds: Delay before job becomes available
        retry_config: Retry configuration

    Returns:
        Job ID
    """
    # Convert string priority to enum
    try:
        job_priority = JobPriority(priority.lower())
    except ValueError:
        logger.warning(f"Invalid priority '{priority}', using 'normal'")
        job_priority = JobPriority.NORMAL

    # Get queue service
    queue_service = await get_queue_service()

    # Dispatch the job
    job_id = await queue_service.enqueue_job(
        job_func=job_func,
        job_args=job_args,
        priority=job_priority,
        job_id=job_id,
        delay_seconds=delay_seconds,
        retry_config=retry_config,
    )

    logger.info(
        "Job dispatched successfully",
        job_id=job_id,
        job_func=job_func,
        priority=priority,
    )

    return job_id


async def get_job_status(job_id: str):
    """Get the status of a job by ID."""
    queue_service = await get_queue_service()
    return await queue_service.get_job_status(job_id)


async def cancel_job(job_id: str) -> bool:
    """Cancel a job by ID."""
    queue_service = await get_queue_service()
    return await queue_service.cancel_job(job_id)


async def retry_job(job_id: str, delay_seconds: int = 0) -> bool:
    """Retry a failed job."""
    queue_service = await get_queue_service()
    return await queue_service.retry_job(job_id, delay_seconds)


async def get_queue_stats(queue_name: Optional[str] = None):
    """Get queue statistics."""
    queue_service = await get_queue_service()
    return await queue_service.get_queue_stats(queue_name)


async def health_check():
    """Perform a health check on the queue system."""
    try:
        queue_service = await get_queue_service()
        return await queue_service.health_check()
    except Exception as e:
        logger.error(f"Queue health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "initialized": False,
        }


async def cleanup_queue_service():
    """Clean up the global queue service."""
    global _queue_service

    if _queue_service:
        await _queue_service.cleanup()
        _queue_service = None
        logger.info("Global queue service cleaned up")
