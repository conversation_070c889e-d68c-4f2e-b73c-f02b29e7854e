"""
TractionX Data Pipeline Service - Queue System

This package provides a modular, priority-aware queue system with support for:
- Multiple priority queues (high, normal, low)
- Round-robin job processing
- Pluggable backends (RQ, Celery, Kafka, etc.)
- Comprehensive monitoring and health checks
- Watchdog support for development
"""

from .dispatcher import dispatch_job, get_queue_service
from .factory import create_queue_service
from .interfaces import QueueBackend, QueueService, QueueStats

__all__ = [
    "QueueService",
    "QueueBackend",
    "QueueStats",
    "dispatch_job",
    "get_queue_service",
    "create_queue_service",
]
