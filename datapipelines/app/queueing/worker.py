"""
Round-Robin Queue Worker

This module provides a priority-aware worker that processes jobs from multiple queues
using round-robin scheduling with proper concurrency control and error handling.
"""

import asyncio
import importlib
import inspect
import signal
import time
from typing import Any, Callable, Dict, Optional, Set

from app.configs import get_logger
from app.tasks import get_job_handlers

from .factory import create_queue_service
from .interfaces import JobPriority

logger = get_logger(__name__)


async def get_queue_service():
    """Get or create a queue service instance."""
    return await create_queue_service()


def convert_datetime_to_iso(obj: Any) -> Any:
    """
    Recursively convert all datetime objects to ISO format strings.
    Handles nested dictionaries, lists, and other data structures.
    """
    from datetime import datetime

    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_datetime_to_iso(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime_to_iso(item) for item in obj]
    else:
        return obj


class RoundRobinWorker:
    """
    Round-robin worker for processing jobs from multiple priority queues.

    Features:
    - Priority-based round-robin scheduling
    - Configurable concurrency per queue
    - Graceful shutdown handling
    - Comprehensive error handling and retry logic
    - Job handler auto-discovery
    - Health monitoring and statistics
    """

    def __init__(
        self,
        worker_id: str = "worker-1",
        concurrency: int = 4,
        poll_interval: float = 1.0,
        shutdown_timeout: int = 120,
        max_job_processing_time: int = 3600,  # 1 hour
    ):
        """
        Initialize the round-robin worker.

        Args:
            worker_id: Unique identifier for this worker
            concurrency: Number of jobs to process concurrently
            poll_interval: How often to check for new jobs (seconds)
            shutdown_timeout: How long to wait for jobs to complete during shutdown
            max_job_processing_time: Maximum time allowed for job processing
        """
        self.worker_id = worker_id
        self.concurrency = concurrency
        self.poll_interval = poll_interval
        self.shutdown_timeout = shutdown_timeout
        self.max_job_processing_time = max_job_processing_time

        # Priority queues in order (high to low)
        self.queue_priorities = [
            JobPriority.HIGH.value,
            JobPriority.NORMAL.value,
            JobPriority.LOW.value,
        ]

        # Worker state
        self.running = False
        self.processing_tasks: Set[asyncio.Task] = set()
        self.queue_service = None
        self.handlers: Dict[str, Callable] = {}

        # Statistics
        self.stats = {
            "jobs_processed": 0,
            "jobs_failed": 0,
            "jobs_retried": 0,
            "start_time": None,
            "last_job_time": None,
        }

    async def initialize(self) -> None:
        """Initialize the worker."""
        # Get queue service
        self.queue_service = await get_queue_service()

        # Load job handlers
        self.handlers = get_job_handlers()

        logger.info(
            f"Worker {self.worker_id} initialized",
            worker_id=self.worker_id,
            concurrency=self.concurrency,
            handlers=list(self.handlers.keys()),
        )

    async def cleanup(self) -> None:
        """Clean up worker resources."""
        if self.running:
            await self.stop()

        logger.info(f"Worker {self.worker_id} cleaned up")

    async def start(self) -> None:
        """Start the worker."""
        if self.running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return

        if not self.queue_service:
            await self.initialize()

        self.running = True
        self.stats["start_time"] = time.time()

        logger.info(
            f"Starting worker {self.worker_id} with concurrency {self.concurrency}",
            worker_id=self.worker_id,
            concurrency=self.concurrency,
            queues=self.queue_priorities,
        )

        # Set up signal handlers for graceful shutdown
        loop = asyncio.get_running_loop()
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(
                sig, lambda s=sig: asyncio.create_task(self._handle_signal(s))
            )

        # Start worker tasks
        for i in range(self.concurrency):
            task = asyncio.create_task(self._worker_loop(f"{self.worker_id}-{i}"))
            self.processing_tasks.add(task)
            task.add_done_callback(self.processing_tasks.discard)

        logger.info(
            f"Worker {self.worker_id} started with {len(self.processing_tasks)} tasks"
        )

    async def stop(self) -> None:
        """Stop the worker gracefully."""
        if not self.running:
            logger.warning(f"Worker {self.worker_id} is not running")
            return

        logger.info(f"Stopping worker {self.worker_id}")
        self.running = False

        if not self.processing_tasks:
            return

        # Wait for tasks to complete with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*self.processing_tasks, return_exceptions=True),
                timeout=self.shutdown_timeout,
            )
            logger.info(f"Worker {self.worker_id} stopped gracefully")
        except asyncio.TimeoutError:
            logger.warning(
                f"Worker {self.worker_id} shutdown timed out, cancelling tasks"
            )
            for task in self.processing_tasks:
                task.cancel()
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)

    async def _handle_signal(self, sig: signal.Signals) -> None:
        """Handle termination signals."""
        logger.info(
            f"Worker {self.worker_id} received signal {sig.name}, shutting down..."
        )
        await self.stop()

    async def _worker_loop(self, task_id: str) -> None:
        """Main worker loop for processing jobs."""
        logger.info(f"Worker task {task_id} started")

        while self.running:
            try:
                # Try to get a job using round-robin priority
                if self.queue_service and hasattr(self.queue_service, "backend"):
                    job_data = await self.queue_service.backend.dequeue(
                        queue_names=self.queue_priorities,
                        timeout=int(self.poll_interval),
                    )

                    if job_data:
                        await self._process_job(job_data, task_id)
                    else:
                        # No jobs available, brief sleep before next poll
                        await asyncio.sleep(0.1)
                else:
                    logger.error("Queue service not properly initialized")
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info(f"Worker task {task_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker task {task_id} error: {e}", exc_info=True)
                await asyncio.sleep(1)  # Brief pause on error

        logger.info(f"Worker task {task_id} stopped")

    async def _process_job(self, job_data: Dict[str, Any], task_id: str) -> None:
        """Process a single job."""
        job_id = job_data.get("job_id", "unknown")
        job_func = job_data.get("job_func")
        job_args = job_data.get("job_args", {})

        logger.info(
            f"Processing job {job_id}",
            job_id=job_id,
            job_func=job_func,
            task_id=task_id,
        )

        start_time = time.time()

        try:
            # Find and execute the job handler
            handler = self._resolve_handler(job_func)

            if not handler:
                raise ValueError(f"No handler found for job function: {job_func}")

            # Execute the handler with timeout
            result = await asyncio.wait_for(
                self._execute_handler(handler, job_args),
                timeout=self.max_job_processing_time,
            )

            # Convert result to JSON-serializable format
            result = convert_datetime_to_iso(result)

            # Mark job as completed
            await self.queue_service.backend.complete_job(job_id, result)

            # Update statistics
            self.stats["jobs_processed"] += 1
            self.stats["last_job_time"] = time.time()

            processing_time = time.time() - start_time
            logger.info(
                f"Job {job_id} completed successfully",
                job_id=job_id,
                processing_time=processing_time,
                task_id=task_id,
            )

        except asyncio.TimeoutError:
            error_msg = f"Job {job_id} timed out after {self.max_job_processing_time}s"
            logger.error(error_msg)
            await self._fail_job(job_id, error_msg, retry=False)

        except Exception as e:
            error_msg = f"Job {job_id} failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self._fail_job(job_id, error_msg, retry=True)

    async def _execute_handler(
        self, handler: Callable, job_args: Dict[str, Any]
    ) -> Any:
        """Execute a job handler function."""
        if inspect.iscoroutinefunction(handler):
            # Async handler
            return await handler(job_args)
        else:
            # Sync handler - run in thread pool
            loop = asyncio.get_running_loop()
            return await loop.run_in_executor(None, handler, job_args)

    def _resolve_handler(self, job_func: str) -> Optional[Callable]:
        """Resolve a job function name to a callable handler."""
        # First, check registered handlers
        if job_func in self.handlers:
            return self.handlers[job_func]

        # Try to import the function dynamically
        try:
            if "." in job_func:
                module_path, func_name = job_func.rsplit(".", 1)
                module = importlib.import_module(module_path)
                return getattr(module, func_name)
            else:
                # Look for function in tasks module
                from app.tasks import company_enrichment, founder_enrichment

                for module in [company_enrichment, founder_enrichment]:
                    if hasattr(module, job_func):
                        return getattr(module, job_func)

                logger.warning(f"Handler not found for job function: {job_func}")
                return None

        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import job function {job_func}: {e}")
            return None

    async def _fail_job(
        self, job_id: str, error_message: str, retry: bool = True
    ) -> None:
        """Mark a job as failed."""
        try:
            await self.queue_service.backend.fail_job(job_id, error_message, retry)

            if retry:
                self.stats["jobs_retried"] += 1
            else:
                self.stats["jobs_failed"] += 1

        except Exception as e:
            logger.error(f"Failed to mark job {job_id} as failed: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get worker statistics."""
        uptime = None
        if self.stats["start_time"]:
            uptime = time.time() - self.stats["start_time"]

        return {
            "worker_id": self.worker_id,
            "running": self.running,
            "concurrency": self.concurrency,
            "active_tasks": len(self.processing_tasks),
            "uptime": uptime,
            "jobs_processed": self.stats["jobs_processed"],
            "jobs_failed": self.stats["jobs_failed"],
            "jobs_retried": self.stats["jobs_retried"],
            "last_job_time": self.stats["last_job_time"],
            "handlers_registered": len(self.handlers),
        }


async def start_worker(
    worker_id: str = "worker-1",
    concurrency: int = 4,
    poll_interval: float = 1.0,
) -> RoundRobinWorker:
    """
    Start a round-robin worker.

    Args:
        worker_id: Unique identifier for the worker
        concurrency: Number of jobs to process concurrently
        poll_interval: How often to check for new jobs

    Returns:
        Started worker instance
    """
    worker = RoundRobinWorker(
        worker_id=worker_id,
        concurrency=concurrency,
        poll_interval=poll_interval,
    )

    await worker.start()
    return worker
