"""
RQ Tasks for TractionX Data Pipeline Service.
"""

from typing import Callable, Dict

from .company_enrichment import enrich_company_data
from .embedding_generation import generate_embeddings
from .etl_merge import merge_enrichment_data
from .founder_enrichment import enrich_founder_data
from .news_aggregation import aggregate_news_data


def get_job_handlers() -> Dict[str, Callable]:
    """Get all available job handlers."""
    return {
        "enrich_company_data": enrich_company_data,
        "enrich_founder_data": enrich_founder_data,
        "aggregate_news_data": aggregate_news_data,
        "generate_embeddings": generate_embeddings,
        "merge_enrichment_data": merge_enrichment_data,
    }


# Export task functions for direct import
__all__ = [
    "enrich_company_data",
    "enrich_founder_data",
    "aggregate_news_data",
    "generate_embeddings",
    "merge_enrichment_data",
    "get_job_handlers",
]
