# Founder Data Schema Migration

This document describes the migration from the legacy founder data model to the new normalized schema that supports signals, scoring, and team analysis.

## Overview

We've refactored the founder data model from a single table with JSONB data to a normalized relational schema that enables:

- Clean relational access to founder experience, education, skills, and profiles
- Support for LLM-based scoring and tagging
- Team-level signal generation (e.g., complementarity analysis)
- Better query performance and data integrity

## Schema Changes

### Legacy Schema (Deprecated)

```sql
founders_legacy (
  id SERIAL PRIMARY KEY,
  founder_id VARCHAR(255) UNIQUE NOT NULL,
  company_id VARCHAR(255) NOT NULL,
  org_id VARCHAR(255) NOT NULL,
  name VARCHAR(500),
  data JSONB,  -- All enrichment data stored here
  source VARCHAR(100),
  s3_raw_data_key VARCHAR(1000),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### New Normalized Schema

#### 1. founders (Main Table)
```sql
founders (
  id UUID PRIMARY <PERSON>,
  founder_id VARCHAR(255) UNIQUE NOT NULL,
  full_name <PERSON>RC<PERSON>R(500),
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  current_job_title VARCHAR(255),
  current_job_company VARCHAR(255),
  linkedin_url TEXT,
  github_url TEXT,
  location_country VARCHAR(100),
  org_id VARCHAR(255) NOT NULL,
  company_id VARCHAR(255) NOT NULL,
  source VARCHAR(100),
  confidence_score FLOAT,
  enrichment_date TIMESTAMP,
  s3_raw_data_key TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 2. founder_experiences
```sql
founder_experiences (
  id UUID PRIMARY KEY,
  founder_id UUID REFERENCES founders(id) ON DELETE CASCADE,
  company_name VARCHAR(255),
  title VARCHAR(255),
  industry VARCHAR(255),
  company_size VARCHAR(50),
  start_date DATE,
  end_date DATE,
  is_primary BOOLEAN,
  location TEXT
)
```

#### 3. founder_education
```sql
founder_education (
  id UUID PRIMARY KEY,
  founder_id UUID REFERENCES founders(id) ON DELETE CASCADE,
  school_name VARCHAR(255),
  degrees TEXT[],
  majors TEXT[],
  start_date DATE,
  end_date DATE,
  location TEXT
)
```

#### 4. founder_skills
```sql
founder_skills (
  id UUID PRIMARY KEY,
  founder_id UUID REFERENCES founders(id) ON DELETE CASCADE,
  skill TEXT
)
```

#### 5. founder_profiles
```sql
founder_profiles (
  id UUID PRIMARY KEY,
  founder_id UUID REFERENCES founders(id) ON DELETE CASCADE,
  network VARCHAR(50),
  url TEXT
)
```

#### 6. founder_signals (LLM Analysis)
```sql
founder_signals (
  id UUID PRIMARY KEY,
  founder_id UUID REFERENCES founders(id) ON DELETE CASCADE,
  score INTEGER CHECK (score >= 0 AND score <= 100),
  tags TEXT[],
  strengths JSONB,
  risks JSONB,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 7. team_signals (Team Analysis)
```sql
team_signals (
  id UUID PRIMARY KEY,
  company_id VARCHAR(255),
  org_id VARCHAR(255),
  complementarity_score INTEGER CHECK (complementarity_score >= 0 AND complementarity_score <= 100),
  coverage JSONB,
  narrative TEXT,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## Migration Process

### 1. Automatic Migration

The migration process is handled by the `FounderDataMigrator` class:

```python
from app.utils.founder_migration import FounderDataMigrator
from app.storage.rds_storage import RDSStorage

# Initialize storage
rds_storage = RDSStorage()
await rds_storage.initialize()

# Run migration
migrator = FounderDataMigrator(rds_storage)
stats = await migrator.migrate_all_founders()
```

### 2. CLI Migration

Use the provided CLI script for easy migration:

```bash
# Check migration status
python scripts/founder_migration_cli.py status

# Run migration
python scripts/founder_migration_cli.py migrate --batch-size 100

# Generate founder signals
python scripts/founder_migration_cli.py signals --batch-size 50

# Generate team signals
python scripts/founder_migration_cli.py team-signals

# Run full pipeline
python scripts/founder_migration_cli.py full
```

### 3. Data Flow

```
PDL Data → S3 (Raw) → RDS (Normalized) → LLM Analysis → Signals
```

1. **PDL Enrichment**: Raw data stored in S3
2. **Normalization**: Data parsed and stored in normalized tables
3. **Signal Generation**: LLM analysis creates founder and team signals
4. **Backward Compatibility**: Legacy table maintained during transition

## Signal Generation

### Founder Signals

Individual founder analysis includes:

- **Score (0-100)**: Overall founder quality score
- **Tags**: Categorization (e.g., "technical", "business-oriented")
- **Strengths**: Identified positive attributes
- **Risks**: Potential concerns or gaps

### Team Signals

Team-level analysis includes:

- **Complementarity Score (0-100)**: How well the team works together
- **Coverage**: Skill and experience coverage analysis
- **Narrative**: Human-readable team assessment

## Usage Examples

### Query Founder with Relations

```python
# Get complete founder profile
founder_data = await rds_storage.get_founder_with_relations(founder_id)

# Access structured data
experiences = founder_data["experiences"]
education = founder_data["education"]
skills = founder_data["skills"]
signals = founder_data["signals"]
```

### Insert New Founder

```python
# Create founder record
founder_record = FounderRecord(
    founder_id="unique_id",
    full_name="John Doe",
    company_id="company_123",
    org_id="org_456",
    # ... other fields
)

# Insert with relations
await rds_storage.insert_founder_with_relations(
    founder_data=founder_record.model_dump(),
    experiences=[exp.model_dump() for exp in experiences],
    education=[edu.model_dump() for edu in education],
    skills=["python", "leadership"],
    profiles=[profile.model_dump() for profile in profiles]
)
```

### Generate Signals

```python
from app.utils.founder_signals import FounderSignalGenerator

generator = FounderSignalGenerator(rds_storage)

# Generate individual founder signals
signal = await generator.generate_founder_signals(founder_id)

# Generate team signals
team_signal = await generator.generate_team_signals(company_id, org_id)
```

## Performance Considerations

### Indexes

The migration creates optimized indexes for common query patterns:

- `founders(founder_id, org_id, company_id)`
- `founder_experiences(founder_id, is_primary)`
- `founder_signals(founder_id, score)`
- `team_signals(company_id, org_id)`

### Query Optimization

- Use `get_founder_with_relations()` for complete profiles
- Filter by `org_id` and `company_id` for tenant isolation
- Use `is_primary` flag for current job experience

## Backward Compatibility

During the transition period:

1. **Dual Write**: New enrichments write to both schemas
2. **Legacy Table**: `founders_legacy` maintains old format
3. **Gradual Migration**: Existing data migrated in batches
4. **API Compatibility**: Existing APIs continue to work

## Validation

After migration, validate:

- [ ] All founders migrated successfully
- [ ] Foreign key relationships intact
- [ ] No duplicate entries
- [ ] Signals generated for active founders
- [ ] Team signals created for companies
- [ ] Performance meets requirements

## Rollback Plan

If issues arise:

1. **Stop New Writes**: Disable new schema writes
2. **Revert Code**: Switch back to legacy schema
3. **Data Cleanup**: Remove incomplete migrations
4. **Investigate**: Analyze and fix issues
5. **Retry**: Re-run migration with fixes

## Monitoring

Monitor migration progress:

- Migration batch completion rates
- Error rates and types
- Signal generation success rates
- Query performance metrics
- Data consistency checks
