# Use official Python slim base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry - use pip for a more reliable installation
RUN pip install --no-cache-dir "poetry==1.7.1" && \
    poetry --version

# Copy pyproject.toml and poetry.lock first for better layer caching
COPY pyproject.toml poetry.lock* ./

# Install dependencies globally (no virtual environment)
RUN pip install --upgrade pip && \
    poetry config virtualenvs.create false && \
    poetry install --no-root --no-interaction --no-ansi

# Copy application code
COPY . .

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]