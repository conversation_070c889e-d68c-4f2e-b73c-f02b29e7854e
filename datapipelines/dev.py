#!/usr/bin/env python3
"""
Development script for TractionX Data Pipeline Service.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional


def run_command(cmd: str, cwd: Optional[str] = None) -> int:
    """Run a shell command and return the exit code."""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd)
    return result.returncode


def setup_environment():
    """Set up the development environment."""
    print("Setting up development environment...")

    # Copy .env.example to .env if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            run_command("cp .env.example .env")
            print("Created .env file from .env.example")
            print("Please edit .env file with your configuration")
        else:
            print("Warning: .env.example not found")

    # Install dependencies
    print("Installing dependencies...")
    if run_command("poetry install") != 0:
        print("Failed to install dependencies")
        return False

    print("Development environment setup complete!")
    return True


def start_api():
    """Start the API server with hot reload."""
    print("Starting API server with hot reload...")
    cmd = "poetry run uvicorn main:app --host 0.0.0.0 --port 8001 --reload"
    return run_command(cmd)


def start_worker():
    """Start the worker with file watching."""
    print("Starting worker with file watching...")
    os.environ["WATCHDOG_ENABLED"] = "1"
    cmd = "poetry run python worker.py --watch"
    return run_command(cmd)


def start_all():
    """Start all services using docker-compose."""
    print("Starting all services with docker-compose...")
    return run_command("docker-compose up")


def test():
    """Run tests."""
    print("Running tests...")
    return run_command("poetry run pytest -v")


def test_coverage():
    """Run tests with coverage."""
    print("Running tests with coverage...")
    return run_command("poetry run pytest --cov=datapipelines --cov-report=html")


def lint():
    """Run linting and formatting."""
    print("Running linting and formatting...")
    commands = ["poetry run black .", "poetry run isort .", "poetry run mypy ."]

    for cmd in commands:
        if run_command(cmd) != 0:
            print(f"Command failed: {cmd}")
            return 1

    print("Linting complete!")
    return 0


def clean():
    """Clean up generated files."""
    print("Cleaning up...")
    commands = [
        "find . -type f -name '*.pyc' -delete",
        "find . -type d -name '__pycache__' -delete",
        "find . -type d -name '*.egg-info' -exec rm -rf {} +",
        "find . -type d -name '.pytest_cache' -exec rm -rf {} +",
        "find . -type d -name '.mypy_cache' -exec rm -rf {} +",
        "rm -rf htmlcov/",
    ]

    for cmd in commands:
        run_command(cmd)

    print("Cleanup complete!")


def docker_build():
    """Build Docker images."""
    print("Building Docker images...")
    return run_command("docker-compose build")


def docker_up():
    """Start services with docker-compose."""
    print("Starting services with docker-compose...")
    return run_command("docker-compose up -d")


def docker_down():
    """Stop services with docker-compose."""
    print("Stopping services with docker-compose...")
    return run_command("docker-compose down")


def docker_logs():
    """Show docker-compose logs."""
    print("Showing docker-compose logs...")
    return run_command("docker-compose logs -f")


def health_check():
    """Check service health."""
    print("Checking service health...")
    return run_command("curl -f http://localhost:8001/health")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="TractionX Data Pipeline Development Script"
    )
    parser.add_argument(
        "command",
        choices=[
            "setup",
            "api",
            "worker",
            "all",
            "test",
            "test-cov",
            "lint",
            "clean",
            "docker-build",
            "docker-up",
            "docker-down",
            "docker-logs",
            "health",
        ],
        help="Command to run",
    )

    args = parser.parse_args()

    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)

    commands = {
        "setup": setup_environment,
        "api": start_api,
        "worker": start_worker,
        "all": start_all,
        "test": test,
        "test-cov": test_coverage,
        "lint": lint,
        "clean": clean,
        "docker-build": docker_build,
        "docker-up": docker_up,
        "docker-down": docker_down,
        "docker-logs": docker_logs,
        "health": health_check,
    }

    command_func = commands.get(args.command)
    if command_func:
        exit_code = command_func()
        sys.exit(exit_code if exit_code is not None else 0)
    else:
        print(f"Unknown command: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
