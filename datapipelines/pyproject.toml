[tool.poetry]
name = "tractionx-datapipelines"
version = "0.1.0"
description = "TractionX Data Pipeline Service"
authors = ["TractionX Team <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11,<3.14"
fastapi = "^0.110.0"
uvicorn = {extras = ["standard"], version = "^0.29.0"}
rq = "^1.16.0"
pydantic = "^2.7.0"
pydantic-settings = "^2.1.0"
redis = "^5.0.0"
httpx = "^0.26.0"
aiohttp = "^3.9.3"
requests = "^2.31.0"
qdrant-client = "^1.14.3"
boto3 = "^1.38.41"
pymongo="^4.13.2"
motor = "^3.3.2"
python-dateutil = "^2.8.2"
openai = "^1.12.0"
python-multipart = "^0.0.6"
structlog = "^24.1.0"
tenacity = "^8.2.3"
watchdog = "^4.0.0"
watchfiles = "^0.21.0"
python-dotenv = "^1.0.1"
# Essential database dependencies
asyncpg = "^0.29.0"
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.4"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^24.1.1"
isort = "^5.13.2"
flake8 = "^7.0.0"
mypy = "^1.8.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120
known_first_party = ["datapipelines"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["datapipelines"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
