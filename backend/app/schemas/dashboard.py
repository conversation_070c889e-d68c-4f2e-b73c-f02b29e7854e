"""
Dashboard API Schemas

Pydantic models for dashboard API requests and responses.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, ConfigDict


class SectorDistribution(BaseModel):
    """Schema for sector distribution data."""
    sector: str
    count: int


class DealStage(BaseModel):
    """Schema for deal stage data."""
    stage: str
    count: int


class AIActivity(BaseModel):
    """Schema for AI activity status."""
    active: bool
    last_sync: Optional[str] = None


class OnboardingStatus(BaseModel):
    """Schema for onboarding status."""
    has_form: bool
    has_thesis: bool


class DashboardSummaryResponse(BaseModel):
    """Schema for unified dashboard summary response."""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "active_deals": 3,
                "active_deals_change_pct": 12,
                "forms": 2,
                "theses": 1,
                "ai_activity": {
                    "active": True,
                    "last_sync": "2025-06-10T14:00:00Z"
                },
                "sector_distribution": [
                    {"sector": "FinTech", "count": 1},
                    {"sector": "AI/ML", "count": 1},
                    {"sector": "SaaS", "count": 1}
                ],
                "deal_stages": [
                    {"stage": "Pre-Seed", "count": 2},
                    {"stage": "Seed", "count": 1},
                    {"stage": "Series A", "count": 0},
                    {"stage": "Series B", "count": 0}
                ],
                "onboarding": {
                    "has_form": True,
                    "has_thesis": False
                }
            }
        }
    )
    
    active_deals: int
    active_deals_change_pct: int
    forms: int
    theses: int
    ai_activity: AIActivity
    sector_distribution: List[SectorDistribution]
    deal_stages: List[DealStage]
    onboarding: OnboardingStatus
