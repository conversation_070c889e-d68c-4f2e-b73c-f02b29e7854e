"""
RDS (PostgreSQL) storage implementation for TractionX Backend Service.

Provides a generic, schema-agnostic access layer for PostgreSQL operations
with support for CRUD operations, custom queries, and robust error handling.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

import asyncpg  # type: ignore
from app.core.config import settings
from app.core.logging import get_logger
from app.models.base import BaseModel
from asyncpg import Pool  # type: ignore

logger = get_logger(__name__)


class RDSStorageError(Exception):
    """Base exception for RDS storage operations."""

    pass


class RDSConnectionError(RDSStorageError):
    """Raised when database connection fails."""

    pass


class RDSQueryError(RDSStorageError):
    """Raised when database query fails."""

    pass


class RDSStorage:
    """
    Generic PostgreSQL storage implementation.

    Provides schema-agnostic CRUD operations and custom query support
    with robust error handling, logging, and performance monitoring.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.pool: Optional[Pool] = None
        self.connection_string = settings.DB_HOST
        self._table_schemas: Dict[str, Dict[str, str]] = {}

    async def initialize(self) -> None:
        """Initialize the database connection pool and core tables."""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=settings.DB_POOL_SIZE,
                max_inactive_connection_lifetime=300,
            )
            self.logger.info("RDS storage initialized successfully")

            # Enable UUID extension
            await self._enable_uuid_extension()

            # Create core tables if they don't exist
            await self._create_core_tables()

        except Exception as e:
            self.logger.error(f"Failed to initialize RDS storage: {e}")
            raise RDSConnectionError(f"Database initialization failed: {e}")

    async def cleanup(self) -> None:
        """Clean up database resources."""
        if self.pool:
            await self.pool.close()
            self.logger.info("RDS storage cleaned up")

    async def health_check(self) -> bool:
        """Check if database is healthy."""
        try:
            if not self.pool:
                return False

            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True

        except Exception as e:
            self.logger.error(f"RDS health check failed: {e}")
            return False

    def register_table_schema(self, table_name: str, schema: Dict[str, str]) -> None:
        """Register a table schema for validation and operations."""
        self._table_schemas[table_name] = schema
        self.logger.debug(f"Registered schema for table: {table_name}")

    async def create_table(self, table_name: str, schema: Dict[str, str]) -> bool:
        """Create a table with the given schema."""
        try:
            # Convert schema to SQL DDL
            columns = []
            for column_name, column_def in schema.items():
                columns.append(f"{column_name} {column_def}")

            ddl = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                await conn.execute(ddl)

            # Register the schema
            self.register_table_schema(table_name, schema)

            self.logger.info(f"Created table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create table {table_name}: {e}")
            return False

    async def insert(
        self,
        table_name: str,
        data: Union[Dict[str, Any], BaseModel],
        return_id: bool = True,
    ) -> Union[str, bool]:
        """
        Insert data and return the ID or success status.

        Args:
            table_name: Name of the table to insert into
            data: Data to insert (dict or Pydantic model)
            return_id: Whether to return the inserted ID or just success status

        Returns:
            Inserted record ID (str) or success status (bool)
        """
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add timestamps if not present
            now = datetime.now(timezone.utc)
            if "created_at" not in data_dict:
                data_dict["created_at"] = now
            if "updated_at" not in data_dict:
                data_dict["updated_at"] = now

            # Generate UUID if not present and table uses UUID primary key
            if "id" not in data_dict and self._uses_uuid_primary_key(table_name):
                data_dict["id"] = str(uuid4())

            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]

            if return_id:
                sql = f"""
                    INSERT INTO {table_name} ({", ".join(columns)})
                    VALUES ({", ".join(placeholders)})
                    RETURNING id
                """
            else:
                sql = f"""
                    INSERT INTO {table_name} ({", ".join(columns)})
                    VALUES ({", ".join(placeholders)})
                """

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                if return_id:
                    record_id = await conn.fetchval(sql, *values)
                    self.logger.debug(
                        f"Inserted record into {table_name} with ID {record_id}"
                    )
                    return str(record_id)
                else:
                    await conn.execute(sql, *values)
                    self.logger.debug(f"Inserted record into {table_name}")
                    return True

        except Exception as e:
            self.logger.error(f"Failed to insert into {table_name}: {e}")
            raise RDSQueryError(f"Insert operation failed: {e}")

    async def update(
        self, table_name: str, id: str, data: Union[Dict[str, Any], BaseModel]
    ) -> bool:
        """
        Update data by ID.

        Args:
            table_name: Name of the table to update
            id: Record ID to update
            data: Data to update (dict or Pydantic model)

        Returns:
            Success status
        """
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add updated timestamp
            data_dict["updated_at"] = datetime.now(timezone.utc)

            # Prepare SQL
            set_clauses = []
            values = []
            for i, (column, value) in enumerate(data_dict.items()):
                set_clauses.append(f"{column} = ${i + 1}")
                values.append(self._serialize_value(value))

            values.append(id)  # For WHERE clause

            sql = f"""
                UPDATE {table_name}
                SET {", ".join(set_clauses)}
                WHERE id = ${len(values)}
            """

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, *values)

            # Check if any rows were updated
            rows_updated = int(result.split()[-1])
            success = rows_updated > 0

            if success:
                self.logger.debug(f"Updated record in {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to update in {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to update {table_name} record {id}: {e}")
            raise RDSQueryError(f"Update operation failed: {e}")

    async def upsert(
        self,
        table_name: str,
        data: Union[Dict[str, Any], BaseModel],
        key_fields: List[str],
    ) -> str:
        """
        Insert or update data based on key fields.

        Args:
            table_name: Name of the table to upsert into
            data: Data to upsert (dict or Pydantic model)
            key_fields: Fields to use for conflict resolution

        Returns:
            Record ID (inserted or updated)
        """
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add timestamps
            now = datetime.now(timezone.utc)
            if "created_at" not in data_dict:
                data_dict["created_at"] = now
            data_dict["updated_at"] = now

            # Generate UUID if not present and table uses UUID primary key
            if "id" not in data_dict and self._uses_uuid_primary_key(table_name):
                data_dict["id"] = str(uuid4())

            # Build conflict resolution
            conflict_columns = ", ".join(key_fields)
            update_clauses = []
            for column in data_dict.keys():
                if column not in key_fields:
                    update_clauses.append(f"{column} = EXCLUDED.{column}")

            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]

            sql = f"""
                INSERT INTO {table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                ON CONFLICT ({conflict_columns})
                DO UPDATE SET {", ".join(update_clauses)}
                RETURNING id
            """

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                record_id = await conn.fetchval(sql, *values)

            self.logger.debug(f"Upserted record in {table_name} with ID {record_id}")
            return str(record_id)

        except Exception as e:
            self.logger.error(f"Failed to upsert into {table_name}: {e}")
            raise RDSQueryError(f"Upsert operation failed: {e}")

    async def get_by_id(self, table_name: str, id: str) -> Optional[Dict[str, Any]]:
        """
        Get data by ID.

        Args:
            table_name: Name of the table to query
            id: Record ID to fetch

        Returns:
            Record data or None if not found
        """
        try:
            sql = f"SELECT * FROM {table_name} WHERE id = $1"

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                record = await conn.fetchrow(sql, id)

            if record:
                return dict(record)
            return None

        except Exception as e:
            self.logger.error(
                f"Failed to get record from {table_name} with ID {id}: {e}"
            )
            raise RDSQueryError(f"Get by ID operation failed: {e}")

    async def get_by_fields(
        self,
        table_name: str,
        filters: Dict[str, Any],
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get data by field filters with optional pagination and ordering.

        Args:
            table_name: Name of the table to query
            filters: Field filters to apply
            limit: Maximum number of records to return
            offset: Number of records to skip
            order_by: ORDER BY clause (e.g., "created_at DESC")

        Returns:
            List of matching records
        """
        try:
            # Build WHERE clause
            where_clauses = []
            values = []
            for i, (column, value) in enumerate(filters.items()):
                where_clauses.append(f"{column} = ${i + 1}")
                values.append(self._serialize_value(value))

            where_clause = " AND ".join(where_clauses) if where_clauses else "TRUE"
            sql = f"SELECT * FROM {table_name} WHERE {where_clause}"

            # Add ordering
            if order_by:
                sql += f" ORDER BY {order_by}"

            # Add pagination
            if limit:
                sql += f" LIMIT {limit}"
            if offset:
                sql += f" OFFSET {offset}"

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                records = await conn.fetch(sql, *values)

            return [dict(record) for record in records]

        except Exception as e:
            self.logger.error(f"Failed to get records from {table_name}: {e}")
            raise RDSQueryError(f"Get by fields operation failed: {e}")

    async def delete(self, table_name: str, id: str) -> bool:
        """
        Delete data by ID.

        Args:
            table_name: Name of the table to delete from
            id: Record ID to delete

        Returns:
            Success status
        """
        try:
            sql = f"DELETE FROM {table_name} WHERE id = $1"

            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, id)

            rows_deleted = int(result.split()[-1])
            success = rows_deleted > 0

            if success:
                self.logger.debug(f"Deleted record from {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to delete from {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to delete from {table_name} record {id}: {e}")
            raise RDSQueryError(f"Delete operation failed: {e}")

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a custom query with parameter binding.

        Args:
            query: SQL query to execute
            params: Query parameters (optional)

        Returns:
            List of result records
        """
        try:
            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                if params:
                    records = await conn.fetch(query, *params.values())
                else:
                    records = await conn.fetch(query)

            return [dict(record) for record in records]

        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            raise RDSQueryError(f"Custom query execution failed: {e}")

    async def insert_many(
        self, table_name: str, records: List[Union[Dict[str, Any], BaseModel]]
    ) -> List[str]:
        """
        Insert multiple records in a single transaction.

        Args:
            table_name: Name of the table to insert into
            records: List of records to insert

        Returns:
            List of inserted record IDs
        """
        if not self.pool:
            raise RDSConnectionError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                inserted_ids = []

                for record in records:
                    record_id = await self._insert_single_record(
                        conn, table_name, record
                    )
                    inserted_ids.append(record_id)

                self.logger.info(
                    f"Inserted {len(inserted_ids)} records into {table_name}"
                )
                return inserted_ids

    async def upsert_many(
        self,
        table_name: str,
        records: List[Union[Dict[str, Any], BaseModel]],
        key_fields: List[str],
    ) -> List[str]:
        """
        Upsert multiple records in a single transaction.

        Args:
            table_name: Name of the table to upsert into
            records: List of records to upsert
            key_fields: Fields to use for conflict resolution

        Returns:
            List of record IDs (inserted or updated)
        """
        if not self.pool:
            raise RDSConnectionError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                record_ids = []

                for record in records:
                    record_id = await self._upsert_single_record(
                        conn, table_name, record, key_fields
                    )
                    record_ids.append(record_id)

                self.logger.info(f"Upserted {len(record_ids)} records in {table_name}")
                return record_ids

    async def _insert_single_record(
        self, conn, table_name: str, data: Union[Dict[str, Any], BaseModel]
    ) -> str:
        """Helper method to insert a single record within a transaction."""
        # Convert BaseModel to dict if needed
        if isinstance(data, BaseModel):
            data_dict = data.model_dump()
        else:
            data_dict = data.copy()

        # Add timestamps
        now = datetime.now(timezone.utc)
        if "created_at" not in data_dict:
            data_dict["created_at"] = now
        if "updated_at" not in data_dict:
            data_dict["updated_at"] = now

        # Generate UUID if needed
        if "id" not in data_dict and self._uses_uuid_primary_key(table_name):
            data_dict["id"] = str(uuid4())

        # Prepare SQL
        columns = list(data_dict.keys())
        placeholders = [f"${i + 1}" for i in range(len(columns))]
        values = [self._serialize_value(data_dict[col]) for col in columns]

        sql = f"""
            INSERT INTO {table_name} ({", ".join(columns)})
            VALUES ({", ".join(placeholders)})
            RETURNING id
        """

        record_id = await conn.fetchval(sql, *values)
        return str(record_id)

    async def _upsert_single_record(
        self,
        conn,
        table_name: str,
        data: Union[Dict[str, Any], BaseModel],
        key_fields: List[str],
    ) -> str:
        """Helper method to upsert a single record within a transaction."""
        # Convert BaseModel to dict if needed
        if isinstance(data, BaseModel):
            data_dict = data.model_dump()
        else:
            data_dict = data.copy()

        # Add timestamps
        now = datetime.now(timezone.utc)
        if "created_at" not in data_dict:
            data_dict["created_at"] = now
        data_dict["updated_at"] = now

        # Generate UUID if needed
        if "id" not in data_dict and self._uses_uuid_primary_key(table_name):
            data_dict["id"] = str(uuid4())

        # Build conflict resolution
        conflict_columns = ", ".join(key_fields)
        update_clauses = []
        for column in data_dict.keys():
            if column not in key_fields:
                update_clauses.append(f"{column} = EXCLUDED.{column}")

        # Prepare SQL
        columns = list(data_dict.keys())
        placeholders = [f"${i + 1}" for i in range(len(columns))]
        values = [self._serialize_value(data_dict[col]) for col in columns]

        sql = f"""
            INSERT INTO {table_name} ({", ".join(columns)})
            VALUES ({", ".join(placeholders)})
            ON CONFLICT ({conflict_columns})
            DO UPDATE SET {", ".join(update_clauses)}
            RETURNING id
        """

        record_id = await conn.fetchval(sql, *values)
        return str(record_id)

    def _serialize_value(self, value: Any) -> Any:
        """Serialize value for database storage."""
        if value is None:
            return None
        elif isinstance(value, dict):
            return json.dumps(value, default=self._json_serializer)
        elif isinstance(value, list):
            # For PostgreSQL arrays, return the list as-is
            # For JSON fields, serialize to JSON string
            return value
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            # Try to parse ISO datetime strings back to datetime objects
            try:
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                # Not a datetime string, return as-is
                return value
        else:
            return value

    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for complex objects."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, "model_dump"):  # Pydantic models
            return obj.model_dump()
        elif hasattr(obj, "__dict__"):
            return obj.__dict__
        else:
            raise TypeError(
                f"Object of type {type(obj).__name__} is not JSON serializable"
            )

    def _uses_uuid_primary_key(self, table_name: str) -> bool:
        """Check if table uses UUID primary key."""
        schema = self._table_schemas.get(table_name, {})
        id_definition = schema.get("id", "")
        return (
            "UUID" in id_definition.upper() and "PRIMARY KEY" in id_definition.upper()
        )

    async def _enable_uuid_extension(self) -> None:
        """Enable the UUID extension in PostgreSQL."""
        try:
            if not self.pool:
                raise RDSConnectionError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                await conn.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
                self.logger.info("UUID extension enabled")
        except Exception as e:
            self.logger.error(f"Failed to enable UUID extension: {e}")
            raise

    async def _create_core_tables(self) -> None:
        """Create core tables for the application."""
        # This will be implemented based on the specific application needs
        # Tables will be created using the schema registry
        pass
