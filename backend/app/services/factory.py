"""
Factory for creating and managing services.

This module provides functions to create and manage service instances.
"""

from typing import Dict, Optional, Type

from fastapi import Depends, FastAPI
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.services.base import BaseService


class ServiceFactory:
    """Factory for creating and managing services."""

    _instances: Dict[Type[BaseService], BaseService] = {}
    _app: Optional[FastAPI] = None

    @classmethod
    def set_app(cls, app: FastAPI) -> None:
        """Set the FastAPI application instance."""
        cls._app = app

    @classmethod
    async def get_service(
        cls, service_class: Type[BaseService], db: Optional[AsyncIOMotorDatabase] = None
    ) -> BaseService:
        """Get or create a service instance."""
        if service_class not in cls._instances:
            # Handle different service initialization requirements
            if (
                service_class.__name__ == "RBACService"
                or service_class.__name__ == "RoleService"
            ):
                instance = service_class(db=db, app=cls._app)
            elif service_class.__name__ == "RedisQueueService":
                # Queue service doesn't need db
                instance = service_class()
            else:
                instance = service_class(db=db) if db is not None else service_class()
            await instance.initialize()
            cls._instances[service_class] = instance
        return cls._instances[service_class]

    @classmethod
    async def cleanup(cls) -> None:
        """Cleanup all service instances."""
        for instance in cls._instances.values():
            await instance.cleanup()
        cls._instances.clear()


# FastAPI dependency functions
async def get_auth_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Auth service instance."""
    from app.services.auth.service import AuthService

    return await ServiceFactory.get_service(AuthService, db)


async def get_rbac_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get RBAC service instance."""
    from app.services.rbac.mongo import RBACService

    return await ServiceFactory.get_service(RBACService, db)


async def get_user_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get User service instance."""
    from app.services.user.mongo import UserService

    return await ServiceFactory.get_service(UserService, db)


async def get_public_user_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Public User service instance."""
    from app.services.user.mongo import PublicUserService

    return await ServiceFactory.get_service(PublicUserService, db)


async def get_audit_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Audit service instance."""
    from app.services.audit.mongo import AuditService

    return await ServiceFactory.get_service(AuditService, db)


async def get_form_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Form service instance."""
    from app.services.form.mongo import FormService

    return await ServiceFactory.get_service(FormService, db)


async def get_cache_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Cache service instance."""
    from app.services.cache.redis import RedisService

    return await ServiceFactory.get_service(RedisService, db)


async def get_role_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Role service instance."""
    from app.services.role.mongo import RoleService

    return await ServiceFactory.get_service(RoleService, db)


async def get_redis_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Redis service instance."""
    from app.services.cache.redis import RedisService

    return await ServiceFactory.get_service(RedisService, db)


async def get_token_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Token service instance."""
    from app.services.token.service import TokenService

    return await ServiceFactory.get_service(TokenService, db)


async def get_queue_service():
    """Get Queue service instance."""
    from app.services.queue.redis_queue import RedisQueueService

    return await ServiceFactory.get_service(RedisQueueService)


async def get_sharing_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Sharing service instance."""
    from app.services.sharing.service import SharingService

    return await ServiceFactory.get_service(SharingService, db)


async def get_trigger_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Trigger service instance."""
    from app.services.trigger.service import TriggerService

    return await ServiceFactory.get_service(TriggerService, db)


async def get_qualifier_form_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Qualifier Form service instance."""
    from app.services.qualifier_form.service import QualifierFormService

    return await ServiceFactory.get_service(QualifierFormService, db)


async def get_thesis_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Thesis service instance."""
    from app.services.thesis.mongo import ThesisService

    return await ServiceFactory.get_service(ThesisService, db)


async def get_exclusion_filter_service(
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Get Exclusion Filter service instance."""
    from app.services.exclusion_filter.service import ExclusionFilterService

    return await ServiceFactory.get_service(ExclusionFilterService, db)


async def get_job_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Job Tracking service instance."""
    from app.services.job.mongo import JobService

    return await ServiceFactory.get_service(JobService, db)


async def get_deal_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Deal service instance."""
    from app.services.deal.mongo import DealService

    return await ServiceFactory.get_service(DealService, db)


async def get_public_submission_service(
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Get Public Submission service instance."""
    from app.services.public_submission.service import PublicSubmissionService

    return await ServiceFactory.get_service(PublicSubmissionService, db)


async def get_file_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get File service instance."""
    from app.services.file.service import FileService

    return await ServiceFactory.get_service(FileService, db)


async def get_submission_processing_service(
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Get Submission Processing service instance."""
    from app.services.submission_processing.service import SubmissionProcessingService

    return await ServiceFactory.get_service(SubmissionProcessingService, db)


async def get_chat_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Chat service instance."""
    from app.services.chat.service import ChatService

    service = await ServiceFactory.get_service(ChatService, db)
    return service  # type: IChatService


async def get_deal_document_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Deal Document service instance."""
    from app.services.deal_document.service import DealDocumentService

    return await ServiceFactory.get_service(DealDocumentService, db)


async def get_onboarding_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Onboarding service instance."""
    from app.services.onboarding.service import OnboardingService

    # Get dependencies
    auth_service = await get_auth_service(db)
    email_service = await get_email_service()

    # Create onboarding service with dependencies
    return OnboardingService(auth_service, email_service)  # type: ignore


async def get_email_service():
    """Get Email service instance."""
    from app.services.email.service import EmailService

    return EmailService()


async def get_context_block_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Context Block service instance."""
    from app.services.context_block.service import ContextBlockService

    return await ServiceFactory.get_service(ContextBlockService, db)


async def get_research_service():
    """Get Research service instance."""
    from app.services.research.service import ResearchService

    service = await ServiceFactory.get_service(ResearchService)
    return service  # type: IResearchService


async def get_demo_deal_service():
    """Get Demo Deal service instance."""
    from app.services.deal.demo import DemoDealService

    return await ServiceFactory.get_service(DemoDealService)
