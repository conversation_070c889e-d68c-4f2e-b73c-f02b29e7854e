"""
Pitch Parser Service Interface

Defines the interface for LLM-based pitch parsing services.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class PitchParserInterface(ABC):
    """Interface for pitch parsing services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the pitch parser service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    @abstractmethod
    async def parse_pitch_content(
        self,
        text_content: str,
        pitch_type: str,
        pages: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Parse pitch content using LLM to extract structured data.

        Args:
            text_content: Extracted text from the pitch
            pitch_type: "deck" or "one_pager"
            pages: List of page data with text and page numbers
            metadata: Optional metadata about the document

        Returns:
            Dictionary containing:
            - company_name: Extracted company name
            - tagline: Company tagline/description
            - ask_amount: Funding ask amount
            - team_summary: Summary of the team
            - traction: Traction/metrics information
            - market_size: Market size information
            - sector: List of sectors/industries
            - source_page_map: Mapping of fields to page numbers
            - confidence_score: Overall parsing confidence (0-1)
        """
        pass

    @abstractmethod
    async def generate_markdown_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """
        Generate a clean markdown summary for investor view.

        Args:
            parsed_data: Structured data from parsing
            text_content: Original text content
            pitch_type: "deck" or "one_pager"

        Returns:
            Markdown formatted summary
        """
        pass

    @abstractmethod
    async def generate_short_description(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """
        Generate a concise short description (1-2 sentences) for the deal.

        Args:
            parsed_data: Structured data from parsing
            text_content: Original text content
            pitch_type: "deck" or "one_pager"

        Returns:
            Concise description suitable for deal listings
        """
        pass

    @abstractmethod
    async def generate_executive_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """
        Generate a comprehensive executive summary in Sequoia style.

        Args:
            parsed_data: Structured data from parsing
            text_content: Original text content
            pitch_type: "deck" or "one_pager"

        Returns:
            Professional executive summary with investment analysis
        """
        pass
