"""
MongoDB Implementation of Deal Service

This module implements the DealServiceInterface using MongoDB as the data store.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Sequence, Tuple, Union, cast

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.deal import Deal, DealStatus
from app.models.form import (
    CoreFieldType,
    Form,
    FormWithDetails,
    Question,
    QuestionType,
    Submission,
)
from app.models.user import User
from app.services.base import BaseService
from app.services.deal.interfaces import DealServiceInterface
from app.services.factory import (
    get_context_block_service,
    get_deal_document_service,
    get_form_service,
    get_thesis_service,
)
from app.services.form.interfaces import FormServiceInterface

logger = get_logger(__name__)


class DealService(BaseService, DealServiceInterface):
    """MongoDB implementation of DealService."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__(db)
        # self.thesis_service: Optional[ThesisServiceInterface] = None

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        self.context_block_service = await get_context_block_service()
        self.thesis_service = await get_thesis_service()

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    # Core CRUD Operations
    async def create_deal(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        deal_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Deal]:
        """Create a new deal directly."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Prepare deal data
            deal_dict = {
                "org_id": org_id,
                "form_id": form_id,
                "submission_ids": [submission_id],
                "created_by": created_by,
                **(deal_data or {}),
            }

            # Create deal
            deal = Deal(**deal_dict)

            # Add initial timeline event
            deal.add_timeline_event("Deal created")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} directly")

            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                    "submission_id": str(submission_id),
                    "created_by": str(created_by),
                    "deal_data": deal_data,
                },
            )
            return None

    async def create_deal_from_pitch(
        self,
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        deal_data: Dict[str, Any],
    ) -> Optional[Deal]:
        """Create a new deal from pitch upload data."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Prepare deal data for pitch upload
            deal_dict = {
                "org_id": org_id,
                "created_by": created_by,
                "form_id": None,  # No form for pitch uploads
                "submission_ids": [],  # No submissions for pitch uploads
                **deal_data,
            }

            # Create deal
            deal = Deal(**deal_dict)

            # Add initial timeline event
            deal.add_timeline_event("Deal created from pitch upload")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} from pitch upload")

            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "created_by": str(created_by),
                    "deal_data": deal_data,
                },
            )
            return None

    async def create_deal_from_submission(
        self,
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        form: Optional[FormWithDetails] = None,
        submission_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Deal]:
        """Create a new deal from a form submission."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # # Get form if not provided
            # if not form:
            #     form = await Form.find_one(query={"id": form_id})
            #     if not form:
            #         logger.error(f"Form not found: {form_id}")
            #         return None

            # Get submission data if not provided
            if not submission_data:
                # TODO: Get submission data from submission service
                # For now, we'll assume it's provided
                logger.error("Submission data must be provided")
                return None

            # Extract core fields from submission
            core_fields = await self._extract_core_fields(form, submission_data)  # type: ignore

            # Create deal
            deal = Deal(
                org_id=org_id,  # type: ignore
                form_id=form_id,  # type: ignore
                submission_ids=[submission_id],  # type: ignore
                created_by=created_by,  # type: ignore
                **core_fields,
            )

            # Add initial timeline event
            deal.add_timeline_event("Deal created from submission")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} from submission {submission_id}")

            # Migrate submission files to deal documents
            try:
                doc_service = await get_deal_document_service()
                migrated_docs = await doc_service.migrate_submission_files_to_deal(  # type: ignore
                    deal.id, org_id, [submission_id]
                )
                logger.info(
                    f"Migrated {len(migrated_docs)} documents to deal {deal.id}"
                )
            except Exception as e:
                logger.error(f"Failed to migrate documents for deal {deal.id}: {e}")
                # Don't fail deal creation if document migration fails

            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "form_id": str(form_id),
                    "submission_id": str(submission_id),
                    "org_id": str(org_id),
                    "created_by": str(created_by),
                },
            )
            return None

    async def _extract_core_fields(
        self,
        form: Optional[Union[Form, FormWithDetails]],
        submission_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Extract core fields from submission data based on form questions."""
        core_fields = {
            "company_name": None,
            "stage": None,
            "sector": None,
            "company_website": None,
            "founders": [],
        }
        founders = []

        # Return empty core fields if form is None
        if not form:
            return core_fields

        # Get all questions from form
        questions = []

        for section in form.sections:
            section_questions = await Question.find_many(
                query={"section_id": section.id},  # type: ignore
                sort=[("order", 1)],
            )
            questions.extend(section_questions)
        section_map = {sec.id: sec for sec in form.sections}  # type: ignore

        # Extract core fields from submission
        for question in questions:
            if (
                not question.core_field and section_map[question.section_id].repeatable  # type: ignore
            ):
                continue

            answer = submission_data.get(str(question.id))
            if answer is None:
                continue

            if question.core_field == CoreFieldType.COMPANY_NAME:
                core_fields["company_name"] = str(answer)
            elif question.core_field == CoreFieldType.STAGE:
                core_fields["stage"] = str(answer)
            elif question.core_field == CoreFieldType.SECTOR:
                if question.type == "multi_select":
                    core_fields["sector"] = (
                        answer if isinstance(answer, list) else [str(answer)]
                    )
                else:
                    core_fields["sector"] = str(answer)
            elif question.core_field == CoreFieldType.COMPANY_WEBSITE:
                core_fields["company_website"] = str(answer)

        repeatable_qs = {
            str(q.id): q  # Convert ObjectId to string for dictionary keys
            for q in questions
            if q.core_field and section_map[q.section_id].repeatable  # type: ignore
        }

        logger.info(
            f"🔍 DEBUG: Found {len(repeatable_qs)} repeatable questions with core fields"
        )
        for q_id, q in repeatable_qs.items():
            logger.info(
                f"🔍 DEBUG: Repeatable Q - ID: {q_id}, core_field: {q.core_field}, label: {q.label}"
            )

        logger.info(
            f"🔍 DEBUG: Processing {len(submission_data)} submission data items"
        )
        for key, value in list(submission_data.items())[:10]:  # Show first 10 items
            logger.info(f"🔍 DEBUG: Submission key: '{key}' -> value: {value}")

        for raw_key, raw_val in submission_data.items():
            logger.info(
                f"🔍 DEBUG: Processing raw_key: '{raw_key}', raw_val: {raw_val}"
            )

            if "_" not in raw_key:
                logger.info(f"🔍 DEBUG: Skipping '{raw_key}' - no underscore")
                continue

            q_id_str, idx_str = raw_key.rsplit("_", 1)
            logger.info(
                f"🔍 DEBUG: Split '{raw_key}' -> q_id_str: '{q_id_str}', idx_str: '{idx_str}'"
            )

            if not idx_str.isdigit():
                logger.info(
                    f"🔍 DEBUG: Skipping '{raw_key}' - idx_str '{idx_str}' is not digit"
                )
                continue

            idx = int(idx_str)
            logger.info(
                f"🔍 DEBUG: Looking for question with ID: '{q_id_str}' in repeatable_qs"
            )

            q = repeatable_qs.get(
                q_id_str
            )  # Now this will work - string key in string-keyed dict
            if not q:
                logger.info(
                    f"🔍 DEBUG: Question '{q_id_str}' not found in repeatable_qs"
                )
                logger.info(
                    f"🔍 DEBUG: Available repeatable question IDs: {list(repeatable_qs.keys())}"
                )
                continue

            logger.info(
                f"🔍 DEBUG: Found question '{q_id_str}' with core_field: {q.core_field}"
            )

            # make sure there's a slot for this index
            while len(founders) <= idx:
                founders.append({})
                logger.info(
                    f"🔍 DEBUG: Extended founders array to length {len(founders)}"
                )

            # map to your friendly field names
            if q.core_field == CoreFieldType.FOUNDER_NAME:
                key = "name"
            elif q.core_field == CoreFieldType.FOUNDER_ROLE:
                key = "role"
            elif q.core_field == CoreFieldType.FOUNDER_LINKEDIN:
                key = "linkedin"
            else:
                logger.info(f"🔍 DEBUG: Unknown core_field: {q.core_field}")
                continue

            logger.info(f"🔍 DEBUG: Setting founders[{idx}]['{key}'] = {raw_val}")
            founders[idx][key] = raw_val

        logger.info(f"🔍 DEBUG: Final founders array: {founders}")

        if founders:
            core_fields["founders"] = [f for f in founders if f]
            logger.info(
                f"🔍 DEBUG: Set core_fields['founders'] to: {core_fields['founders']}"
            )
        else:
            logger.info("🔍 DEBUG: No founders found, founders array is empty")

        return core_fields

    async def get_deal(self, deal_id: Union[str, ObjectId]) -> Optional[Deal]:
        """Get a deal by ID."""
        try:
            return await Deal.find_one(query={"_id": deal_id})
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return None

    async def list_deals(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        status: Optional[List[str]] = None,
        form_id: Optional[Union[str, ObjectId]] = None,
        search: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
        tags: Optional[List[str]] = None,
        assigned_to_me: Optional[bool] = None,
        created_at_start: Optional[str] = None,
        created_at_end: Optional[str] = None,
        sort_by: str = "updated_at",
        sort_order: str = "desc",
    ) -> Tuple[List[Deal], int]:
        """List deals with optional filtering and pagination."""
        try:
            # Build query
            query: Dict[str, Any] = {"org_id": ObjectId(org_id)}

            if status:
                query["status"] = {"$in": status}
            if form_id:
                query["form_id"] = ObjectId(form_id)
            if search:
                query["company_name"] = {"$regex": search, "$options": "i"}
            if stage:
                query["stage"] = stage
            if sector:
                query["sector"] = {
                    "$in": [sector] if isinstance(sector, str) else sector
                }
            if tags:
                query["tags"] = {"$in": tags}
            if assigned_to_me is not None:
                # This will be handled by the API layer with current_user.id
                # For now, we'll pass it through and handle in the API
                pass
            if created_at_start:
                try:
                    from datetime import datetime

                    start_timestamp = int(
                        datetime.fromisoformat(
                            created_at_start.replace("Z", "+00:00")
                        ).timestamp()
                    )
                    query["created_at"] = {"$gte": start_timestamp}
                except ValueError:
                    logger.warning(
                        f"Invalid created_at_start format: {created_at_start}"
                    )
            if created_at_end:
                try:
                    from datetime import datetime

                    end_timestamp = int(
                        datetime.fromisoformat(
                            created_at_end.replace("Z", "+00:00")
                        ).timestamp()
                    )
                    if "created_at" in query:
                        query["created_at"]["$lte"] = end_timestamp
                    else:
                        query["created_at"] = {"$lte": end_timestamp}
                except ValueError:
                    logger.warning(f"Invalid created_at_end format: {created_at_end}")

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(query=query, skip=skip, limit=limit, sort=sort)

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "skip": skip,
                    "limit": limit,
                    "status": status,
                    "form_id": str(form_id) if form_id else None,
                    "search": search,
                    "stage": stage,
                    "sector": sector,
                    "tags": tags,
                    "assigned_to_me": assigned_to_me,
                    "created_at_start": created_at_start,
                    "created_at_end": created_at_end,
                    "sort_by": sort_by,
                    "sort_order": sort_order,
                },
            )
            return [], 0

    async def update_deal(
        self, deal_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[Deal]:
        """Update a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Handle status updates
            if "status" in update_data:
                new_status = DealStatus(update_data["status"])
                notes = update_data.get("status_notes")
                deal.update_status(DealStatus(new_status), notes)
                del update_data["status"]
                if "status_notes" in update_data:
                    del update_data["status_notes"]

            # Handle tag updates
            if "add_tags" in update_data:
                for tag in update_data["add_tags"]:
                    deal.add_tag(tag)
                del update_data["add_tags"]
            if "remove_tags" in update_data:
                for tag in update_data["remove_tags"]:
                    deal.remove_tag(tag)
                del update_data["remove_tags"]

            # Update other fields
            for key, value in update_data.items():
                if hasattr(deal, key):
                    setattr(deal, key, value)

            # Save updates
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            await deal.save(is_update=True)

            return deal
        except Exception as e:
            await self.handle_error(
                e, {"deal_id": str(deal_id), "update_data": update_data}
            )
            return None

    async def delete_deal(self, deal_id: Union[str, ObjectId]) -> bool:
        """Delete a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return False

            # Delete deal
            await deal.delete()
            return True
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return False

    # Auxiliary Operations
    async def get_deals_by_submission(
        self, submission_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> List[Deal]:
        """Get deals associated with a specific submission."""
        try:
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {"org_id": org_id, "submission_ids": {"$in": [submission_id]}}

            deals = await Deal.find_many(query=query, sort=[("created_at", -1)])

            return deals
        except Exception as e:
            await self.handle_error(
                e, {"submission_id": str(submission_id), "org_id": str(org_id)}
            )
            return []

    async def get_deals_by_form(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """Get deals associated with a specific form."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {"org_id": org_id, "form_id": form_id}

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(
                query=query, skip=skip, limit=limit, sort=[("created_at", -1)]
            )

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "form_id": str(form_id),
                    "org_id": str(org_id),
                    "skip": skip,
                    "limit": limit,
                },
            )
            return [], 0

    async def get_deal_summary(self, org_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """Get deal summary statistics for dashboard."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Get all deals for the organization
            deals = await Deal.find_many(query={"org_id": org_id})

            # Calculate summary statistics
            total_deals = len(deals)

            # Count by status
            by_status = {}
            for status in DealStatus:
                by_status[status.value] = sum(
                    1 for deal in deals if deal.status == status
                )

            # Count by stage
            by_stage = {}
            for deal in deals:
                if deal.stage:
                    by_stage[deal.stage] = by_stage.get(deal.stage, 0) + 1

            # Count by sector
            by_sector = {}
            for deal in deals:
                if deal.sector:
                    sectors = (
                        deal.sector if isinstance(deal.sector, list) else [deal.sector]
                    )
                    for sector in sectors:
                        by_sector[sector] = by_sector.get(sector, 0) + 1

            # Recent activity (last 7 and 30 days)
            now = datetime.now(timezone.utc)
            seven_days_ago = int((now - timedelta(days=7)).timestamp())
            thirty_days_ago = int((now - timedelta(days=30)).timestamp())

            recent_activity = {
                "last_7_days": sum(
                    1 for deal in deals if deal.created_at >= seven_days_ago
                ),
                "last_30_days": sum(
                    1 for deal in deals if deal.created_at >= thirty_days_ago
                ),
            }

            return {
                "total_deals": total_deals,
                "by_status": by_status,
                "by_stage": by_stage,
                "by_sector": by_sector,
                "recent_activity": recent_activity,
            }
        except Exception as e:
            await self.handle_error(e, {"org_id": str(org_id)})
            return {
                "total_deals": 0,
                "by_status": {},
                "by_stage": {},
                "by_sector": {},
                "recent_activity": {"last_7_days": 0, "last_30_days": 0},
            }

    async def add_timeline_event(
        self,
        deal_id: Union[str, ObjectId],
        event: str,
        notes: Optional[str] = None,
        user_id: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """Add a timeline event to a deal."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Add timeline event with user info if provided
            event_data = {
                "date": datetime.now(timezone.utc).isoformat(),
                "event": event,
                "notes": notes,
            }
            if user_id:
                event_data["user_id"] = str(user_id)

            deal.timeline.append(event_data)
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "event": event,
                    "notes": notes,
                    "user_id": str(user_id) if user_id else None,
                },
            )
            return None

    async def update_deal_notes(
        self, deal_id: Union[str, ObjectId], notes: str
    ) -> Optional[Deal]:
        """Update deal notes."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            deal.notes = notes
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id), "notes": notes})
            return None

    async def bulk_update_deals(
        self,
        deal_ids: List[Union[str, ObjectId]],
        update_data: Dict[str, Any],
        org_id: Union[str, ObjectId],
    ) -> List[Deal]:
        """Bulk update multiple deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            updated_deals = []

            for deal_id in deal_ids:
                if isinstance(deal_id, str):
                    deal_id = ObjectId(deal_id)

                # Get deal and verify it belongs to the organization
                deal = await self.get_deal(deal_id)
                if not deal or deal.org_id != org_id:
                    continue

                # Update the deal
                updated_deal = await self.update_deal(deal_id, update_data)
                if updated_deal:
                    updated_deals.append(updated_deal)

            return updated_deals
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_ids": [str(id) for id in deal_ids],
                    "update_data": update_data,
                    "org_id": str(org_id),
                },
            )
            return []

    async def search_deals(
        self,
        org_id: Union[str, ObjectId],
        query: Optional[str] = None,
        filters: Optional[Dict[str, List[str]]] = None,
        date_range: Optional[Dict[str, str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """Advanced search for deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build base query
            search_query: Dict[str, Any] = {"org_id": org_id}

            # Add text search
            if query:
                search_query["company_name"] = {"$regex": query, "$options": "i"}

            # Add filters
            if filters:
                if "status" in filters:
                    search_query["status"] = {"$in": filters["status"]}
                if "stage" in filters:
                    search_query["stage"] = {"$in": filters["stage"]}
                if "sector" in filters:
                    search_query["sector"] = {"$in": filters["sector"]}
                if "tags" in filters:
                    search_query["tags"] = {"$in": filters["tags"]}

            # Add date range filter
            if date_range:
                date_filter = {}
                if "start" in date_range:
                    start_date = datetime.fromisoformat(date_range["start"]).replace(
                        tzinfo=timezone.utc
                    )
                    date_filter["$gte"] = int(start_date.timestamp())
                if "end" in date_range:
                    end_date = datetime.fromisoformat(date_range["end"]).replace(
                        tzinfo=timezone.utc
                    )
                    date_filter["$lte"] = int(end_date.timestamp())
                if date_filter:
                    search_query["created_at"] = date_filter

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(search_query)

            # Execute search
            deals = await Deal.find_many(
                query=search_query, skip=skip, limit=limit, sort=sort
            )

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "query": query,
                    "filters": filters,
                    "date_range": date_range,
                    "sort_by": sort_by,
                    "sort_order": sort_order,
                    "skip": skip,
                    "limit": limit,
                },
            )
            return [], 0

    async def get_deal_submission_preview(
        self, deal_id: Union[str, ObjectId]
    ) -> Optional[Dict[str, Any]]:
        """
        Get a comprehensive preview of all submissions for a deal.
        """
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            deal = await Deal.find_one(query={"_id": deal_id})
            if not deal:
                logger.error(f"Deal not found for preview: {deal_id}")
                return None

            submission_ids = getattr(deal, "submission_ids", [])
            if not submission_ids:
                return {
                    "deal_id": str(deal_id),
                    "no_submissions": True,
                    "submissions": [],
                    "dropdown": [],
                }

            # Use FormService to get form details efficiently
            form_service = cast(FormServiceInterface, await get_form_service())

            all_submissions_preview = []
            dropdown_options = []

            submissions = await Submission.find_many({"_id": {"$in": submission_ids}})

            # Create a map for quick lookup
            # submission_map = {sub.id: sub for sub in submissions}

            # Fetch all required forms in a single query
            form_ids = list(set([sub.form_id for sub in submissions]))
            forms = await Form.find_many({"_id": {"$in": form_ids}})
            form_map = {
                str(form.id): await form_service.get_form_with_details(str(form.id))
                for form in forms
            }

            # Process submissions in chronological order (most recent first)
            sorted_submissions = sorted(
                submissions, key=lambda s: s.created_at, reverse=True
            )

            for submission in sorted_submissions:
                form_details = form_map.get(str(submission.form_id))
                if not form_details:
                    logger.warning(
                        f"Form {submission.form_id} not found for submission {submission.id}"
                    )
                    continue

                # Create a flat map of all questions in the form for easy lookup
                question_map = {
                    str(q.id): q
                    for section in form_details.sections
                    for q in section.questions
                }

                # Build the sections for the preview
                preview_sections = []
                for section_details in form_details.sections:
                    section_data = {
                        "section_id": str(section_details.id),
                        "title": section_details.title,
                        "repeatable": section_details.repeatable,
                    }

                    if section_details.repeatable:
                        # Handle repeatable sections - parse answers with _0, _1, etc. suffix
                        instances = []
                        instance_map = {}

                        # Group answers by instance index
                        for q_id, answer in submission.answers.items():
                            if "_" in q_id:
                                base_q_id, instance_str = q_id.rsplit("_", 1)
                                try:
                                    instance_index = int(instance_str)
                                    if instance_index not in instance_map:
                                        instance_map[instance_index] = {}
                                    instance_map[instance_index][base_q_id] = answer
                                except ValueError:
                                    # Not a repeatable section answer, skip
                                    continue

                        # Create instances for this section
                        for instance_index, instance_answers in instance_map.items():
                            instance_questions = []
                            for q_id, answer in instance_answers.items():
                                question = question_map.get(q_id)
                                if question:
                                    instance_questions.append({
                                        "question_id": q_id,
                                        "label": question.label,
                                        "type": question.type.value,
                                        "answer": self._format_answer(answer, question),
                                    })
                            instances.append({
                                "index": instance_index,
                                "answers": instance_questions,
                            })
                        section_data["instances"] = sorted(
                            instances, key=lambda i: i["index"]
                        )
                    else:
                        # Handle regular sections - look directly in answers
                        regular_questions = []
                        for question in section_details.questions:
                            q_id_str = str(question.id)
                            answer = submission.answers.get(q_id_str)
                            regular_questions.append({
                                "question_id": q_id_str,
                                "label": question.label,
                                "type": question.type.value,
                                "answer": self._format_answer(answer, question),
                            })
                        section_data["questions"] = regular_questions
                    preview_sections.append(section_data)

                # Append to the main lists
                all_submissions_preview.append({
                    "submission_id": str(submission.id),
                    "submitted_at": datetime.fromtimestamp(
                        submission.created_at, tz=timezone.utc
                    ).isoformat(),
                    "form_name": form_details.name,
                    "form_version": form_details.version,
                    "sections": preview_sections,
                })

                dropdown_options.append({
                    "form_name": form_details.name,
                    "submission_id": str(submission.id),
                    "submitted_at": datetime.fromtimestamp(
                        submission.created_at, tz=timezone.utc
                    ).isoformat(),
                })

            return {
                "deal_id": str(deal_id),
                "no_submissions": len(all_submissions_preview) == 0,
                "submissions": all_submissions_preview,
                "dropdown": dropdown_options,
            }

        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return None

    def _format_answer(self, answer: Any, question: Question) -> Any:
        """Helper to format answers based on question type (e.g., mapping option values to labels)."""
        if answer is None:
            return None

        # For single/multi-select, map value to label
        if (
            question.type
            in [
                QuestionType.SINGLE_SELECT,
                QuestionType.MULTI_SELECT,
            ]
            and question.options
        ):
            options_map = {opt["value"]: opt["label"] for opt in question.options}
            if isinstance(answer, list):
                return [options_map.get(val, val) for val in answer]
            return options_map.get(answer, answer)

        return answer

    async def assign_users_to_deal(
        self,
        deal_id: Union[str, ObjectId],
        user_ids: Sequence[Union[str, ObjectId]],
        acting_user_id: Union[str, ObjectId],
    ) -> Optional[Deal]:
        """Assign multiple users to a deal with validation and email notification."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(acting_user_id, str):
                acting_user_id = ObjectId(acting_user_id)

            # Convert user_ids to ObjectIds
            user_obj_ids = []
            for user_id in user_ids:
                if isinstance(user_id, str):
                    user_obj_ids.append(ObjectId(user_id))
                else:
                    user_obj_ids.append(user_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                logger.error(f"Deal not found: {deal_id}")
                return None

            # Get acting user
            acting_user = await User.find_one(query={"_id": acting_user_id})
            if not acting_user:
                logger.error(f"Acting user not found: {acting_user_id}")
                return None

            # Get users to assign and validate they belong to same organization
            users_to_assign = []
            for user_obj_id in user_obj_ids:
                user = await User.find_one(query={"_id": user_obj_id})
                if not user:
                    logger.error(f"User not found: {user_obj_id}")
                    return None
                if user.org_id != deal.org_id:
                    logger.error(
                        f"User {user_obj_id} not in same org as deal {deal_id}"
                    )
                    return None
                users_to_assign.append(user)

            # Check if already assigned to same users (no-op)
            current_assigned_ids = set(str(uid) for uid in deal.assigned_user_ids)
            new_assigned_ids = set(str(uid) for uid in user_obj_ids)

            if current_assigned_ids == new_assigned_ids:
                logger.info(f"Deal {deal_id} already assigned to users {user_obj_ids}")
                return deal

            # Update assignment
            deal.assigned_user_ids = user_obj_ids
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            # Add timeline event
            user_names = [user.name for user in users_to_assign]
            event_text = f"Assigned to {', '.join(user_names)}"
            deal.add_timeline_event(event_text, None)

            # Save deal
            await deal.save(is_update=True)

            # Send assignment emails
            try:
                from app.services.factory import get_email_service

                email_service = await get_email_service()
                company_name = deal.company_name or "Unknown Company"

                for user in users_to_assign:
                    await email_service.send_deal_assignment_email(
                        user_email=user.email,
                        user_name=user.name,
                        deal_id=str(deal_id),
                        company_name=company_name,
                        assigned_by_name=acting_user.name,
                    )
                logger.info(
                    f"Assignment emails sent for deal {deal_id} to {len(users_to_assign)} users"
                )
            except Exception as e:
                logger.error(
                    f"Failed to send assignment emails for deal {deal_id}: {e}"
                )
                # Don't fail the assignment if email fails

            logger.info(f"Assigned deal {deal_id} to users {user_obj_ids}")
            return deal

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "user_ids": [str(uid) for uid in user_ids],
                    "acting_user_id": str(acting_user_id),
                },
            )
            return None

    async def update_deal_assignments(
        self,
        deal_id: Union[str, ObjectId],
        user_ids: Sequence[Union[str, ObjectId]],
        action: str,
        acting_user_id: Union[str, ObjectId],
    ) -> Optional[Deal]:
        """Update assigned users on a deal with different actions."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(acting_user_id, str):
                acting_user_id = ObjectId(acting_user_id)

            # Convert user_ids to ObjectIds
            user_obj_ids = []
            for user_id in user_ids:
                if isinstance(user_id, str):
                    user_obj_ids.append(ObjectId(user_id))
                else:
                    user_obj_ids.append(user_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                logger.error(f"Deal not found: {deal_id}")
                return None

            # Get acting user
            acting_user = await User.find_one(query={"_id": acting_user_id})
            if not acting_user:
                logger.error(f"Acting user not found: {acting_user_id}")
                return None

            # Get users and validate they belong to same organization
            users_to_assign = []
            for user_obj_id in user_obj_ids:
                user = await User.find_one(query={"_id": user_obj_id})
                if not user:
                    logger.error(f"User not found: {user_obj_id}")
                    return None
                if user.org_id != deal.org_id:
                    logger.error(
                        f"User {user_obj_id} not in same org as deal {deal_id}"
                    )
                    return None
                users_to_assign.append(user)

            # Get current assigned users for comparison
            current_assigned_ids = set(str(uid) for uid in deal.assigned_user_ids)
            # new_assigned_ids = set(str(uid) for uid in user_obj_ids)

            # Perform the requested action
            if action == "replace":
                # Replace all assigned users
                deal.assigned_user_ids = user_obj_ids
                event_text = f"Reassigned to {', '.join([user.name for user in users_to_assign])}"

            elif action == "add":
                # Add new users to existing assignments
                for user_obj_id in user_obj_ids:
                    if user_obj_id not in deal.assigned_user_ids:
                        deal.assigned_user_ids.append(user_obj_id)
                event_text = f"Added {', '.join([user.name for user in users_to_assign])} to assignment"

            elif action == "remove":
                # Remove specified users from assignments
                deal.assigned_user_ids = [
                    uid for uid in deal.assigned_user_ids if uid not in user_obj_ids
                ]
                event_text = f"Removed {', '.join([user.name for user in users_to_assign])} from assignment"

            else:
                logger.error(f"Invalid action: {action}")
                return None

            # Check if there was actually a change
            updated_assigned_ids = set(str(uid) for uid in deal.assigned_user_ids)
            if current_assigned_ids == updated_assigned_ids:
                logger.info(f"No change in assignments for deal {deal_id}")
                return deal

            # Update timestamp and add timeline event
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            deal.add_timeline_event(event_text, None)

            # Save deal
            await deal.save(is_update=True)

            # Send email notifications for new assignments
            if action in ["replace", "add"]:
                try:
                    from app.services.factory import get_email_service

                    email_service = await get_email_service()
                    company_name = deal.company_name or "Unknown Company"

                    # Only send emails to newly added users
                    if action == "replace":
                        users_to_notify = users_to_assign
                    else:  # add
                        users_to_notify = [
                            user
                            for user in users_to_assign
                            if str(user.id) not in current_assigned_ids
                        ]

                    for user in users_to_notify:
                        await email_service.send_deal_assignment_email(
                            user_email=user.email,
                            user_name=user.name,
                            deal_id=str(deal_id),
                            company_name=company_name,
                            assigned_by_name=acting_user.name,
                        )
                    logger.info(
                        f"Assignment emails sent for deal {deal_id} to {len(users_to_notify)} users"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to send assignment emails for deal {deal_id}: {e}"
                    )
                    # Don't fail the assignment if email fails

            logger.info(f"Updated deal {deal_id} assignments with action '{action}'")
            return deal

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "user_ids": [str(uid) for uid in user_ids],
                    "action": action,
                    "acting_user_id": str(acting_user_id),
                },
            )
            return None

    async def assign_user_to_deal(
        self,
        deal_id: Union[str, ObjectId],
        user_id: Union[str, ObjectId],
        acting_user_id: Union[str, ObjectId],
    ) -> Optional[Deal]:
        """Assign a single user to a deal (backward compatibility)."""
        return await self.assign_users_to_deal(deal_id, [user_id], acting_user_id)

    async def update_deal_status(
        self,
        deal_id: Union[str, ObjectId],
        new_status: DealStatus,
        note: Optional[str] = None,
        acting_user_id: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """Update deal status with timeline tracking."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                logger.error(f"Deal not found: {deal_id}")
                return None

            # Update status
            old_status = deal.status
            deal.status = new_status
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            # Add timeline event
            event_text = f"Status changed to {new_status.value.upper()}"
            deal.add_timeline_event(event_text, note)

            # Save deal
            await deal.save(is_update=True)

            logger.info(
                f"Updated deal {deal_id} status from {old_status} to {new_status}"
            )
            return deal

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "new_status": str(new_status),
                    "note": note,
                    "acting_user_id": str(acting_user_id) if acting_user_id else None,
                },
            )
            return None
