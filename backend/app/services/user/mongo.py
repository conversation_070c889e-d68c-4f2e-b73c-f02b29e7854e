from typing import Optional, List, Dict, Any
from bson import ObjectId
from datetime import datetime

from motor.motor_asyncio import AsyncIOMotorDatabase
from app.models.organization import Organization
from app.models.user import User, PublicUser
from app.services.base import BaseService
from app.services.user.interfaces import UserServiceInterface, PublicUserServiceInterface
from app.core.logging import get_logger

logger = get_logger(__name__)


class UserService(BaseService, UserServiceInterface):
    """MongoDB implementation of UserService."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize database connection."""
        pass

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            return User(**user) if user else None
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        try:
            user = await User.find_one({"email": email})
            logger.info(f"User: {user}")
            return user if user else None
        except Exception as e:
            await self.handle_error(e, {"email": email})

    async def create_user(
        self,
        email: str,
        password: str,
        name: str,
        is_superuser: bool = False,
        org_id: Optional[str] = None,
        role_id: Optional[str] = None,
    ) -> User:
        """Create a new user."""
        try:
            user = User(
                email=email,
                password_hash=password,
                name=name,
                is_superuser=is_superuser,
                org_id=org_id,
                role_id=role_id,
            )
            result = await self.db.users.insert_one(user.dict(by_alias=True))
            user.id = result.inserted_id
            return user
        except Exception as e:
            await self.handle_error(e, {"email": email, "name": name, "is_superuser": is_superuser})

    async def update_user(self, query: Dict[str, Any], update: Dict[str, Any]) -> Optional[User]:
        """Update an existing user using MongoDB-style update."""
        try:
            result = await self.db.users.find_one_and_update(query, update, return_document=True)
            return User(**result) if result else None
        except Exception as e:
            await self.handle_error(e, {"query": query, "update": update})

    async def delete_user(self, user_id: str) -> bool:
        """Delete a user."""
        try:
            result = await self.db.users.delete_one({"_id": ObjectId(user_id)})
            return result.deleted_count > 0
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def list_users(
        self, skip: int = 0, limit: int = 100, status: Optional[str] = None
    ) -> List[User]:
        """List users with optional filtering."""
        try:
            query = {}
            if status:
                query["status"] = status

            cursor = self.db.users.find(query).skip(skip).limit(limit)
            users = await cursor.to_list(length=limit)
            return [User(**user) for user in users]
        except Exception as e:
            await self.handle_error(e, {"skip": skip, "limit": limit, "status": status})

    async def activate_user(self, user_id: str) -> Optional[User]:
        """Activate a user."""
        try:
            result = await self.db.users.find_one_and_update(
                {"_id": ObjectId(user_id)}, {"$set": {"status": "active"}}, return_document=True
            )
            return User(**result) if result else None
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def suspend_user(self, user_id: str) -> Optional[User]:
        """Suspend a user."""
        try:
            result = await self.db.users.find_one_and_update(
                {"_id": ObjectId(user_id)}, {"$set": {"status": "suspended"}}, return_document=True
            )
            return User(**result) if result else None
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def get_organization(self, user_id: str) -> Optional[Organization]:
        """Get organization for a user."""
        try:
            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            return Organization(**user.get("organization")) if user else None
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})


class PublicUserService(BaseService, PublicUserServiceInterface):
    """MongoDB implementation of PublicUserService."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize database connection."""
        pass

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def get_public_user(self, user_id: str) -> Optional[PublicUser]:
        """Get public user by ID."""
        try:
            user = await PublicUser.find_one({"_id": ObjectId(user_id)})
            return user if user else None
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def get_public_user_by_email(self, email: str) -> Optional[PublicUser]:
        """Get public user by email."""
        try:
            user = await PublicUser.find_one({"email": email})
            logger.info(f"Public User: {user}")
            return user if user else None
        except Exception as e:
            await self.handle_error(e, {"email": email})

    async def create_public_user(
        self, email: str, name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None
    ) -> PublicUser:
        """Create a new public user."""
        try:
            publicuser = PublicUser(email=email, name=name, metadata=metadata or {})
            await publicuser.save()
            return publicuser
        except Exception as e:
            await self.handle_error(e, {"email": email, "name": name, "metadata": metadata})

    async def update_public_user(
        self, query: Dict[str, Any], update: Dict[str, Any]
    ) -> Optional[PublicUser]:
        """Update an existing public user using MongoDB-style update."""
        try:
            result = await PublicUser.update_many(query, update)
            return PublicUser(**result) if result else None
        except Exception as e:
            await self.handle_error(e, {"query": query, "update": update})

    async def delete_public_user(self, user_id: str) -> bool:
        """Delete a public user."""
        try:
            result = await self.db.publicusers.delete_one({"_id": ObjectId(user_id)})
            return result.deleted_count > 0
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})

    async def track_form_access(self, user_id: str, form_id: str) -> bool:
        """Track that a public user accessed a specific form."""
        try:
            result = await PublicUser.update_one(
                query={"id": ObjectId(user_id)},
                update={
                    "$addToSet": {"accessed_forms": ObjectId(form_id)},
                    "$set": {
                        "last_access": int(datetime.utcnow().timestamp()),
                        "updated_at": int(datetime.utcnow().timestamp()),
                    },
                },
            )
            return result.modified_count > 0
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id, "form_id": form_id})

    async def update_last_access(self, user_id: str) -> bool:
        """Update the last access timestamp for a public user."""
        try:
            result = await PublicUser.update_one(
                query={"id": ObjectId(user_id)},
                update={
                    "$set": {
                        "last_access": int(datetime.utcnow().timestamp()),
                        "updated_at": int(datetime.utcnow().timestamp()),
                    }
                },
            )
            return result.modified_count > 0
        except Exception as e:
            await self.handle_error(e, {"user_id": user_id})
