"""
Queue worker pool implementation.

This module provides a worker pool for managing multiple queue workers.
"""
import asyncio
from typing import Any, Dict, List, Optional, Union, Callable

from app.core.logging import get_logger
from app.services.queue.interfaces import QueueServiceInterface
from app.models.queue import QueueType
from app.services.queue.worker import QueueWorker
from app.services.queue.worker_interface import Worker<PERSON>nter<PERSON>, JobHandlerInterface, WorkerPoolInterface

logger = get_logger(__name__)


class WorkerPool(WorkerPoolInterface):
    """Pool of workers for processing jobs from the queue."""
    
    def __init__(
        self,
        queue_service: QueueServiceInterface,
        queue_types: Optional[List[QueueType]] = None,
        poll_interval: float = 5.0,
        shutdown_timeout: int = 60
    ):
        """
        Initialize the worker pool.
        
        Args:
            queue_service: Queue service to use
            queue_types: Queue types to process (default: [QueueType.DEFAULT])
            poll_interval: How often to poll for jobs when none are available (seconds)
            shutdown_timeout: Maximum time to wait for jobs to complete during shutdown (seconds)
        """
        self.queue_service = queue_service
        self.queue_types = queue_types or [QueueType.DEFAULT]
        self.poll_interval = poll_interval
        self.shutdown_timeout = shutdown_timeout
        
        self.workers: List[WorkerInterface] = []
        self.handlers: Dict[str, Union[JobHandlerInterface, Callable, Any]] = {}
    
    async def initialize(self) -> None:
        """Initialize the worker pool."""
        # Initialize the queue service
        await self.queue_service.initialize()
    
    async def cleanup(self) -> None:
        """Clean up resources used by the worker pool."""
        # Stop all workers
        await self.stop()
        
        # Clean up the queue service
        await self.queue_service.cleanup()
    
    async def start(self, num_workers: int = 1) -> None:
        """
        Start the worker pool.
        
        Args:
            num_workers: Number of workers to start
        """
        logger.info(f"Starting worker pool with {num_workers} workers")
        
        # Create and start workers
        for i in range(num_workers):
            worker_id = f"worker-{i+1}"
            worker = QueueWorker(
                queue_service=self.queue_service,
                worker_id=worker_id,
                queue_types=self.queue_types,
                poll_interval=self.poll_interval,
                shutdown_timeout=self.shutdown_timeout
            )
            
            # Register handlers
            for job_type, handler in self.handlers.items():
                worker.register_handler(job_type, handler)
            
            # Initialize and start the worker
            await worker.initialize()
            await worker.start()
            
            self.workers.append(worker)
            logger.info(f"Started worker {worker_id}")
    
    async def stop(self) -> None:
        """Stop the worker pool."""
        if not self.workers:
            logger.warning("No workers to stop")
            return
        
        logger.info(f"Stopping {len(self.workers)} workers")
        
        # Stop all workers
        stop_tasks = [worker.stop() for worker in self.workers]
        await asyncio.gather(*stop_tasks)
        
        # Clear the worker list
        self.workers.clear()
        
        logger.info("All workers stopped")
    
    def register_handler(
        self,
        job_type: str,
        handler: Union[JobHandlerInterface, Callable, Any]
    ) -> None:
        """
        Register a handler for a job type.
        
        Args:
            job_type: Type of job to handle
            handler: Handler function or class
        """
        self.handlers[job_type] = handler
        logger.info(f"Registered handler for job type: {job_type}")
        
        # Register with existing workers
        for worker in self.workers:
            worker.register_handler(job_type, handler)
    
 
