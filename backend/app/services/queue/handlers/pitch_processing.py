"""
Pitch Processing Queue Handler

Handles background processing of uploaded pitch decks and one-pagers.
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.models.deal import DealStatus
from app.models.queue import Job
from app.services.factory import get_deal_service, get_file_service
from app.services.pdf_parser.service import PDFParserService
from app.services.pitch_parser.service import PitchParserService
from app.services.queue.handlers.base import BaseJobHandler

logger = get_logger(__name__)


class PitchProcessingHandler(BaseJobHandler):
    """Handler for processing pitch upload jobs."""

    def __init__(self):
        super().__init__()
        self.pdf_parser: Optional[PDFParserService] = None
        self.pitch_parser: Optional[PitchParserService] = None
        self.file_service: Optional[Any] = None
        self.deal_service: Optional[Any] = None

    async def _initialize_services(self) -> None:
        """Initialize all required services."""
        try:
            # Initialize PDF and pitch parser services directly (they don't use factory)
            self.pdf_parser = PDFParserService()
            await self.pdf_parser.initialize()

            self.pitch_parser = PitchParserService()
            await self.pitch_parser.initialize()

            # Use factory pattern for services that need database
            from app.core.database import get_database

            db = await get_database()

            self.file_service = await get_file_service(db)
            self.deal_service = await get_deal_service(db)

            self.logger.info("Pitch processing handler initialized")
        except Exception as e:
            self.logger.error(
                f"Failed to initialize pitch processing handler: {str(e)}"
            )
            raise

    async def cleanup(self) -> None:
        """Cleanup all services."""
        try:
            if self.pdf_parser:
                await self.pdf_parser.cleanup()
            if self.pitch_parser:
                await self.pitch_parser.cleanup()
            if self.file_service:
                await self.file_service.cleanup()
            if self.deal_service:
                await self.deal_service.cleanup()

            self.logger.info("Pitch processing handler cleanup completed")
        except Exception as e:
            self.logger.error(
                f"Error during pitch processing handler cleanup: {str(e)}"
            )

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """Process a pitch upload job."""
        job_data = job.payload
        return await self.handle_job(job_data)

    async def handle_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle pitch processing job.

        Job data should contain:
        - temp_id: Temporary upload ID
        - org_id: Organization ID
        - user_id: User ID who uploaded
        - user_email: User email
        """
        # Extract and validate required job data
        temp_id = job_data.get("temp_id")
        org_id = job_data.get("org_id")
        user_id = job_data.get("user_id")
        user_email = job_data.get("user_email")

        # Validate required fields
        if not all([temp_id, org_id, user_id, user_email]):
            error_msg = f"Missing required job data: temp_id={temp_id}, org_id={org_id}, user_id={user_id}, user_email={user_email}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "temp_id": temp_id}

        self.logger.info(f"Processing pitch upload: temp_id={temp_id}, org_id={org_id}")

        try:
            # Reconstruct S3 key from temp_id
            s3_key = f"deals/{org_id}/temp/{temp_id}/pitch_upload.pdf"

            # Ensure services are initialized (BaseJobHandler.handle() calls initialize())
            if (
                not self.pdf_parser
                or not self.pitch_parser
                or not self.file_service
                or not self.deal_service
            ):
                await self._initialize_services()

            # Step 1: Extract text from PDF
            self.logger.info(f"Extracting text from PDF: {s3_key}")
            if not self.pdf_parser:
                raise RuntimeError("PDF parser service not initialized")

            pdf_data = await self.pdf_parser.extract_text_from_s3(
                s3_bucket=settings.S3_BUCKET_ASSETS, s3_key=s3_key
            )

            # Auto-detect pitch type based on content
            pitch_type = self.pdf_parser.detect_pitch_type(
                pdf_data["page_count"], pdf_data["text"]
            )

            self.logger.info(f"Auto-detected pitch type: {pitch_type}")

            # Step 2: Parse content with LLM
            self.logger.info(f"Parsing pitch content with LLM (type: {pitch_type})")
            if not self.pitch_parser:
                raise RuntimeError("Pitch parser service not initialized")

            parsed_data = await self.pitch_parser.parse_pitch_content(
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
                pages=pdf_data["pages"],
                metadata=pdf_data.get("metadata"),
            )

            # Step 3: Generate short description
            self.logger.info("Generating short description")
            short_description = await self.pitch_parser.generate_short_description(
                parsed_data=parsed_data,
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
            )

            # Step 4: Generate executive summary
            self.logger.info("Generating executive summary")
            executive_summary = await self.pitch_parser.generate_executive_summary(
                parsed_data=parsed_data,
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
            )

            # Step 5: Generate markdown summary
            self.logger.info("Generating markdown summary")
            markdown_summary = await self.pitch_parser.generate_markdown_summary(
                parsed_data=parsed_data,
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
            )

            # Step 6: Create deal from parsed data
            self.logger.info("Creating deal from parsed data")
            deal = await self._create_deal_from_parsed_pitch(
                org_id=str(org_id),
                user_id=str(user_id),
                temp_id=str(temp_id),
                parsed_data=parsed_data,
                short_description=short_description,
                markdown_summary=markdown_summary,
                pitch_type=pitch_type,
                pdf_data=pdf_data,
                original_s3_key=s3_key,
            )

            # Step 7: Move PDF to permanent location
            self.logger.info(f"Moving PDF to permanent location for deal {deal.id}")
            final_s3_key = await self._move_pdf_to_permanent_location(
                temp_s3_key=s3_key, deal_id=str(deal.id), org_id=str(org_id)
            )

            # Step 8: Store executive summary
            self.logger.info(f"Storing executive summary for deal {deal.id}")
            executive_summary_s3_key = await self._store_executive_summary(
                deal_id=str(deal.id),
                org_id=str(org_id),
                executive_summary=executive_summary,
            )

            # Step 9: Update deal with final URLs
            await self._update_deal_with_final_urls(
                deal=deal,
                final_s3_key=final_s3_key,
                markdown_summary=markdown_summary,
                executive_summary_s3_key=executive_summary_s3_key,
                short_description=short_description,
            )

            self.logger.info(f"Successfully processed pitch upload: deal_id={deal.id}")

            return {
                "success": True,
                "deal_id": str(deal.id),
                "temp_id": temp_id,
                "pitch_type": pitch_type,
                "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                    "confidence_score", 0.0
                ),
                "pages_processed": pdf_data["page_count"],
            }

        except Exception as e:
            self.logger.error(f"Error processing pitch upload {temp_id}: {str(e)}")

            # Try to create a minimal deal with error information
            try:
                await self._create_error_deal(
                    org_id=str(org_id),
                    user_id=str(user_id),
                    temp_id=str(temp_id),
                    error_message=str(e),
                    s3_key=s3_key,
                )
            except Exception as create_error:
                self.logger.error(f"Failed to create error deal: {str(create_error)}")

            return {"success": False, "error": str(e), "temp_id": temp_id}

    async def _create_deal_from_parsed_pitch(
        self,
        org_id: str,
        user_id: str,
        temp_id: str,
        parsed_data: Dict[str, Any],
        short_description: str,
        markdown_summary: str,
        pitch_type: str,
        pdf_data: Dict[str, Any],
        original_s3_key: str,
    ) -> Any:
        """Create a deal from parsed pitch data."""
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        # Prepare deal data
        deal_data = {
            "company_name": parsed_data.get("company_name")
            or f"Company from {pitch_type}",
            "description": short_description,  # Use the generated short description
            "sector": parsed_data.get("sector", []),
            "status": DealStatus.TRIAGE,
            "enriched_data": {
                "pitch_parsing": parsed_data,
                "pdf_metadata": pdf_data.get("metadata", {}),
                "processing_metadata": {
                    "temp_id": temp_id,
                    "pitch_type": pitch_type,
                    "processed_at": datetime.now(timezone.utc).isoformat(),
                    "original_filename": pdf_data.get("filename"),
                    "page_count": pdf_data.get("page_count"),
                },
            },
            "tags": [pitch_type] if pitch_type == "one_pager" else [],
            "timeline": [
                {
                    "event": "Deal created from pitch upload",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "details": {
                        "pitch_type": pitch_type,
                        "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                            "confidence_score"
                        ),
                        "pages": pdf_data.get("page_count"),
                    },
                }
            ],
        }

        # Create the deal
        deal = await self.deal_service.create_deal_from_pitch(
            org_id=org_id, created_by=user_id, deal_data=deal_data
        )

        return deal

    async def _move_pdf_to_permanent_location(
        self, temp_s3_key: str, deal_id: str, org_id: str
    ) -> str:
        """Move PDF from temp location to permanent deal location."""
        if not self.file_service:
            raise RuntimeError("File service not initialized")

        # Generate final S3 key - use deals/ instead of decks/

        final_s3_key = f"deals/{deal_id}/pitch_deck.pdf"

        # Copy file to new location
        await self.file_service.copy_s3_object(
            source_bucket=settings.S3_BUCKET_ASSETS,
            source_key=temp_s3_key,
            dest_bucket=settings.S3_BUCKET_ASSETS,
            dest_key=final_s3_key,
        )

        # Delete temp file
        await self.file_service.delete_s3_object(
            bucket=settings.S3_BUCKET_ASSETS, key=temp_s3_key
        )

        return final_s3_key

    async def _store_executive_summary(
        self, deal_id: str, org_id: str, executive_summary: str
    ) -> str:
        """Store executive summary in deals/ structure."""
        if not self.file_service:
            raise RuntimeError("File service not initialized")

        # Generate S3 key for executive summary
        executive_summary_s3_key = f"deals/{deal_id}/research/executive_summary.md"

        # Upload executive summary to S3
        await self.file_service.upload_text_to_s3(
            bucket=settings.S3_BUCKET_ASSETS,
            key=executive_summary_s3_key,
            content=executive_summary,
            content_type="text/markdown",
        )

        return executive_summary_s3_key

    async def _update_deal_with_final_urls(
        self,
        deal: Any,
        final_s3_key: str,
        markdown_summary: str,
        executive_summary_s3_key: str,
        short_description: str,
    ) -> None:
        """Update deal with final URLs for PDF and markdown."""
        if not self.file_service or not self.deal_service:
            raise RuntimeError("File service or deal service not initialized")

        # Generate public URLs
        pitch_deck_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{final_s3_key}"

        # Save markdown to S3 - use deals/ structure
        markdown_s3_key = f"deals/{deal.org_id}/{deal.id}/research/pitch_summary.md"
        await self.file_service.upload_text_to_s3(
            bucket=settings.S3_BUCKET_ASSETS,
            key=markdown_s3_key,
            content=markdown_summary,
            content_type="text/markdown",
        )

        context_block_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{markdown_s3_key}"

        # Generate executive summary URL
        executive_summary_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{executive_summary_s3_key}"

        # Update deal
        await self.deal_service.update_deal(
            deal_id=str(deal.id),
            update_data={
                "pitch_deck_url": pitch_deck_url,
                "context_block_url": context_block_url,
                "executive_summary_url": executive_summary_url,
                "description": short_description,  # Update with generated short description
            },
        )

    async def _create_error_deal(
        self, org_id: str, user_id: str, temp_id: str, error_message: str, s3_key: str
    ) -> None:
        """Create a minimal deal when processing fails."""
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        deal_data = {
            "company_name": f"Failed Upload {temp_id[:8]}",
            "description": "Pitch processing failed",
            "status": DealStatus.TRIAGE,
            "enriched_data": {
                "processing_error": {
                    "error": error_message,
                    "temp_id": temp_id,
                    "s3_key": s3_key,
                    "failed_at": datetime.now(timezone.utc).isoformat(),
                }
            },
            "tags": ["processing_failed"],
            "timeline": [
                {
                    "event": "Deal creation failed during pitch processing",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "details": {"error": error_message},
                }
            ],
        }

        await self.deal_service.create_deal_from_pitch(
            org_id=org_id, created_by=user_id, deal_data=deal_data
        )
