"""
Queue job handlers.

This package contains handlers for queue jobs.
"""

from typing import Callable, Dict, <PERSON>

from app.jobs.chat_completion import process_chat_completion
from app.services.queue.handlers.generic_job import HANDLERS as GENERIC_JOB_HANDLERS
from app.services.queue.handlers.job_tracking import HAN<PERSON><PERSON><PERSON> as JOB_TRACKING_HANDLERS
from app.services.queue.handlers.research_handler import HANDLERS as RESEARCH_HANDLERS
from app.services.queue.handlers.submission_processing import (
    HANDLERS as SUBMISSION_PROCESSING_HANDLERS,
)
from app.services.queue.handlers.pitch_processing import PitchProcessingHandler
from app.services.queue.worker_interface import Job<PERSON><PERSON>lerInterface

# Create handler registry with all handlers
HANDLERS: Dict[str, Union[JobHandlerInterface, Callable]] = {
    # Function-based handlers
    **JOB_TRACKING_HANDLERS,
    **GENERIC_JOB_HANDLERS,
    **SUBMISSION_PROCESSING_HANDLERS,
    **RESEARC<PERSON>_HANDLERS,
    # Chat completion handler
    "chat_completion": process_chat_completion,
    # Pitch processing handler
    "parse_pitch_upload": PitchProcessingHandler(),
}

__all__ = ["HANDLERS"]
