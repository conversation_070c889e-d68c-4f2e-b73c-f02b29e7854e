"""
File service implementation for TractionX submission file handling.

This service handles S3 file operations, presigned URL generation,
and file metadata management for form submissions.
"""

import boto3
import botocore
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List

from motor.motor_asyncio import AsyncIOMotorDatabase
from app.core.config import settings
from app.core.logging import get_logger
from app.utils.common import ObjectIdField
from app.models.file import SubmissionFile, FileAccessLog, FileStatus, FileAccessType
from app.services.base import BaseService
from app.services.file.interface import FileServiceInterface


class FileService(BaseService, FileServiceInterface):
    """File service implementation using S3 for storage."""
    
    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__()
        self.db = db  # Store database reference even though FileService uses S3
        self.logger = get_logger(__name__)
        self.s3_client = None
        self.bucket_name = settings.S3_BUCKET_SUBMISSIONS
        self.region = settings.AWS_REGION
        self.presigned_url_expiry = settings.S3_PRESIGNED_URL_EXPIRY
        self.max_file_size_bytes = settings.MAX_FILE_SIZE_MB * 1024 * 1024
        self.allowed_extensions = [ext.lower() for ext in settings.ALLOWED_FILE_EXTENSIONS]
        
    async def initialize(self) -> None:
        """Initialize S3 client."""
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=self.region
            )
            # Verify connection
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            self.logger.info(f"File service initialized with S3 bucket: {self.bucket_name}")
        except Exception as e:
            self.logger.error(f"Failed to initialize file service: {str(e)}")
            raise
    
    def _generate_s3_key(self, submission_id: str, filename: str) -> str:
        """Generate S3 key for file following PRD structure."""
        # Clean filename for S3 (remove spaces and special chars)
        clean_filename = filename.replace(" ", "_").replace("/", "_")
        # Follow PRD structure: submissions/{submissionId}/files/{userFilename}
        return f"submissions/{submission_id}/files/{clean_filename}"
    
    async def validate_file_upload(
        self,
        filename: str,
        mime_type: str,
        file_size: int,
        question_validation: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Validate file upload against system and question-specific rules."""
        errors = []
        
        # Check file size
        max_size = self.max_file_size_bytes
        if question_validation and question_validation.get("max"):
            # Question-specific max size in MB
            max_size = min(max_size, int(question_validation["max"]) * 1024 * 1024)
        
        if file_size > max_size:
            max_mb = max_size / (1024 * 1024)
            errors.append(f"File size ({file_size / (1024 * 1024):.1f}MB) exceeds maximum allowed size ({max_mb:.1f}MB)")
        
        # Check file extension
        file_ext = "." + filename.split(".")[-1].lower() if "." in filename else ""
        allowed_exts = self.allowed_extensions
        
        if question_validation and question_validation.get("regex"):
            # Question-specific allowed extensions
            question_exts = [ext.strip().lower() for ext in question_validation["regex"].split(",")]
            # Ensure extensions start with dot
            question_exts = [ext if ext.startswith(".") else f".{ext}" for ext in question_exts]
            allowed_exts = question_exts
        
        if file_ext not in allowed_exts:
            errors.append(f"File type '{file_ext}' not allowed. Supported formats: {', '.join(allowed_exts)}")
        
        # Basic filename validation
        if not filename or len(filename) > 255:
            errors.append("Invalid filename")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    async def generate_presigned_upload_url(
        self,
        submission_id: str,
        question_id: str,
        original_filename: str,
        mime_type: str,
        file_size: int,
        uploaded_by_user_id: str,
        uploaded_by_email: str,
        org_id: str,
        access_type: str = "public_user"
    ) -> Dict[str, Any]:
        """Generate a presigned URL for file upload."""
        try:
            if not self.s3_client:
                await self.initialize()
            
            # Generate S3 key
            s3_key = self._generate_s3_key(submission_id, original_filename)
            
            # Create file record
            file_record = SubmissionFile(
                submission_id=ObjectIdField(submission_id),
                question_id=ObjectIdField(question_id),
                org_id=ObjectIdField(org_id),
                uploaded_by_user_id=ObjectIdField(uploaded_by_user_id),
                uploaded_by_email=uploaded_by_email,
                access_type=FileAccessType(access_type),
                original_filename=original_filename,
                s3_key=s3_key,
                s3_bucket=self.bucket_name,
                mime_type=mime_type,
                file_size=file_size,
                status=FileStatus.UPLOADING
            )
            
            await file_record.save()
            
            # Generate presigned URL for PUT operation
            presigned_url = self.s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key,
                    'ContentType': mime_type,
                    'ContentLength': file_size
                },
                ExpiresIn=self.presigned_url_expiry
            )
            
            self.logger.info(f"Generated presigned upload URL for file {file_record.id}")
            
            return {
                "presigned_url": presigned_url,
                "s3_key": s3_key,
                "file_id": str(file_record.id),
                "expires_in": self.presigned_url_expiry,
                "upload_fields": {
                    "Content-Type": mime_type,
                    "Content-Length": str(file_size)
                }
            }
            
        except Exception as e:
            await self.handle_error(e, {
                "submission_id": submission_id,
                "question_id": question_id,
                "filename": original_filename
            })
    
    async def confirm_file_upload(
        self,
        file_id: str,
        checksum: Optional[str] = None
    ) -> Optional[SubmissionFile]:
        """Confirm that a file has been successfully uploaded to S3."""
        try:
            file_record = await SubmissionFile.find_one({"_id": ObjectIdField(file_id)})
            if not file_record:
                return None
            
            # Verify file exists in S3
            try:
                self.s3_client.head_object(Bucket=self.bucket_name, Key=file_record.s3_key)
            except botocore.exceptions.ClientError as e:
                if e.response['Error']['Code'] == '404':
                    file_record.mark_error("File not found in S3")
                    await file_record.save(is_update=True)
                    return file_record
                raise
            
            # Update file status
            file_record.mark_uploaded()
            if checksum:
                file_record.checksum = checksum
            file_record.mark_ready()
            
            await file_record.save(is_update=True)
            
            self.logger.info(f"Confirmed upload for file {file_id}")
            return file_record
            
        except Exception as e:
            await self.handle_error(e, {"file_id": file_id})
    
    async def generate_presigned_download_url(
        self,
        file_id: str,
        accessed_by_email: str,
        access_type: str,
        submission_status: str = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate a presigned URL for file download."""
        try:
            file_record = await SubmissionFile.find_one({"_id": ObjectIdField(file_id)})
            if not file_record:
                raise ValueError("File not found")
            
            # Check access permissions
            if not file_record.can_be_accessed_by(accessed_by_email, access_type, submission_status):
                await self.log_file_access(
                    file_id, str(file_record.submission_id), str(file_record.org_id),
                    accessed_by_email, access_type, "download", False,
                    "Access denied", ip_address, user_agent
                )
                raise PermissionError("Access denied")
            
            # Generate presigned URL for GET operation
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': file_record.s3_key,
                    'ResponseContentDisposition': f'attachment; filename="{file_record.original_filename}"'
                },
                ExpiresIn=self.presigned_url_expiry
            )
            
            # Track access
            file_record.track_access(accessed_by_email)
            await file_record.save(is_update=True)
            
            # Log access
            await self.log_file_access(
                file_id, str(file_record.submission_id), str(file_record.org_id),
                accessed_by_email, access_type, "download", True,
                None, ip_address, user_agent
            )
            
            self.logger.info(f"Generated presigned download URL for file {file_id}")
            
            return {
                "presigned_url": presigned_url,
                "filename": file_record.original_filename,
                "file_size": file_record.file_size,
                "mime_type": file_record.mime_type,
                "expires_in": self.presigned_url_expiry
            }
            
        except Exception as e:
            await self.handle_error(e, {"file_id": file_id, "accessed_by": accessed_by_email})

    async def delete_file(
        self,
        file_id: str,
        deleted_by_email: str,
        access_type: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """Delete a file (soft delete)."""
        try:
            file_record = await SubmissionFile.find_one({"_id": ObjectIdField(file_id)})
            if not file_record:
                return False

            # Check if user can delete (only original uploader or org users)
            if access_type == "investor":
                await self.log_file_access(
                    file_id, str(file_record.submission_id), str(file_record.org_id),
                    deleted_by_email, access_type, "delete", False,
                    "Investors cannot delete files", ip_address, user_agent
                )
                return False

            if access_type == "public_user" and file_record.uploaded_by_email != deleted_by_email:
                await self.log_file_access(
                    file_id, str(file_record.submission_id), str(file_record.org_id),
                    deleted_by_email, access_type, "delete", False,
                    "Can only delete own files", ip_address, user_agent
                )
                return False

            # Soft delete the file
            file_record.soft_delete()
            await file_record.save(is_update=True)

            # Log deletion
            await self.log_file_access(
                file_id, str(file_record.submission_id), str(file_record.org_id),
                deleted_by_email, access_type, "delete", True,
                None, ip_address, user_agent
            )

            self.logger.info(f"Deleted file {file_id} by {deleted_by_email}")
            return True

        except Exception as e:
            await self.handle_error(e, {"file_id": file_id, "deleted_by": deleted_by_email})
            return False

    async def copy_s3_object(
        self,
        source_bucket: str,
        source_key: str,
        dest_bucket: str,
        dest_key: str
    ) -> bool:
        """Copy an object from one S3 location to another."""
        try:
            copy_source = {
                'Bucket': source_bucket,
                'Key': source_key
            }

            self.s3_client.copy_object(
                CopySource=copy_source,
                Bucket=dest_bucket,
                Key=dest_key
            )

            self.logger.info(f"Successfully copied S3 object from {source_bucket}/{source_key} to {dest_bucket}/{dest_key}")
            return True

        except Exception as e:
            self.logger.error(f"Error copying S3 object: {str(e)}")
            return False

    async def delete_s3_object(
        self,
        bucket: str,
        key: str
    ) -> bool:
        """Delete an object from S3."""
        try:
            self.s3_client.delete_object(
                Bucket=bucket,
                Key=key
            )

            self.logger.info(f"Successfully deleted S3 object: {bucket}/{key}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting S3 object: {str(e)}")
            return False

    async def upload_text_to_s3(
        self,
        bucket: str,
        key: str,
        content: str,
        content_type: str = "text/plain"
    ) -> bool:
        """Upload text content to S3."""
        try:
            self.s3_client.put_object(
                Bucket=bucket,
                Key=key,
                Body=content.encode('utf-8'),
                ContentType=content_type
            )

            self.logger.info(f"Successfully uploaded text to S3: {bucket}/{key}")
            return True

        except Exception as e:
            self.logger.error(f"Error uploading text to S3: {str(e)}")
            return False

    async def delete_file_by_s3_key(
        self,
        s3_key: str,
        deleted_by_email: str,
        access_type: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """Delete a file by S3 key (soft delete)."""
        try:
            file_record = await SubmissionFile.find_one({"s3_key": s3_key})
            if not file_record:
                return False

            # Check if user can delete (only original uploader or org users)
            if access_type == "investor":
                await self.log_file_access(
                    str(file_record.id), str(file_record.submission_id), str(file_record.org_id),
                    deleted_by_email, access_type, "delete", False,
                    "Investors cannot delete files", ip_address, user_agent
                )
                return False

            if access_type == "public_user" and file_record.uploaded_by_email != deleted_by_email:
                await self.log_file_access(
                    str(file_record.id), str(file_record.submission_id), str(file_record.org_id),
                    deleted_by_email, access_type, "delete", False,
                    "Can only delete own files", ip_address, user_agent
                )
                return False

            # Soft delete the file
            file_record.soft_delete()
            await file_record.save(is_update=True)

            # Log deletion
            await self.log_file_access(
                str(file_record.id), str(file_record.submission_id), str(file_record.org_id),
                deleted_by_email, access_type, "delete", True,
                None, ip_address, user_agent
            )

            self.logger.info(f"Deleted file {file_record.id} (s3_key: {s3_key}) by {deleted_by_email}")
            return True

        except Exception as e:
            await self.handle_error(e, {"s3_key": s3_key, "deleted_by": deleted_by_email})
            return False

    async def list_submission_files(
        self,
        submission_id: str,
        question_id: Optional[str] = None,
        accessed_by_email: str = None,
        access_type: str = None
    ) -> List[Dict[str, Any]]:
        """List files for a submission."""
        try:
            query = {
                "submission_id": ObjectIdField(submission_id),
                "is_active": True,
                "status": {"$in": [FileStatus.UPLOADED, FileStatus.READY]}
            }

            if question_id:
                query["question_id"] = ObjectIdField(question_id)

            files = await SubmissionFile.find(query).to_list()

            result = []
            for file_record in files:
                # Check access permissions if user info provided
                if accessed_by_email and access_type:
                    if not file_record.can_be_accessed_by(accessed_by_email, access_type):
                        continue

                result.append({
                    "id": str(file_record.id),
                    "original_filename": file_record.original_filename,
                    "file_size": file_record.file_size,
                    "display_size": file_record.display_size,
                    "mime_type": file_record.mime_type,
                    "file_extension": file_record.file_extension,
                    "question_id": str(file_record.question_id),
                    "uploaded_at": file_record.created_at,
                    "uploaded_by": file_record.uploaded_by_email,
                    "download_count": file_record.download_count,
                    "last_accessed_at": file_record.last_accessed_at
                })

            return result

        except Exception as e:
            await self.handle_error(e, {"submission_id": submission_id})
            return []

    async def get_file_by_id(
        self,
        file_id: str,
        accessed_by_email: str = None,
        access_type: str = None
    ) -> Optional[SubmissionFile]:
        """Get file by ID with access control."""
        try:
            file_record = await SubmissionFile.find_one({"_id": ObjectIdField(file_id)})
            if not file_record or not file_record.is_active:
                return None

            # Check access permissions if user info provided
            if accessed_by_email and access_type:
                if not file_record.can_be_accessed_by(accessed_by_email, access_type):
                    return None

            return file_record

        except Exception as e:
            await self.handle_error(e, {"file_id": file_id})
            return None

    async def log_file_access(
        self,
        file_id: str,
        submission_id: str,
        org_id: str,
        accessed_by_email: str,
        access_type: str,
        action: str,
        success: bool,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> FileAccessLog:
        """Log file access for audit purposes."""
        try:
            log_entry = FileAccessLog(
                file_id=ObjectIdField(file_id),
                submission_id=ObjectIdField(submission_id),
                org_id=ObjectIdField(org_id),
                accessed_by_email=accessed_by_email,
                access_type=FileAccessType(access_type),
                action=action,
                success=success,
                error_message=error_message,
                ip_address=ip_address,
                user_agent=user_agent
            )

            await log_entry.save()
            return log_entry

        except Exception as e:
            self.logger.error(f"Failed to log file access: {str(e)}")
            # Don't re-raise as logging failure shouldn't break the main operation
            return None

    async def cleanup_orphaned_files(self, max_age_hours: int = 24) -> int:
        """Clean up orphaned files that were never confirmed as uploaded."""
        try:
            cutoff_time = int(datetime.now(timezone.utc).timestamp()) - (max_age_hours * 3600)

            # Find orphaned files (still in UPLOADING status and old)
            orphaned_files = await SubmissionFile.find({
                "status": FileStatus.UPLOADING,
                "created_at": {"$lt": cutoff_time}
            }).to_list()

            cleanup_count = 0
            for file_record in orphaned_files:
                try:
                    # Try to delete from S3 (may not exist)
                    try:
                        self.s3_client.delete_object(Bucket=self.bucket_name, Key=file_record.s3_key)
                    except botocore.exceptions.ClientError:
                        pass  # File may not exist in S3

                    # Soft delete the record
                    file_record.soft_delete()
                    await file_record.save(is_update=True)
                    cleanup_count += 1

                except Exception as e:
                    self.logger.error(f"Failed to cleanup orphaned file {file_record.id}: {str(e)}")

            self.logger.info(f"Cleaned up {cleanup_count} orphaned files")
            return cleanup_count

        except Exception as e:
            await self.handle_error(e, {"max_age_hours": max_age_hours})
            return 0
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        try:
            # Close S3 client connection if needed
            if hasattr(self, 's3_client') and self.s3_client:
                # boto3 clients don't need explicit cleanup but we can reset it
                self.s3_client = None
            self.logger.info("File service cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during file service cleanup: {str(e)}")
