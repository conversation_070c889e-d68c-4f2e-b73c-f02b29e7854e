"""
PDF Parser Service Implementation

Handles PDF text extraction using PyMuPDF with OCR fallback.
"""

import fitz  # PyMuPDF
import boto3
from io import Bytes<PERSON>
from typing import Dict, Any, List, Optional
import re

from app.core.config import settings
from app.core.logging import get_logger
from app.services.base import BaseService
from app.services.pdf_parser.interface import PDFParserInterface

logger = get_logger(__name__)


class PDFParserService(BaseService, PDFParserInterface):
    """PDF parser service using PyMuPDF for text extraction."""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.s3_client = None
        
    async def initialize(self) -> None:
        """Initialize the PDF parser service."""
        try:
            # Initialize S3 client for fetching PDFs
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION
            )
            self.logger.info("PDF parser service initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize PDF parser service: {str(e)}")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        try:
            if hasattr(self, 's3_client') and self.s3_client:
                self.s3_client = None
            self.logger.info("PDF parser service cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during PDF parser service cleanup: {str(e)}")
    
    async def extract_text_from_pdf(
        self, 
        pdf_content: BytesIO,
        filename: str
    ) -> Dict[str, Any]:
        """Extract text from PDF content using PyMuPDF."""
        try:
            # Open PDF from BytesIO
            pdf_document = fitz.open(stream=pdf_content.getvalue(), filetype="pdf")
            
            pages = []
            full_text = ""
            
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                
                # Extract text
                page_text = page.get_text()
                
                # If no text found, try OCR (basic fallback)
                if not page_text.strip():
                    # For now, we'll just note that OCR might be needed
                    page_text = f"[Page {page_num + 1}: Image-based content detected]"
                    self.logger.warning(f"Page {page_num + 1} appears to be image-based, OCR might be needed")
                
                pages.append({
                    "page_number": page_num + 1,
                    "text": page_text.strip()
                })
                full_text += f"\n\n--- Page {page_num + 1} ---\n{page_text}"
            
            # Get PDF metadata
            metadata = pdf_document.metadata
            
            pdf_document.close()
            
            result = {
                "text": full_text.strip(),
                "pages": pages,
                "page_count": len(pages),
                "metadata": {
                    "title": metadata.get("title", ""),
                    "author": metadata.get("author", ""),
                    "subject": metadata.get("subject", ""),
                    "creator": metadata.get("creator", ""),
                    "producer": metadata.get("producer", ""),
                    "creation_date": metadata.get("creationDate", ""),
                    "modification_date": metadata.get("modDate", "")
                },
                "filename": filename
            }
            
            self.logger.info(f"Successfully extracted text from PDF: {filename} ({len(pages)} pages)")
            return result
            
        except Exception as e:
            self.logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            raise
    
    async def extract_text_from_s3(
        self,
        s3_bucket: str,
        s3_key: str
    ) -> Dict[str, Any]:
        """Extract text from PDF stored in S3."""
        try:
            if not self.s3_client:
                await self.initialize()
            
            # Download PDF from S3
            response = self.s3_client.get_object(Bucket=s3_bucket, Key=s3_key)
            pdf_content = BytesIO(response['Body'].read())
            
            # Extract filename from S3 key
            filename = s3_key.split('/')[-1]
            
            # Extract text
            result = await self.extract_text_from_pdf(pdf_content, filename)
            
            # Add S3 metadata
            result["s3_metadata"] = {
                "bucket": s3_bucket,
                "key": s3_key,
                "size": response.get('ContentLength', 0),
                "last_modified": response.get('LastModified'),
                "content_type": response.get('ContentType', '')
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error extracting text from S3 PDF {s3_bucket}/{s3_key}: {str(e)}")
            raise
    
    def detect_pitch_type(self, page_count: int, text_content: str) -> str:
        """
        Detect if the document is a deck or one-pager.
        
        Uses heuristics based on page count and content patterns.
        """
        try:
            # Simple heuristic: 1-2 pages = one_pager, 3+ pages = deck
            if page_count <= 2:
                return "one_pager"
            else:
                return "deck"
                
        except Exception as e:
            self.logger.error(f"Error detecting pitch type: {str(e)}")
            # Default to deck if detection fails
            return "deck"
    
    def _clean_text(self, text: str) -> str:
        """Clean extracted text by removing excessive whitespace and formatting."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page breaks and form feeds
        text = re.sub(r'[\f\r]+', '\n', text)
        
        # Remove excessive newlines
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        return text.strip()
    
    def _extract_key_sections(self, text: str, pages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract key sections from the text for better LLM processing.
        
        This helps identify common pitch deck sections.
        """
        sections = {}
        
        # Common section headers to look for
        section_patterns = {
            "problem": r"(?i)(problem|challenge|pain\s+point)",
            "solution": r"(?i)(solution|our\s+solution|approach)",
            "market": r"(?i)(market|market\s+size|tam|addressable\s+market)",
            "traction": r"(?i)(traction|growth|metrics|revenue|customers)",
            "team": r"(?i)(team|founders|about\s+us|leadership)",
            "ask": r"(?i)(ask|funding|investment|raise|seeking)",
            "financials": r"(?i)(financials|revenue|projections|forecast)"
        }
        
        for section_name, pattern in section_patterns.items():
            matches = re.finditer(pattern, text)
            for match in matches:
                # Find the page containing this section
                char_pos = match.start()
                page_num = self._find_page_for_position(char_pos, pages)
                
                sections[section_name] = {
                    "found": True,
                    "page": page_num,
                    "position": char_pos
                }
                break
        
        return sections
    
    def _find_page_for_position(self, char_pos: int, pages: List[Dict[str, Any]]) -> int:
        """Find which page contains a specific character position in the full text."""
        current_pos = 0
        
        for page in pages:
            page_text = page.get("text", "")
            if current_pos <= char_pos < current_pos + len(page_text):
                return page["page_number"]
            current_pos += len(page_text) + 20  # Account for page separators
        
        return 1  # Default to first page if not found
