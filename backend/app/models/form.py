from datetime import datetime, timezone
from enum import Enum
from typing import Annotated, Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field

from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.user import User
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class CoreFieldType(str, Enum):
    """Core field types that are required for deal creation."""

    # Basic fields
    COMPANY_NAME = "company_name"
    STAGE = "stage"
    SECTOR = "sector"
    COMPANY_WEBSITE = "company_website"

    # Founder fields
    FOUNDER_NAME = "founder_name"
    FOUNDER_ROLE = "founder_role"
    FOUNDER_LINKEDIN = "founder_linkedin"


class QuestionType(str, Enum):
    SHORT_TEXT = "short_text"
    LONG_TEXT = "long_text"
    NUMBER = "number"
    RANGE = "range"
    SINGLE_SELECT = "single_select"
    MULTI_SELECT = "multi_select"
    BOOLEAN = "boolean"
    FILE = "file"
    DATE = "date"


class ValidationRule(TractionXModel):
    """
    Defines validation rules (min/max, regex, custom) for form fields or questions.
    """

    min: Optional[float] = None
    max: Optional[float] = None
    regex: Optional[str] = None
    required_if: Optional[Dict[str, Any]] = None
    custom: Optional[str] = None


class ConditionClause(BaseModel):
    """
    Represents a single condition clause within a visibility condition.
    Used to determine when a question or section should be visible.
    """

    question_id: str = Field(
        ..., description="ID of the question whose answer will be evaluated"
    )
    value: Any = Field(
        ..., description="Value to compare against the question's answer"
    )
    section_instance_index: Optional[int] = Field(
        None, description="For repeated sections, the specific instance index to target"
    )

    model_config = ConfigDict(
        extra_model_config={
            "json_schema_extra": {
                "example": {
                    "question_id": "q123",
                    "value": "yes",
                    "section_instance_index": 0,
                }
            }
        }
    )  # type: ignore


class VisibilityCondition(TractionXModel):
    """
    Defines when a question should be visible based on other answers or conditions.
    Supports complex logical operations and targeting specific instances of repeated sections.
    """

    operator: Literal["and", "or", "not", "==", "!=", ">", "<", ">=", "<="] = Field(
        ..., description="Logical operator for condition evaluation", example="=="
    )  # type: ignore
    conditions: List[Union[Dict[str, Any], "ConditionClause"]] = Field(
        ..., description="List of conditions to evaluate"
    )

    model_config = ConfigDict(
        extra_model_config={
            "json_schema_extra": {
                "example": {
                    "operator": "==",
                    "conditions": [{"question_id": "q123", "value": "yes"}],
                }
            }
        }
    )  # type: ignore


class Question(TractionXModel):
    """
    Represents a question in a form, including type, options, validation, and visibility.

    Enhanced with support for:
    - Repeatable section embedding
    - Advanced visibility conditions
    - Core field identification for deal creation
    """

    model_config = ConfigDict(
        extra_model_config={
            "json_schema_extra": {
                "example": {
                    "type": "single_select",
                    "label": "What is your preferred contact method?",
                    "help_text": "Select how you would like to be contacted",
                    "required": True,
                    "core_field": "company_name",  # Optional core field type
                    "options": [
                        {"value": "email", "label": "Email"},
                        {"value": "phone", "label": "Phone"},
                        {"value": "mail", "label": "Mail"},
                    ],
                    "visibility_condition": {
                        "operator": "==",
                        "conditions": [{"question_id": "q123", "value": "yes"}],
                    },
                    "repeat_section_id": "section123",
                    "max_repeats": 5,
                }
            }
        }
    )  # type: ignore

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    section_id: Annotated[ObjectIdField, populate_reference("Section")]
    type: QuestionType
    label: str
    help_text: Optional[str] = None
    required: bool = False
    core_field: Optional[CoreFieldType] = Field(
        None, description="Identifies if this is a core field for deal creation"
    )
    # Changed to support label/value pairs
    options: Optional[List[Dict[str, str]]] = None
    validation: Optional[ValidationRule] = None
    visibility_condition: Optional[VisibilityCondition] = None
    # New fields for repeatable section embedding
    repeat_section_id: Optional[ObjectIdField] = None
    max_repeats: Optional[int] = None
    order: int
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    def validate_core_field(self) -> None:
        """Validate that core field questions have appropriate types and options."""
        if not self.core_field:
            return

        if self.core_field == CoreFieldType.COMPANY_NAME:
            if self.type != QuestionType.SHORT_TEXT:
                raise ValueError("Company name core field must be of type short_text")
        elif self.core_field == CoreFieldType.STAGE:
            if self.type != QuestionType.SINGLE_SELECT:
                raise ValueError("Stage core field must be of type single_select")
            if not self.options:
                raise ValueError("Stage core field must have options defined")
        elif self.core_field == CoreFieldType.SECTOR:
            if self.type not in [QuestionType.SINGLE_SELECT, QuestionType.MULTI_SELECT]:
                raise ValueError(
                    "Sector core field must be of type single_select or multi_select"
                )
            if not self.options:
                raise ValueError("Sector core field must have options defined")

    def validate_visibility_condition(
        self, all_questions: Optional[List["Question"]] = None
    ) -> None:
        """
        Validate that visibility conditions only reference Boolean and MCQ question types.
        This helps ensure conditional visibility works reliably.
        """
        if not self.visibility_condition or not all_questions:
            return

        # Question types that support reliable conditional visibility
        allowed_types = {
            QuestionType.BOOLEAN,
            QuestionType.SINGLE_SELECT,
            QuestionType.MULTI_SELECT,
        }

        # Create a map of question IDs to their types
        question_type_map = {
            str(q.id): q.type for q in all_questions if hasattr(q, "type")
        }

        # Check each condition
        for condition in self.visibility_condition.conditions:
            if isinstance(condition, dict):
                referenced_question_id = condition.get("question_id")
            else:
                referenced_question_id = getattr(condition, "question_id", None)

            if referenced_question_id:
                referenced_question_type = question_type_map.get(
                    str(referenced_question_id)
                )

                if (
                    referenced_question_type
                    and referenced_question_type not in allowed_types
                ):
                    raise ValueError(
                        f"Visibility condition references question {referenced_question_id} "
                        f"of type {referenced_question_type}. Only Boolean, single_select, "
                        f"and multi_select questions can be used in visibility conditions."
                    )


class Section(TractionXModel):
    """
    Represents a section within a form, containing questions and metadata.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    form_id: Annotated[ObjectIdField, populate_reference("Form")]
    title: str
    description: Optional[str] = None
    order: int
    questions: Annotated[List[ObjectIdField], populate_reference("Question")] = Field(
        default_factory=list
    )
    repeatable: bool = False
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )


class SectionWithQuestions(Section):
    questions: List["Question"] = []


class Form(TractionXModel):
    """
    Represents a form, including sections, versioning, and organizational context.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: Annotated[ObjectIdField, populate_reference("Organization")]
    name: str
    description: str
    version: int
    is_active: bool = True
    sections: Annotated[List[ObjectIdField], populate_reference("Section")] = Field(
        default_factory=list
    )
    default_section_ids: Annotated[
        List[ObjectIdField], populate_reference("Section")
    ] = Field(default_factory=list)
    created_by: Annotated[ObjectIdField, populate_reference(User)] = Field(
        default_factory=lambda: PyObjectId("680fc5c6980ce9ae38ac6729"),
        description="ID of the user who created the form (defaults to system user)",
    )
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )


class FormWithDetails(TractionXModel):
    """
    Represents a detailed view of a form, including all sections and questions.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    name: str
    description: str
    version: int
    is_active: bool
    sections: List[SectionWithQuestions]
    default_section_ids: List[ObjectIdField]
    created_by: Annotated[ObjectIdField, populate_reference(User)]
    created_at: int
    updated_at: int


class Submission(TractionXModel):
    """
    Represents a submission to a form, including answers and metadata.

    Enhanced to support:
    - Structured answers for repeatable sections
    - Custom metadata for tracking submission context
    - Submission status tracking
    - Job tracking for asynchronous processing

    The answers field can be either:
    1. A flat dictionary of question_id -> answer (backward compatibility)
    2. A structured format with:
       - answers: Dict[str, Any] - Regular question answers
       - repeatable_answers: Dict[str, Dict[str, Dict[str, Any]]] - Repeatable section answers
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: Annotated[ObjectIdField, populate_reference(Organization)]
    form_id: Annotated[ObjectIdField, populate_reference(Form)]
    answers: Dict[str, Any] = Field(
        ..., description="Answer data, can be flat or structured format"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Optional metadata for the submission"
    )
    status: str = Field(
        default="submitted",
        description="Status of the submission (submitted, in_review, approved, rejected)",
    )
    active_jobs: List[ObjectIdField] = Field(
        default_factory=list,
        description="List of active job IDs associated with this submission",
    )
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
