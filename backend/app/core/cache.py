from typing import AsyncGenerator, Optional, Any
import json

import redis.asyncio as redis

from app.core.config import settings


class RedisCache:
    client: redis.Redis = None

    async def connect_to_cache(self) -> None:
        """Create Redis connection."""
        self.client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
        )

    async def close_cache_connection(self) -> None:
        """Close Redis connection."""
        if self.client:
            await self.client.close()

    async def get_cache(self) -> AsyncGenerator[redis.Redis, None]:
        """Get Redis instance."""
        if not self.client:
            await self.connect_to_cache()
        yield self.client

    async def get(self, key: str, namespace: Optional[str] = None) -> Optional[Any]:
        """Get value by key."""
        if not self.client:
            await self.connect_to_cache()
        value = await self.client.get(key)
        if value:
            return json.loads(value)
        return None

    async def set(
        self,
        key: str,
        value: Any,
        namespace: Optional[str] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value for key with optional TTL."""
        if not self.client:
            await self.connect_to_cache()
        value_str = json.dumps(value)
        if ttl:
            await self.client.setex(key, ttl, value_str)
        else:
            await self.client.set(key, value_str)
        return True

    async def delete(self, key: str, namespace: Optional[str] = None) -> bool:
        """Delete key from Redis."""
        if not self.client:
            await self.connect_to_cache()
        result = await self.client.delete(key)
        return bool(result)


cache = RedisCache()
