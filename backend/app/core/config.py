from typing import Optional, Set

from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env.dev", env_file_encoding="utf-8", case_sensitive=True
    )

    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "TX-App"
    VERSION: str = "0.1.0"
    DESCRIPTION: str = "Investing intelligence platform backend"

    # Security
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    TEST_MODE: bool = False  # When True, bypasses all auth, RBAC, and tenant checks
    INVITATION_TOKEN_EXPIRY: int
    PASSWORD_RESET_TOKEN_EXPIRY: int
    # # ORIGIN
    # ALLOWED_ORIGINS: str = "http://localhost:8080"

    # CORS
    BACKEND_CORS_ORIGINS: list[str] = [
        "http://localhost:3000",  # Next.js dev server
        "http://localhost:8080",  # Alternative frontend port
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
        "https://tractionx.ai",  # Production domain
        "https://app.tractionx.ai",  # Production app domain
        "https://v1.tractionx.ai",
        "https://v1.tractionx.ai/",  # Production app domain
    ]

    # MongoDB
    MONGODB_URL: str
    MONGODB_DB_NAME: str

    # Redis
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: str
    REDIS_KEY_PREFIX: str
    REDIS_DEFAULT_TTL: int = 3600  # 1 hour in seconds

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    # RDS
    DB_HOST: str
    DB_PORT: int
    DB_NAME: str
    DB_USER: str
    DB_PASSWORD: str
    DB_POOL_SIZE: int
    DB_MAX_OVERFLOW: int

    # Reset token expiry
    RESET_TOKEN_EXPIRY: int = 120

    # Organization Settings
    DOMAIN: str = "tractionx.ai"  # Default domain for organization subdomains
    ALLOWED_HOSTS: list[str] = ["localhost", "127.0.0.1", "tractionx.ai"]

    # Portal URLs
    # PRO_PORTAL_URL: str = "https://pro.tractionx.com"
    BASIC_PORTAL_URL: str = "https://v1.tractionx.ai"

    # Email Settings
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = "<EMAIL>"
    SMTP_PASSWORD: str = "your_app_password"
    SMTP_FROM: str = "<EMAIL>"
    SMTP_FROM_NAME: str = "TractionX"
    FRONTEND_URL: str

    # Resend Email Service
    RESEND_API_KEY: str
    USE_RESEND: bool = True  # Set to True to use Resend instead of SMTP
    RESEND_DOMAIN: str

    # S3 File Storage Settings
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    S3_BUCKET_SUBMISSIONS: str
    S3_BUCKET_ASSETS: str
    S3_PRESIGNED_URL_EXPIRY: int = 600  # 10 minutes in seconds

    # AI Services
    PERPLEXITY_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None

    # File Upload Limits
    MAX_FILE_SIZE_MB: int = 50  # Maximum file size in MB
    ALLOWED_FILE_EXTENSIONS: list[str] = [
        ".pdf",
        ".doc",
        ".docx",
        ".txt",
        ".rtf",  # Documents
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",  # Images
        ".xls",
        ".xlsx",
        ".csv",  # Spreadsheets
        ".ppt",
        ".pptx",  # Presentations
        ".zip",
        ".rar",
        ".7z",  # Archives
    ]

    # Worker
    WORKER_CONCURRENCY: int = 2
    DEBUG: bool = True
    WATCHDOG_ENABLED: int = 1

    # AI Services
    OPENAI_API_KEY: Optional[str] = None

    # Public Paths
    PUBLIC_PATHS: Set[str] = {
        "/docs",
        "/",
        "/redoc",
        "/openapi.json",
        "/favicon.ico",
        "/api/v1/login",
        "/api/v1/register",
        "/api/v1/refresh",
        "/api/v1/verify",
        "/api/v1/reset-password",
        "/api/v1/forgot-password",
        "/api/v1/accept-invitation",
        "/api/v1/magic-link",  # Magic link endpoints
        "/api/v1/magic-link/verify",
        "/api/v1/public",  # Public submission endpoints
        "/api/v1/public/login",
        "/api/v1/public/magic-link/verify",
        "/api/v1/public/file/presign-upload",
        "/api/v1/public/file/presign-download",
        "/api/v1/public/file/confirm-upload",
        "/api/v1/public/file/delete",
        "/api/v1/public/file",  # Public file endpoints
        "/health",
        "/share",
        "/s",
        "/embed",
        "/qr",
    }

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: str | list[str] | None) -> list[str]:
        if v is None:
            return [
                "http://localhost:3000",  # Next.js dev server
                "http://localhost:8080",  # Alternative frontend port
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8080",
                "https://tractionx.ai",  # Production domain
                "https://app.tractionx.ai",  # Production app domain
                "https://v1.tractionx.ai",  # Production app domain
            ]
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v  # type: ignore
        raise ValueError(v)

    @property
    def mongodb_connection_string(self) -> str:
        return f"{self.MONGODB_URL}/{self.MONGODB_DB_NAME}"

    @property
    def redis_connection_string(self) -> str:
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"


settings = Settings()  # type: ignore
