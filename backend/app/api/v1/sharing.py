from typing import Dict, Any, List, Optional
from fastapi import Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.services.sharing.interfaces import SharingServiceInterface, SharingType, EmbedType
from app.services.factory import get_sharing_service
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.utils.rbac.rbac import rbac_register
from app.models.user import User

router = BaseAPIRouter(prefix="/sharing", tags=["sharing"])


class CreateSharingConfigRequest(BaseModel):
    resource_type: str = Field(..., description="Type of resource (e.g., 'form', 'report')")
    resource_id: str = Field(..., description="ID of the resource")
    enabled: bool = Field(default=True, description="Whether sharing is enabled")
    sharing_types: List[SharingType] = Field(
        default=[SharingType.LINK],
        description="List of sharing types to enable"
    )
    allowed_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains allowed for embedding"
    )
    embed_type: EmbedType = Field(
        default=EmbedType.INLINE,
        description="Type of embed"
    )
    custom_styles: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Custom styles for embedding"
    )
    tracking_enabled: bool = Field(
        default=True,
        description="Whether to track views/usage"
    )
    expires_at: Optional[int] = Field(
        default=None,
        description="When the sharing configuration expires (unix timestamp)"
    )


class UpdateSharingConfigRequest(BaseModel):
    enabled: Optional[bool] = Field(default=None, description="Whether sharing is enabled")
    sharing_types: Optional[List[SharingType]] = Field(
        default=None,
        description="List of sharing types to enable"
    )
    allowed_domains: Optional[List[str]] = Field(
        default=None,
        description="List of domains allowed for embedding"
    )
    embed_type: Optional[EmbedType] = Field(
        default=None,
        description="Type of embed"
    )
    custom_styles: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Custom styles for embedding"
    )
    tracking_enabled: Optional[bool] = Field(
        default=None,
        description="Whether to track views/usage"
    )
    expires_at: Optional[int] = Field(
        default=None,
        description="When the sharing configuration expires (unix timestamp)"
    )


class GenerateSharingLinkRequest(BaseModel):
    expires_at: Optional[int] = Field(
        default=None,
        description="When the link expires (unix timestamp)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata for the link"
    )


class GenerateEmbedCodeRequest(BaseModel):
    embed_type: Optional[EmbedType] = Field(
        default=None,
        description="Type of embed (overrides config if provided)"
    )
    custom_styles: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Custom styles (overrides config if provided)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata for the embed code"
    )


class GenerateQRCodeRequest(BaseModel):
    size: int = Field(
        default=300,
        description="Size of the QR code in pixels"
    )
    error_correction_level: str = Field(
        default="M",
        description="Error correction level (L, M, Q, H)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata for the QR code"
    )


class TrackViewRequest(BaseModel):
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata for the view"
    )


@router.get("/configs")
@rbac_register(resource="sharing", action="view", group="Sharing", description="List sharing configurations")
async def list_sharing_configs(
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    sharing_service: SharingServiceInterface = Depends(get_sharing_service)
) -> List[Dict[str, Any]]:
    """
    List sharing configurations for the organization.
    """
    org_id, is_cross_org = org_context

    try:
        configs = await sharing_service.list_sharing_configs(
            org_id=org_id,
            resource_type=resource_type,
            resource_id=resource_id
        )
        return configs
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list sharing configurations: {str(e)}"
        )


@router.post("/configs", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="sharing", action="create", group="Sharing", description="Create sharing configuration")
async def create_sharing_config(
    request: CreateSharingConfigRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    sharing_service: SharingServiceInterface = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Create a sharing configuration for a resource.
    """
    org_id, is_cross_org = org_context

    try:
        config = await sharing_service.create_sharing_config(
            resource_type=request.resource_type,
            resource_id=request.resource_id,
            org_id=org_id,
            enabled=request.enabled,
            sharing_types=request.sharing_types,
            allowed_domains=request.allowed_domains,
            embed_type=request.embed_type,
            custom_styles=request.custom_styles,
            tracking_enabled=request.tracking_enabled,
            expires_at=request.expires_at
        )
        return config
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create sharing configuration: {str(e)}"
        )


@router.get("/configs/{config_id}")
@rbac_register(resource="sharing", action="view", group="Sharing", description="Get sharing configuration")
async def get_sharing_config(
    config_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Get sharing configuration by ID.
    """
    config = await sharing_service.get_sharing_config(config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sharing configuration not found"
        )
    return config


@router.put("/configs/{config_id}")
@rbac_register(resource="sharing", action="update", group="Sharing", description="Update sharing configuration")
async def update_sharing_config(
    config_id: str,
    request: UpdateSharingConfigRequest,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Update a sharing configuration.
    """
    # Filter out None values
    updates = {k: v for k, v in request.dict().items() if v is not None}

    if not updates:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No updates provided"
        )

    config = await sharing_service.update_sharing_config(config_id, updates)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sharing configuration not found"
        )
    return config


@router.delete("/configs/{config_id}")
@rbac_register(resource="sharing", action="delete", group="Sharing", description="Delete sharing configuration")
async def delete_sharing_config(
    config_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, str]:
    """
    Delete a sharing configuration.
    """
    success = await sharing_service.delete_sharing_config(config_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sharing configuration not found"
        )
    return {"status": "Sharing configuration deleted"}


@router.get("/configs/{config_id}/links")
@rbac_register(resource="sharing", action="view", group="Sharing", description="List sharing links")
async def list_sharing_links(
    config_id: str,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service)
) -> List[Dict[str, Any]]:
    """
    List sharing links for a configuration.
    """
    try:
        links = await sharing_service.list_sharing_links(config_id)
        return links
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list sharing links: {str(e)}"
        )


@router.post("/configs/{config_id}/links", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="sharing", action="create", group="Sharing", description="Generate sharing link")
async def generate_sharing_link(
    config_id: str,
    request: GenerateSharingLinkRequest,
    current_user: User = Depends(get_current_user),
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Generate a sharing link.
    """
    try:
        link = await sharing_service.generate_sharing_link(
            config_id=config_id,
            created_by=str(current_user.id),
            expires_at=request.expires_at,
            metadata=request.metadata
        )
        return link
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate sharing link: {str(e)}"
        )


@router.post("/configs/{config_id}/embeds", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="sharing", action="create", group="Sharing", description="Generate embed code")
async def generate_embed_code(
    config_id: str,
    request: GenerateEmbedCodeRequest,
    current_user: User = Depends(get_current_user),
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Generate an embed code.
    """
    try:
        embed = await sharing_service.generate_embed_code(
            config_id=config_id,
            created_by=str(current_user.id),
            embed_type=request.embed_type,
            custom_styles=request.custom_styles,
            metadata=request.metadata
        )
        return embed
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate embed code: {str(e)}"
        )


@router.post("/configs/{config_id}/qrcodes", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="sharing", action="create", group="Sharing", description="Generate QR code")
async def generate_qr_code(
    config_id: str,
    request: GenerateQRCodeRequest,
    current_user: User = Depends(get_current_user),
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Generate a QR code.
    """
    try:
        qr = await sharing_service.generate_qr_code(
            config_id=config_id,
            created_by=str(current_user.id),
            size=request.size,
            error_correction_level=request.error_correction_level,
            metadata=request.metadata
        )
        return qr
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate QR code: {str(e)}"
        )


@router.get("/links/{link_id}")
@rbac_register(resource="sharing", action="view", group="Sharing", description="Get sharing link")
async def get_sharing_link(
    link_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Get sharing link by ID.
    """
    link = await sharing_service.get_sharing_link(link_id)
    if not link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sharing link not found"
        )
    return link


@router.get("/embeds/{embed_id}")
@rbac_register(resource="sharing", action="view", group="Sharing", description="Get embed code")
async def get_embed_code(
    embed_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Get embed code by ID.
    """
    embed = await sharing_service.get_embed_code(embed_id)
    if not embed:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Embed code not found"
        )
    return embed


@router.get("/qrcodes/{qr_id}")
@rbac_register(resource="sharing", action="view", group="Sharing", description="Get QR code")
async def get_qr_code(
    qr_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Get QR code by ID.
    """
    qr = await sharing_service.get_qr_code(qr_id)
    if not qr:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="QR code not found"
        )
    return qr


@router.post("/links/{link_id}/track")
@rbac_register(resource="sharing", action="update", group="Sharing", description="Track link view")
async def track_link_view(
    link_id: str,
    request: TrackViewRequest,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, str]:
    """
    Track a view of a shared link.
    """
    success = await sharing_service.track_view(
        sharing_id=link_id,
        sharing_type=SharingType.LINK,
        metadata=request.metadata
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sharing link not found"
        )
    return {"status": "View tracked"}


@router.post("/embeds/{embed_id}/track")
@rbac_register(resource="sharing", action="update", group="Sharing", description="Track embed view")
async def track_embed_view(
    embed_id: str,
    request: TrackViewRequest,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, str]:
    """
    Track a view of an embed.
    """
    success = await sharing_service.track_view(
        sharing_id=embed_id,
        sharing_type=SharingType.EMBED,
        metadata=request.metadata
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Embed code not found"
        )
    return {"status": "View tracked"}


@router.post("/qrcodes/{qr_id}/track")
@rbac_register(resource="sharing", action="update", group="Sharing", description="Track QR code scan")
async def track_qr_scan(
    qr_id: str,
    request: TrackViewRequest,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, str]:
    """
    Track a scan of a QR code.
    """
    success = await sharing_service.track_view(
        sharing_id=qr_id,
        sharing_type=SharingType.QR_CODE,
        metadata=request.metadata
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="QR code not found"
        )
    return {"status": "Scan tracked"}


@router.get("/stats")
@rbac_register(resource="sharing", action="view", group="Sharing", description="Get sharing statistics")
async def get_sharing_stats(
    resource_type: str,
    resource_id: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Get sharing statistics for a resource.
    """
    return await sharing_service.get_sharing_stats(resource_type, resource_id)


@router.get("/validate/{token}")
async def validate_sharing_token(
    token: str,
    sharing_service = Depends(get_sharing_service)
) -> Dict[str, Any]:
    """
    Validate a sharing token.
    """
    result = await sharing_service.validate_sharing_token(token)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid or expired token"
        )
    return result
