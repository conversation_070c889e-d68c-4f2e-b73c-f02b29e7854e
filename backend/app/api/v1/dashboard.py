"""
Dashboard API Endpoints

Unified dashboard summary endpoint providing real-time data for all dashboard components.
"""

from typing import Tuple

from bson import ObjectId
from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.base import BaseAPIRouter
from app.core.database import get_database
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.deal import Deal
from app.models.form import Form
from app.models.thesis import InvestmentThesis
from app.models.user import User
from app.schemas.dashboard import DashboardSummaryResponse

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/dashboard", tags=["dashboard"])


@router.get("/summary", response_model=DashboardSummaryResponse)
async def get_dashboard_summary(
    current_user: User = Depends(get_current_user),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> DashboardSummaryResponse:
    """
    Get unified dashboard summary with real-time data.

    Returns:
    - Active deals count and percentage change
    - Forms and theses counts
    - AI activity status
    - Sector distribution
    - Deal stages distribution
    - Onboarding status
    """
    try:
        org_id, _ = org_context
        org_object_id = ObjectId(org_id)

        logger.info(f"Getting dashboard summary for org {org_id}")

        # Get all deals for the organization
        deals = await Deal.find_many(query={"org_id": org_object_id})
        active_deals = len(deals)

        # Calculate deals change percentage (this month vs last month)
        # For now, return 0 - can be enhanced with actual historical data
        active_deals_change_pct = 0

        # Get forms count
        forms = await Form.find_many(query={"org_id": org_object_id, "is_active": True})
        # Only include forms that are not create by the system, i.e form.name!='Demo Form - Understanding Form Builder'
        forms_count = len([form for form in forms if form.name!='Demo Form - Understanding Form Builder'])

        # Get theses count
        theses = await InvestmentThesis.find_many(
            query={"org_id": org_object_id, "is_active": True, "is_deleted": False}
        )
        theses_count = len([thesis for thesis in theses if thesis.name!='Demo Investment Thesis - Understanding Form Builder'])
        # theses_count = len(theses)

        # AI Activity - dummy data for now
        ai_activity = {"active": False, "last_sync": None}

        # Sector distribution
        sector_distribution = []
        sector_counts = {}
        for deal in deals:
            if deal.sector:
                # Handle both string and list sectors
                sectors = (
                    deal.sector if isinstance(deal.sector, list) else [deal.sector]
                )
                for sector in sectors:
                    if sector:
                        sector_counts[sector] = sector_counts.get(sector, 0) + 1

        for sector, count in sector_counts.items():
            sector_distribution.append({
                "sector": sector.replace("_", " ").capitalize(),
                "count": count,
            })

        # Deal stages distribution
        deal_stages = []
        stage_counts = {}
        for deal in deals:
            if deal.stage:
                stage_counts[deal.stage.replace("_", " ").capitalize()] = (
                    stage_counts.get(deal.stage, 0) + 1
                )

        # Ensure common stages are included even if count is 0
        common_stages = [
            "Pre-Seed",
            "Seed",
            "Series A",
            "Series B",
            "Series C",
            "Growth",
        ]
        for stage in common_stages:
            if stage not in stage_counts:
                stage_counts[stage] = 0

        for stage, count in stage_counts.items():
            deal_stages.append({"stage": stage, "count": count})

        # Onboarding status
        has_form = forms_count > 0
        has_thesis = theses_count > 0

        # onboarding = {"has_form": has_form, "has_thesis": has_thesis}
        onboarding = {"has_form": has_form, "has_thesis": has_thesis}

        summary = DashboardSummaryResponse(
            active_deals=active_deals,
            active_deals_change_pct=active_deals_change_pct,
            forms=forms_count,
            theses=theses_count,
            ai_activity=ai_activity,  # type: ignore
            sector_distribution=sector_distribution,
            deal_stages=deal_stages,
            onboarding=onboarding,  # type: ignore
        )

        logger.info(
            f"Dashboard summary generated: {active_deals} deals, {forms_count} forms, {theses_count} theses"
        )
        return summary

    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
